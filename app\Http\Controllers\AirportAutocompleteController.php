<?php

namespace App\Http\Controllers;

use App\Services\AirportAutocompleteService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class AirportAutocompleteController extends Controller
{
    protected AirportAutocompleteService $autocompleteService;

    public function __construct(AirportAutocompleteService $autocompleteService)
    {
        $this->autocompleteService = $autocompleteService;
    }

    /**
     * Search airports for autocomplete
     */
    public function search(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'query' => 'required|string|min:1|max:50',
            'limit' => 'sometimes|integer|min:1|max:20'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid search parameters',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $query = $request->input('query');
            $limit = $request->input('limit', 10);

            $startTime = microtime(true);
            $results = $this->autocompleteService->searchAirports($query, $limit);
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);

            return response()->json([
                'success' => true,
                'data' => $results,
                'meta' => [
                    'query' => $query,
                    'count' => count($results),
                    'response_time' => $responseTime . 'ms',
                    'limit' => $limit
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Airport autocomplete search failed', [
                'query' => $request->input('query'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Search failed. Please try again.',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get airport details by code
     */
    public function getAirportDetails(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required|string|size:3'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid airport code',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $code = strtoupper($request->input('code'));
            $results = $this->autocompleteService->searchAirports($code, 1);

            if (empty($results)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Airport not found'
                ], 404);
            }

            $airport = $results[0];

            return response()->json([
                'success' => true,
                'data' => $airport
            ]);

        } catch (\Exception $e) {
            \Log::error('Airport details fetch failed', [
                'code' => $request->input('code'),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch airport details'
            ], 500);
        }
    }

    /**
     * Get popular airports
     */
    public function getPopularAirports(): JsonResponse
    {
        try {
            $popularCodes = ['JFK', 'LAX', 'LHR', 'CDG', 'DXB', 'NRT', 'SYD', 'SIN'];
            $results = [];

            foreach ($popularCodes as $code) {
                $airportResults = $this->autocompleteService->searchAirports($code, 1);
                if (!empty($airportResults)) {
                    $results[] = $airportResults[0];
                }
            }

            return response()->json([
                'success' => true,
                'data' => $results,
                'meta' => [
                    'count' => count($results),
                    'type' => 'popular_airports'
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Popular airports fetch failed', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch popular airports'
            ], 500);
        }
    }

    /**
     * Clear autocomplete cache
     */
    public function clearCache(): JsonResponse
    {
        try {
            $this->autocompleteService->clearCache();

            return response()->json([
                'success' => true,
                'message' => 'Airport autocomplete cache cleared successfully'
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to clear airport cache', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to clear cache'
            ], 500);
        }
    }

    /**
     * Get autocomplete statistics
     */
    public function getStats(): JsonResponse
    {
        try {
            $stats = $this->autocompleteService->getSearchStats();

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to get airport stats', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get statistics'
            ], 500);
        }
    }
}
