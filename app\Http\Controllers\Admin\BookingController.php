<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Flight;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class BookingController extends Controller
{
    /**
     * Display a listing of bookings with filtering and search
     */
    public function index(Request $request): View
    {
        $query = Booking::with(['flight.airline', 'flight.departureAirport', 'flight.arrivalAirport', 'passengers', 'user']);

        // Apply filters
        if ($request->filled('booking_status')) {
            $query->where('booking_status', $request->booking_status);
        }

        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        if ($request->filled('flight_type')) {
            $query->whereHas('flight', function ($q) use ($request) {
                $q->where('flight_type', $request->flight_type);
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('booking_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('booking_date', '<=', $request->date_to);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('booking_reference', 'like', "%{$search}%")
                  ->orWhere('contact_email', 'like', "%{$search}%")
                  ->orWhereHas('flight', function ($q) use ($search) {
                      $q->where('flight_number', 'like', "%{$search}%");
                  })
                  ->orWhereHas('passengers', function ($q) use ($search) {
                      $q->where('first_name', 'like', "%{$search}%")
                        ->orWhere('last_name', 'like', "%{$search}%");
                  });
            });
        }

        // Sort by booking date by default (newest first)
        $query->orderBy('booking_date', 'desc');

        // Handle export request
        if ($request->get('export') === 'csv') {
            return $this->exportBookings($query);
        }

        $bookings = $query->paginate(20)->withQueryString();

        // Get filter options
        $bookingStatuses = ['Pending', 'Confirmed', 'Cancelled', 'Completed'];
        $paymentStatuses = ['Pending', 'Paid', 'Failed', 'Refunded'];
        $flightTypes = ['Domestic', 'International'];

        return view('admin.bookings.index', compact(
            'bookings',
            'bookingStatuses',
            'paymentStatuses',
            'flightTypes'
        ));
    }

    /**
     * Display the specified booking
     */
    public function show(Booking $booking): View
    {
        $booking->load(['flight.airline', 'flight.departureAirport', 'flight.arrivalAirport', 'passengers', 'user']);

        return view('admin.bookings.show', compact('booking'));
    }

    /**
     * Show the form for editing the specified booking
     */
    public function edit(Booking $booking): View
    {
        $booking->load(['flight', 'passengers']);

        return view('admin.bookings.edit', compact('booking'));
    }

    /**
     * Update the specified booking
     */
    public function update(Request $request, Booking $booking): RedirectResponse
    {
        $validated = $request->validate([
            'booking_status' => 'required|in:Pending,Confirmed,Cancelled,Completed',
            'payment_status' => 'required|in:Pending,Paid,Failed,Refunded',
            'contact_email' => 'required|email',
            'contact_phone' => 'nullable|string',
            'special_requests' => 'nullable|string',
        ]);

        $booking->update($validated);

        return redirect()->route('admin.bookings.index')
            ->with('success', 'Booking updated successfully.');
    }

    /**
     * Cancel a booking
     */
    public function cancel(Booking $booking): RedirectResponse
    {
        if (!$booking->can_be_cancelled) {
            return redirect()->back()
                ->with('error', 'This booking cannot be cancelled.');
        }

        $booking->update([
            'booking_status' => 'Cancelled',
            'payment_status' => 'Refunded'
        ]);

        // Update flight available seats
        $booking->flight->increment('available_seats', $booking->passenger_count);

        return redirect()->route('admin.bookings.index')
            ->with('success', 'Booking cancelled successfully.');
    }

    /**
     * Check in passengers for a booking
     */
    public function checkIn(Booking $booking): RedirectResponse
    {
        if (!$booking->can_check_in) {
            return redirect()->back()
                ->with('error', 'Check-in is not available for this booking.');
        }

        $booking->update([
            'is_checked_in' => true,
            'check_in_time' => now()
        ]);

        return redirect()->route('admin.bookings.show', $booking)
            ->with('success', 'Passengers checked in successfully.');
    }

    /**
     * Remove the specified booking
     */
    public function destroy(Booking $booking): RedirectResponse
    {
        // Only allow deletion of cancelled bookings
        if ($booking->booking_status !== 'Cancelled') {
            return redirect()->route('admin.bookings.index')
                ->with('error', 'Only cancelled bookings can be deleted.');
        }

        $booking->delete();

        return redirect()->route('admin.bookings.index')
            ->with('success', 'Booking deleted successfully.');
    }

    /**
     * Export bookings to CSV
     */
    private function exportBookings($query)
    {
        $bookings = $query->with(['flight.airline', 'flight.departureAirport', 'flight.arrivalAirport', 'passengers', 'user'])->get();

        $filename = 'bookings_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($bookings) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Booking Reference',
                'Flight Number',
                'Airline',
                'Route',
                'Departure Time',
                'Passenger Count',
                'Primary Passenger',
                'Contact Email',
                'Contact Phone',
                'Total Amount',
                'Booking Status',
                'Payment Status',
                'Payment Method',
                'Booking Date',
                'Checked In',
                'Check-in Time'
            ]);

            // CSV data
            foreach ($bookings as $booking) {
                $primaryPassenger = $booking->passengers->first();

                fputcsv($file, [
                    $booking->booking_reference,
                    $booking->flight->flight_number,
                    $booking->flight->airline->name,
                    $booking->flight->departureAirport->code . ' → ' . $booking->flight->arrivalAirport->code,
                    $booking->flight->departure_time->format('Y-m-d H:i:s'),
                    $booking->passenger_count,
                    $primaryPassenger ? $primaryPassenger->full_name : '',
                    $booking->contact_email,
                    $booking->contact_phone,
                    $booking->total_amount,
                    $booking->booking_status,
                    $booking->payment_status,
                    $booking->payment_method,
                    $booking->booking_date->format('Y-m-d H:i:s'),
                    $booking->is_checked_in ? 'Yes' : 'No',
                    $booking->check_in_time ? $booking->check_in_time->format('Y-m-d H:i:s') : ''
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
