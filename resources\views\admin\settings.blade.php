@extends('layouts.admin')

@section('page-title', 'System Settings')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="page-title">System Settings</h1>
            <p class="page-subtitle">Configure system preferences and options</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-primary" onclick="saveAllSettings()">
                <i class="fas fa-save me-2"></i>Save All Changes
            </button>
            <button class="btn btn-outline-secondary" onclick="resetSettings()">
                <i class="fas fa-undo me-2"></i>Reset to Defaults
            </button>
            <button class="btn btn-outline-info" onclick="testFormData()">
                <i class="fas fa-bug me-2"></i>Test Form Data
            </button>
        </div>
    </div>

    <div class="row">
        <!-- Settings Navigation -->
        <div class="col-lg-3">
            <div class="card">
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <a href="#general" class="list-group-item list-group-item-action active" data-bs-toggle="pill">
                            <i class="fas fa-cog me-2"></i>General Settings
                        </a>
                        <a href="#booking" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                            <i class="fas fa-ticket-alt me-2"></i>Booking Settings
                        </a>
                        <a href="#email" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                            <i class="fas fa-envelope me-2"></i>Email Settings
                        </a>
                        <a href="#payment" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                            <i class="fas fa-credit-card me-2"></i>Payment Settings
                        </a>
                        <a href="#security" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                            <i class="fas fa-shield-alt me-2"></i>Security Settings
                        </a>
                        <a href="#maintenance" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                            <i class="fas fa-tools me-2"></i>Maintenance
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Content -->
        <div class="col-lg-9">
            <form id="settingsForm">
                @csrf
                <div class="tab-content">
                    <!-- General Settings -->
                    <div class="tab-pane fade show active" id="general">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-cog me-2"></i>General Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="site_name" class="form-label">Site Name</label>
                                        <input type="text" class="form-control" id="site_name" name="site_name" value="{{ $settings['general']['site_name'] }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="contact_phone" class="form-label">Contact Phone</label>
                                        <input type="tel" class="form-control" id="contact_phone" name="contact_phone" value="{{ $settings['general']['contact_phone'] }}">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="timezone" class="form-label">Default Timezone</label>
                                        <select class="form-select" id="timezone" name="timezone">
                                            <option value="UTC" {{ $settings['general']['timezone'] == 'UTC' ? 'selected' : '' }}>UTC</option>
                                            <option value="America/New_York" {{ $settings['general']['timezone'] == 'America/New_York' ? 'selected' : '' }}>Eastern Time</option>
                                            <option value="America/Chicago" {{ $settings['general']['timezone'] == 'America/Chicago' ? 'selected' : '' }}>Central Time</option>
                                            <option value="America/Denver" {{ $settings['general']['timezone'] == 'America/Denver' ? 'selected' : '' }}>Mountain Time</option>
                                            <option value="America/Los_Angeles" {{ $settings['general']['timezone'] == 'America/Los_Angeles' ? 'selected' : '' }}>Pacific Time</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="date_format" class="form-label">Date Format</label>
                                        <select class="form-select" id="date_format" name="date_format">
                                            <option value="Y-m-d" {{ $settings['general']['date_format'] == 'Y-m-d' ? 'selected' : '' }}>YYYY-MM-DD</option>
                                            <option value="m/d/Y" {{ $settings['general']['date_format'] == 'm/d/Y' ? 'selected' : '' }}>MM/DD/YYYY</option>
                                            <option value="d/m/Y" {{ $settings['general']['date_format'] == 'd/m/Y' ? 'selected' : '' }}>DD/MM/YYYY</option>
                                            <option value="F j, Y" {{ $settings['general']['date_format'] == 'F j, Y' ? 'selected' : '' }}>Month DD, YYYY</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="time_format" class="form-label">Time Format</label>
                                        <select class="form-select" id="time_format" name="time_format">
                                            <option value="H:i" {{ $settings['general']['time_format'] == 'H:i' ? 'selected' : '' }}>24-hour (HH:MM)</option>
                                            <option value="g:i A" {{ $settings['general']['time_format'] == 'g:i A' ? 'selected' : '' }}>12-hour (H:MM AM/PM)</option>
                                            <option value="h:i A" {{ $settings['general']['time_format'] == 'h:i A' ? 'selected' : '' }}>12-hour (HH:MM AM/PM)</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="site_description" class="form-label">Site Description</label>
                                    <textarea class="form-control" id="site_description" name="site_description" rows="3">{{ $settings['general']['site_description'] }}</textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="contact_email" class="form-label">Contact Email</label>
                                        <input type="email" class="form-control" id="contact_email" name="contact_email" value="{{ $settings['general']['contact_email'] }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="contactPhone" class="form-label">Contact Phone</label>
                                        <input type="tel" class="form-control" id="contactPhone" value="+****************">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Booking Settings -->
                    <div class="tab-pane fade" id="booking">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-ticket-alt me-2"></i>Booking Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="booking_window_days" class="form-label">Booking Window (days)</label>
                                        <input type="number" class="form-control" id="booking_window_days" name="booking_window_days"
                                               value="{{ $settings['booking']['booking_window_days'] }}" min="1" max="730">
                                        <div class="form-text">How many days in advance bookings can be made</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="max_passengers_per_booking" class="form-label">Max Passengers per Booking</label>
                                        <input type="number" class="form-control" id="max_passengers_per_booking" name="max_passengers_per_booking"
                                               value="{{ $settings['booking']['max_passengers_per_booking'] }}" min="1" max="20">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="cancellation_window_hours" class="form-label">Free Cancellation Window (hours)</label>
                                        <input type="number" class="form-control" id="cancellation_window_hours" name="cancellation_window_hours"
                                               value="{{ $settings['booking']['cancellation_window_hours'] }}" min="1" max="168">
                                        <div class="form-text">Hours before departure for free cancellation</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="payment_timeout_minutes" class="form-label">Payment Timeout (minutes)</label>
                                        <input type="number" class="form-control" id="payment_timeout_minutes" name="payment_timeout_minutes"
                                               value="{{ $settings['payment']['payment_timeout_minutes'] }}" min="5" max="60">
                                        <div class="form-text">Time limit for completing payment</div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="require_passport_info" name="require_passport_info"
                                               value="1" {{ $settings['booking']['require_passport_info'] ? 'checked' : '' }}>
                                        <label class="form-check-label" for="require_passport_info">
                                            Require passport information for international flights
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="auto_confirm_bookings" name="auto_confirm_bookings"
                                               value="1" {{ $settings['booking']['auto_confirm_bookings'] ? 'checked' : '' }}>
                                        <label class="form-check-label" for="auto_confirm_bookings">
                                            Auto-confirm bookings after payment
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="send_confirmation_email" name="send_confirmation_email"
                                               value="1" {{ $settings['booking']['send_confirmation_email'] ? 'checked' : '' }}>
                                        <label class="form-check-label" for="send_confirmation_email">
                                            Send booking confirmation emails
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Email Settings -->
                    <div class="tab-pane fade" id="email">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-envelope me-2"></i>Email Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="smtp_host" class="form-label">SMTP Host</label>
                                        <input type="text" class="form-control" id="smtp_host" name="smtp_host"
                                               value="{{ $settings['email']['smtp_host'] }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="smtp_port" class="form-label">SMTP Port</label>
                                        <input type="number" class="form-control" id="smtp_port" name="smtp_port"
                                               value="{{ $settings['email']['smtp_port'] }}">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="smtp_username" class="form-label">SMTP Username</label>
                                        <input type="text" class="form-control" id="smtp_username" name="smtp_username"
                                               value="{{ $settings['email']['smtp_username'] }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="smtp_password" class="form-label">SMTP Password</label>
                                        <input type="password" class="form-control" id="smtp_password" name="smtp_password"
                                               value="{{ $settings['email']['smtp_password'] }}" placeholder="Enter SMTP password">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="from_email" class="form-label">From Email</label>
                                        <input type="email" class="form-control" id="from_email" name="from_email"
                                               value="{{ $settings['email']['from_email'] }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="from_name" class="form-label">From Name</label>
                                        <input type="text" class="form-control" id="from_name" name="from_name"
                                               value="{{ $settings['email']['from_name'] }}">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="smtp_encryption" class="form-label">SMTP Encryption</label>
                                        <select class="form-select" id="smtp_encryption" name="smtp_encryption">
                                            <option value="tls" {{ $settings['email']['smtp_encryption'] == 'tls' ? 'selected' : '' }}>TLS</option>
                                            <option value="ssl" {{ $settings['email']['smtp_encryption'] == 'ssl' ? 'selected' : '' }}>SSL</option>
                                            <option value="none" {{ $settings['email']['smtp_encryption'] == 'none' ? 'selected' : '' }}>None</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <button type="button" class="btn btn-outline-primary">
                                        <i class="fas fa-paper-plane me-2"></i>Send Test Email
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Settings -->
                    <div class="tab-pane fade" id="payment">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Payment Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-4">
                                    <div class="col-md-6 mb-3">
                                        <label for="currency" class="form-label">Default Currency</label>
                                        <select class="form-select" id="currency" name="currency">
                                            <option value="USD" {{ $settings['payment']['currency'] == 'USD' ? 'selected' : '' }}>USD - US Dollar</option>
                                            <option value="EUR" {{ $settings['payment']['currency'] == 'EUR' ? 'selected' : '' }}>EUR - Euro</option>
                                            <option value="GBP" {{ $settings['payment']['currency'] == 'GBP' ? 'selected' : '' }}>GBP - British Pound</option>
                                            <option value="CAD" {{ $settings['payment']['currency'] == 'CAD' ? 'selected' : '' }}>CAD - Canadian Dollar</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="payment_methods" class="form-label">Accepted Payment Methods</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="credit_card" name="payment_methods[]" value="credit_card"
                                                   {{ in_array('credit_card', $settings['payment']['payment_methods']) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="credit_card">Credit/Debit Cards</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="paypal" name="payment_methods[]" value="paypal"
                                                   {{ in_array('paypal', $settings['payment']['payment_methods']) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="paypal">PayPal</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="bank_transfer" name="payment_methods[]" value="bank_transfer"
                                                   {{ in_array('bank_transfer', $settings['payment']['payment_methods']) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="bank_transfer">Bank Transfer</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <h6>Payment Gateways</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-body">
                                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                                        <div class="d-flex align-items-center">
                                                            <i class="fab fa-stripe fa-2x text-primary me-3"></i>
                                                            <div>
                                                                <div class="fw-bold">Stripe</div>
                                                                <small class="text-muted">Credit/Debit Cards</small>
                                                            </div>
                                                        </div>
                                                        <div class="form-check form-switch">
                                                            <input class="form-check-input" type="checkbox" id="stripeEnabled" checked>
                                                        </div>
                                                    </div>
                                                    <div class="mb-2">
                                                        <label class="form-label small">Publishable Key</label>
                                                        <input type="text" class="form-control form-control-sm" value="pk_test_••••••••">
                                                    </div>
                                                    <div class="mb-2">
                                                        <label class="form-label small">Secret Key</label>
                                                        <input type="password" class="form-control form-control-sm" value="sk_test_••••••••">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-body">
                                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                                        <div class="d-flex align-items-center">
                                                            <i class="fab fa-paypal fa-2x text-primary me-3"></i>
                                                            <div>
                                                                <div class="fw-bold">PayPal</div>
                                                                <small class="text-muted">PayPal Payments</small>
                                                            </div>
                                                        </div>
                                                        <div class="form-check form-switch">
                                                            <input class="form-check-input" type="checkbox" id="paypalEnabled">
                                                        </div>
                                                    </div>
                                                    <div class="mb-2">
                                                        <label class="form-label small">Client ID</label>
                                                        <input type="text" class="form-control form-control-sm" placeholder="Enter PayPal Client ID">
                                                    </div>
                                                    <div class="mb-2">
                                                        <label class="form-label small">Client Secret</label>
                                                        <input type="password" class="form-control form-control-sm" placeholder="Enter PayPal Client Secret">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="processingFee" class="form-label">Processing Fee (%)</label>
                                        <input type="number" step="0.01" class="form-control" id="processingFee" value="2.9">
                                        <div class="form-text">Additional fee charged for payment processing</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="refundPolicy" class="form-label">Refund Processing Time (days)</label>
                                        <input type="number" class="form-control" id="refundPolicy" value="5">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Security Settings -->
                    <div class="tab-pane fade" id="security">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Security Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="session_timeout_minutes" class="form-label">Session Timeout (minutes)</label>
                                        <input type="number" class="form-control" id="session_timeout_minutes" name="session_timeout_minutes"
                                               value="{{ $settings['security']['session_timeout_minutes'] }}" min="30" max="480">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="max_login_attempts" class="form-label">Max Login Attempts</label>
                                        <input type="number" class="form-control" id="max_login_attempts" name="max_login_attempts"
                                               value="{{ $settings['security']['max_login_attempts'] }}" min="3" max="10">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="lockout_duration_minutes" class="form-label">Lockout Duration (minutes)</label>
                                        <input type="number" class="form-control" id="lockout_duration_minutes" name="lockout_duration_minutes"
                                               value="{{ $settings['security']['lockout_duration_minutes'] }}" min="5" max="60">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="password_min_length" class="form-label">Minimum Password Length</label>
                                        <input type="number" class="form-control" id="password_min_length" name="password_min_length"
                                               value="{{ $settings['security']['password_min_length'] }}" min="6" max="20">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enable_2fa" name="enable_2fa"
                                               value="1" {{ $settings['security']['enable_2fa'] ? 'checked' : '' }}>
                                        <label class="form-check-label" for="enable_2fa">
                                            Enable Two-Factor Authentication
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="require_password_confirmation" name="require_password_confirmation"
                                               value="1" {{ $settings['security']['require_password_confirmation'] ? 'checked' : '' }}>
                                        <label class="form-check-label" for="require_password_confirmation">
                                            Require password confirmation for sensitive actions
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Maintenance -->
                    <div class="tab-pane fade" id="maintenance">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-tools me-2"></i>System Maintenance</h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-4">
                                    <div class="col-md-6 mb-3">
                                        <label for="backup_frequency" class="form-label">Backup Frequency</label>
                                        <select class="form-select" id="backup_frequency" name="backup_frequency">
                                            <option value="hourly" {{ $settings['maintenance']['backup_frequency'] == 'hourly' ? 'selected' : '' }}>Hourly</option>
                                            <option value="daily" {{ $settings['maintenance']['backup_frequency'] == 'daily' ? 'selected' : '' }}>Daily</option>
                                            <option value="weekly" {{ $settings['maintenance']['backup_frequency'] == 'weekly' ? 'selected' : '' }}>Weekly</option>
                                            <option value="monthly" {{ $settings['maintenance']['backup_frequency'] == 'monthly' ? 'selected' : '' }}>Monthly</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="log_retention_days" class="form-label">Log Retention (days)</label>
                                        <input type="number" class="form-control" id="log_retention_days" name="log_retention_days"
                                               value="{{ $settings['maintenance']['log_retention_days'] }}" min="7" max="365">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="maintenance_message" class="form-label">Maintenance Message</label>
                                    <textarea class="form-control" id="maintenance_message" name="maintenance_message" rows="3">{{ $settings['maintenance']['maintenance_message'] }}</textarea>
                                    <div class="form-text">Message displayed when maintenance mode is enabled</div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="maintenance_mode" name="maintenance_mode"
                                               value="1" {{ $settings['maintenance']['maintenance_mode'] ? 'checked' : '' }}>
                                        <label class="form-check-label" for="maintenance_mode">
                                            Enable Maintenance Mode
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enable_debug_mode" name="enable_debug_mode"
                                               value="1" {{ $settings['maintenance']['enable_debug_mode'] ? 'checked' : '' }}>
                                        <label class="form-check-label" for="enable_debug_mode">
                                            Enable Debug Mode
                                        </label>
                                    </div>
                                </div>
                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <i class="fas fa-database fa-3x text-primary mb-3"></i>
                                            <h6>Database Backup</h6>
                                            <p class="small text-muted">Create a backup of the database</p>
                                            <button class="btn btn-primary btn-sm">
                                                <i class="fas fa-download me-2"></i>Create Backup
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <i class="fas fa-broom fa-3x text-warning mb-3"></i>
                                            <h6>Clear Cache</h6>
                                            <p class="small text-muted">Clear application cache and optimize performance</p>
                                            <button class="btn btn-warning btn-sm">
                                                <i class="fas fa-trash me-2"></i>Clear Cache
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <i class="fas fa-file-alt fa-3x text-info mb-3"></i>
                                            <h6>System Logs</h6>
                                            <p class="small text-muted">View and download system logs</p>
                                            <button class="btn btn-info btn-sm">
                                                <i class="fas fa-eye me-2"></i>View Logs
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                                            <h6>Maintenance Mode</h6>
                                            <p class="small text-muted">Put the site in maintenance mode</p>
                                            <button class="btn btn-outline-danger btn-sm">
                                                <i class="fas fa-power-off me-2"></i>Enable Maintenance
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function saveAllSettings() {
    // Show loading state
    const saveButton = document.querySelector('button[onclick="saveAllSettings()"]');
    const originalText = saveButton.innerHTML;
    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
    saveButton.disabled = true;

    // Get the form and serialize data
    const form = document.getElementById('settingsForm');
    const formData = new FormData(form);

    // Handle checkboxes properly - ensure unchecked checkboxes send '0'
    const checkboxes = form.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        if (!checkbox.checked && checkbox.name) {
            formData.set(checkbox.name, '0');
        } else if (checkbox.checked && checkbox.name) {
            formData.set(checkbox.name, '1');
        }
    });

    // Handle payment methods array specially
    const paymentMethods = [];
    form.querySelectorAll('input[name="payment_methods[]"]:checked').forEach(checkbox => {
        paymentMethods.push(checkbox.value);
    });
    formData.delete('payment_methods[]');
    if (paymentMethods.length > 0) {
        paymentMethods.forEach(method => {
            formData.append('payment_methods[]', method);
        });
    }

    // Submit to server
    fetch('{{ route('admin.settings.update') }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('success', data.message);
        } else {
            showToast('error', data.message || 'Failed to save settings');
            if (data.errors) {
                console.error('Validation errors:', data.errors);
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('error', 'An error occurred while saving settings');
    })
    .finally(() => {
        // Restore button state
        saveButton.innerHTML = originalText;
        saveButton.disabled = false;
    });
}

function resetSettings() {
    if (!confirm('Are you sure you want to reset all settings to their default values? This action cannot be undone.')) {
        return;
    }

    const resetButton = document.querySelector('button[onclick="resetSettings()"]');
    const originalText = resetButton.innerHTML;
    resetButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Resetting...';
    resetButton.disabled = true;

    fetch('{{ route('admin.settings.reset') }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('success', data.message);
            // Reload page to show default values
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showToast('error', data.message || 'Failed to reset settings');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('error', 'An error occurred while resetting settings');
    })
    .finally(() => {
        resetButton.innerHTML = originalText;
        resetButton.disabled = false;
    });
}

function testFormData() {
    const form = document.getElementById('settingsForm');
    const formData = new FormData(form);

    console.log('=== FORM DATA TEST ===');
    for (let [key, value] of formData.entries()) {
        console.log(`${key}: ${value}`);
    }

    // Test payment methods array
    const paymentMethods = [];
    form.querySelectorAll('input[name="payment_methods[]"]:checked').forEach(checkbox => {
        paymentMethods.push(checkbox.value);
    });
    console.log('Payment methods array:', paymentMethods);

    alert('Form data logged to console. Check browser developer tools.');
}

function showToast(type, message) {
    const bgClass = type === 'success' ? 'bg-success' : 'bg-danger';
    const icon = type === 'success' ? 'fas fa-check' : 'fas fa-exclamation-triangle';

    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white ${bgClass} border-0 position-fixed top-0 end-0 m-3`;
    toast.style.zIndex = '9999';
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="${icon} me-2"></i>${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    document.body.appendChild(toast);

    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    setTimeout(() => {
        toast.remove();
    }, 5000);
}
</script>
@endpush
