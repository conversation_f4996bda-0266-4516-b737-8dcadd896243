<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\View\View;

class BookTicketsController extends Controller
{
    /**
     * Display the book tickets page
     */
    public function index(): View
    {
        return view('book-tickets');
    }

    /**
     * Get airport suggestions for autocomplete
     */
    public function searchAirports(Request $request)
    {
        $query = $request->get('q', '');
        
        if (strlen($query) < 1) {
            return response()->json([]);
        }

        // Sample airport data - in production, this would come from database
        $airports = [
            ['code' => 'ISB', 'name' => 'Islamabad International Airport', 'city' => 'Islamabad', 'country' => 'Pakistan'],
            ['code' => 'LHE', 'name' => 'Allama Iqbal International Airport', 'city' => 'Lahore', 'country' => 'Pakistan'],
            ['code' => 'KHI', 'name' => 'Jinnah International Airport', 'city' => 'Karachi', 'country' => 'Pakistan'],
            ['code' => 'PEW', 'name' => 'Peshawar International Airport', 'city' => 'Peshawar', 'country' => 'Pakistan'],
            ['code' => 'MUX', 'name' => 'Multan International Airport', 'city' => 'Multan', 'country' => 'Pakistan'],
            ['code' => 'BWP', 'name' => 'Bannu Airport', 'city' => 'Bannu', 'country' => 'Pakistan'],
            ['code' => 'BAH', 'name' => 'Bahrain International Airport', 'city' => 'Manama', 'country' => 'Bahrain'],
            ['code' => 'DXB', 'name' => 'Dubai International Airport', 'city' => 'Dubai', 'country' => 'UAE'],
            ['code' => 'SHJ', 'name' => 'Sharjah International Airport', 'city' => 'Sharjah', 'country' => 'UAE'],
            ['code' => 'AUH', 'name' => 'Abu Dhabi International Airport', 'city' => 'Abu Dhabi', 'country' => 'UAE'],
            ['code' => 'DOH', 'name' => 'Hamad International Airport', 'city' => 'Doha', 'country' => 'Qatar'],
            ['code' => 'KWI', 'name' => 'Kuwait International Airport', 'city' => 'Kuwait City', 'country' => 'Kuwait'],
            ['code' => 'RUH', 'name' => 'King Khalid International Airport', 'city' => 'Riyadh', 'country' => 'Saudi Arabia'],
            ['code' => 'JED', 'name' => 'King Abdulaziz International Airport', 'city' => 'Jeddah', 'country' => 'Saudi Arabia'],
            ['code' => 'DMM', 'name' => 'King Fahd International Airport', 'city' => 'Dammam', 'country' => 'Saudi Arabia'],
            ['code' => 'MCT', 'name' => 'Muscat International Airport', 'city' => 'Muscat', 'country' => 'Oman'],
            ['code' => 'LHR', 'name' => 'London Heathrow Airport', 'city' => 'London', 'country' => 'United Kingdom'],
            ['code' => 'LGW', 'name' => 'London Gatwick Airport', 'city' => 'London', 'country' => 'United Kingdom'],
            ['code' => 'MAN', 'name' => 'Manchester Airport', 'city' => 'Manchester', 'country' => 'United Kingdom'],
            ['code' => 'CDG', 'name' => 'Charles de Gaulle Airport', 'city' => 'Paris', 'country' => 'France'],
            ['code' => 'FRA', 'name' => 'Frankfurt Airport', 'city' => 'Frankfurt', 'country' => 'Germany'],
            ['code' => 'AMS', 'name' => 'Amsterdam Airport Schiphol', 'city' => 'Amsterdam', 'country' => 'Netherlands'],
            ['code' => 'IST', 'name' => 'Istanbul Airport', 'city' => 'Istanbul', 'country' => 'Turkey'],
            ['code' => 'JFK', 'name' => 'John F. Kennedy International Airport', 'city' => 'New York', 'country' => 'USA'],
            ['code' => 'LAX', 'name' => 'Los Angeles International Airport', 'city' => 'Los Angeles', 'country' => 'USA'],
            ['code' => 'ORD', 'name' => 'O\'Hare International Airport', 'city' => 'Chicago', 'country' => 'USA'],
            ['code' => 'YYZ', 'name' => 'Toronto Pearson International Airport', 'city' => 'Toronto', 'country' => 'Canada'],
            ['code' => 'YVR', 'name' => 'Vancouver International Airport', 'city' => 'Vancouver', 'country' => 'Canada'],
            ['code' => 'SYD', 'name' => 'Sydney Kingsford Smith Airport', 'city' => 'Sydney', 'country' => 'Australia'],
            ['code' => 'MEL', 'name' => 'Melbourne Airport', 'city' => 'Melbourne', 'country' => 'Australia'],
            ['code' => 'NRT', 'name' => 'Narita International Airport', 'city' => 'Tokyo', 'country' => 'Japan'],
            ['code' => 'ICN', 'name' => 'Incheon International Airport', 'city' => 'Seoul', 'country' => 'South Korea'],
            ['code' => 'SIN', 'name' => 'Singapore Changi Airport', 'city' => 'Singapore', 'country' => 'Singapore'],
            ['code' => 'BKK', 'name' => 'Suvarnabhumi Airport', 'city' => 'Bangkok', 'country' => 'Thailand'],
            ['code' => 'KUL', 'name' => 'Kuala Lumpur International Airport', 'city' => 'Kuala Lumpur', 'country' => 'Malaysia'],
            ['code' => 'CGK', 'name' => 'Soekarno-Hatta International Airport', 'city' => 'Jakarta', 'country' => 'Indonesia'],
            ['code' => 'DEL', 'name' => 'Indira Gandhi International Airport', 'city' => 'New Delhi', 'country' => 'India'],
            ['code' => 'BOM', 'name' => 'Chhatrapati Shivaji Maharaj International Airport', 'city' => 'Mumbai', 'country' => 'India'],
            ['code' => 'BLR', 'name' => 'Kempegowda International Airport', 'city' => 'Bangalore', 'country' => 'India'],
            ['code' => 'MAA', 'name' => 'Chennai International Airport', 'city' => 'Chennai', 'country' => 'India'],
            ['code' => 'CCU', 'name' => 'Netaji Subhas Chandra Bose International Airport', 'city' => 'Kolkata', 'country' => 'India'],
            ['code' => 'HYD', 'name' => 'Rajiv Gandhi International Airport', 'city' => 'Hyderabad', 'country' => 'India'],
            ['code' => 'AMD', 'name' => 'Sardar Vallabhbhai Patel International Airport', 'city' => 'Ahmedabad', 'country' => 'India'],
            ['code' => 'COK', 'name' => 'Cochin International Airport', 'city' => 'Kochi', 'country' => 'India'],
            ['code' => 'GOI', 'name' => 'Goa International Airport', 'city' => 'Goa', 'country' => 'India'],
            ['code' => 'DAC', 'name' => 'Hazrat Shahjalal International Airport', 'city' => 'Dhaka', 'country' => 'Bangladesh'],
            ['code' => 'CGP', 'name' => 'Shah Amanat International Airport', 'city' => 'Chittagong', 'country' => 'Bangladesh'],
            ['code' => 'CMB', 'name' => 'Bandaranaike International Airport', 'city' => 'Colombo', 'country' => 'Sri Lanka'],
            ['code' => 'KTM', 'name' => 'Tribhuvan International Airport', 'city' => 'Kathmandu', 'country' => 'Nepal'],
            ['code' => 'TBS', 'name' => 'Tbilisi International Airport', 'city' => 'Tbilisi', 'country' => 'Georgia'],
            ['code' => 'EVN', 'name' => 'Zvartnots International Airport', 'city' => 'Yerevan', 'country' => 'Armenia'],
            ['code' => 'GYD', 'name' => 'Heydar Aliyev International Airport', 'city' => 'Baku', 'country' => 'Azerbaijan']
        ];

        $query = strtolower($query);
        $matches = array_filter($airports, function($airport) use ($query) {
            return strpos(strtolower($airport['code']), $query) !== false ||
                   strpos(strtolower($airport['name']), $query) !== false ||
                   strpos(strtolower($airport['city']), $query) !== false ||
                   strpos(strtolower($airport['country']), $query) !== false;
        });

        // Limit to 10 results
        $matches = array_slice($matches, 0, 10);

        return response()->json(array_values($matches));
    }
}
