<?php

namespace App\Http\Controllers;

use App\Services\FlightApiService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class FlightSearchController extends Controller
{
    protected FlightApiService $flightApiService;

    public function __construct(FlightApiService $flightApiService)
    {
        $this->flightApiService = $flightApiService;
    }

    /**
     * Display the flight search page
     */
    public function index(): View
    {
        return view('flight-search.index');
    }

    /**
     * Search for flights using external APIs
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'departure_airport' => 'required|string|size:3',
                'arrival_airport' => 'required|string|size:3',
                'departure_date' => 'required|date|after_or_equal:today',
                'return_date' => 'nullable|date|after:departure_date',
                'adults' => 'required|integer|min:1|max:9',
                'children' => 'nullable|integer|min:0|max:8',
                'infants' => 'nullable|integer|min:0|max:2',
                'cabin_class' => 'required|in:economy,premium_economy,business,first',
                'max_results' => 'nullable|integer|min:1|max:50',
                'max_price' => 'nullable|numeric|min:0',
                'direct_flights_only' => 'nullable|boolean',
                'preferred_airlines' => 'nullable|array',
                'preferred_airlines.*' => 'string',
            ]);

            // Prepare search parameters
            $searchParams = [
                'departure_airport' => strtoupper($validated['departure_airport']),
                'arrival_airport' => strtoupper($validated['arrival_airport']),
                'departure_date' => $validated['departure_date'],
                'return_date' => $validated['return_date'] ?? null,
                'adults' => $validated['adults'],
                'children' => $validated['children'] ?? 0,
                'infants' => $validated['infants'] ?? 0,
                'cabin_class' => $validated['cabin_class'],
                'max_results' => $validated['max_results'] ?? 20,
                'max_price' => $validated['max_price'] ?? null,
                'direct_flights_only' => $validated['direct_flights_only'] ?? false,
                'preferred_airlines' => $validated['preferred_airlines'] ?? [],
            ];

            // Search flights using the API service
            $flights = $this->flightApiService->searchFlights($searchParams);

            // Filter results if needed
            if (!empty($searchParams['max_price'])) {
                $flights = array_filter($flights, function ($flight) use ($searchParams) {
                    return $flight['price'] <= $searchParams['max_price'];
                });
            }

            if ($searchParams['direct_flights_only']) {
                $flights = array_filter($flights, function ($flight) {
                    return $flight['stops'] === 0;
                });
            }

            // Sort by price (ascending)
            usort($flights, function ($a, $b) {
                return $a['price'] <=> $b['price'];
            });

            // Limit results
            $flights = array_slice($flights, 0, $searchParams['max_results']);

            return response()->json([
                'success' => true,
                'data' => $flights,
                'count' => count($flights),
                'search_params' => $searchParams,
                'message' => count($flights) > 0 ? 'Flights found successfully' : 'No flights found for your search criteria'
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid search parameters',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            Log::error('Flight search error: ' . $e->getMessage(), [
                'request' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while searching for flights. Please try again.',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Get flight details by ID
     */
    public function getFlightDetails(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'flight_id' => 'required|string',
                'provider' => 'required|string'
            ]);

            $details = $this->flightApiService->getFlightDetails(
                $validated['flight_id'],
                $validated['provider']
            );

            if (!$details) {
                return response()->json([
                    'success' => false,
                    'message' => 'Flight details not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $details
            ]);

        } catch (\Exception $e) {
            Log::error('Flight details error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Unable to fetch flight details'
            ], 500);
        }
    }

    /**
     * Search airports for autocomplete
     */
    public function searchAirports(Request $request): JsonResponse
    {
        try {
            $query = $request->get('query', '');
            
            if (strlen($query) < 2) {
                return response()->json([
                    'success' => true,
                    'data' => []
                ]);
            }

            // Cache airport search results
            $cacheKey = 'airport_search_' . md5(strtolower($query));
            
            $airports = Cache::remember($cacheKey, 3600, function () use ($query) {
                return $this->flightApiService->searchAirports($query);
            });

            return response()->json([
                'success' => true,
                'data' => $airports
            ]);

        } catch (\Exception $e) {
            Log::error('Airport search error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Unable to search airports',
                'data' => []
            ]);
        }
    }

    /**
     * Get popular destinations
     */
    public function getPopularDestinations(): JsonResponse
    {
        try {
            $popular = Cache::remember('popular_destinations', 86400, function () {
                // Return some popular destinations
                return [
                    ['code' => 'NYC', 'name' => 'New York', 'city' => 'New York', 'country' => 'United States'],
                    ['code' => 'LON', 'name' => 'London', 'city' => 'London', 'country' => 'United Kingdom'],
                    ['code' => 'PAR', 'name' => 'Paris', 'city' => 'Paris', 'country' => 'France'],
                    ['code' => 'TOK', 'name' => 'Tokyo', 'city' => 'Tokyo', 'country' => 'Japan'],
                    ['code' => 'DXB', 'name' => 'Dubai', 'city' => 'Dubai', 'country' => 'United Arab Emirates'],
                    ['code' => 'SIN', 'name' => 'Singapore', 'city' => 'Singapore', 'country' => 'Singapore'],
                    ['code' => 'SYD', 'name' => 'Sydney', 'city' => 'Sydney', 'country' => 'Australia'],
                    ['code' => 'LAX', 'name' => 'Los Angeles', 'city' => 'Los Angeles', 'country' => 'United States'],
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $popular
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'data' => []
            ]);
        }
    }
}
