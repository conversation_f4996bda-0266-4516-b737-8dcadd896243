@extends('layouts.admin')

@section('page-title', 'Flight Details')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="page-title">Flight Details</h1>
            <p class="page-subtitle">{{ $flight->flight_number }} - {{ $flight->airline->name }}</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('admin.flights.edit', $flight) }}" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>Edit Flight
            </a>
            <a href="{{ route('admin.flights.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Flights
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Flight Information -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-plane me-2"></i>Flight Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Flight Number:</td>
                                    <td>{{ $flight->flight_number }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Airline:</td>
                                    <td>{{ $flight->airline->name }} ({{ $flight->airline->code }})</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Aircraft Type:</td>
                                    <td>{{ $flight->aircraft_type }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Flight Type:</td>
                                    <td><span class="badge bg-info">{{ $flight->flight_type }}</span></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Class Type:</td>
                                    <td><span class="badge bg-secondary">{{ $flight->class_type }}</span></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Status:</td>
                                    <td>
                                        @php
                                            $statusClass = match($flight->status) {
                                                'Scheduled' => 'bg-primary',
                                                'Delayed' => 'bg-warning',
                                                'Cancelled' => 'bg-danger',
                                                'Completed' => 'bg-success',
                                                default => 'bg-secondary'
                                            };
                                        @endphp
                                        <span class="badge {{ $statusClass }}">{{ $flight->status }}</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Gate:</td>
                                    <td>{{ $flight->gate ?? 'Not assigned' }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Total Seats:</td>
                                    <td>{{ $flight->total_seats }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Available Seats:</td>
                                    <td>{{ $flight->available_seats }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Booked Seats:</td>
                                    <td>{{ $flight->total_seats - $flight->available_seats }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Base Price:</td>
                                    <td class="fw-bold text-success">${{ number_format($flight->base_price, 2) }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Duration:</td>
                                    <td>{{ floor($flight->duration_minutes / 60) }}h {{ $flight->duration_minutes % 60 }}m</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Route Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-route me-2"></i>Route Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-5">
                            <div class="text-center">
                                <h6 class="fw-bold">Departure</h6>
                                <div class="display-6 fw-bold text-primary">{{ $flight->departureAirport->code }}</div>
                                <div class="fw-bold">{{ $flight->departureAirport->name }}</div>
                                <div class="text-muted">{{ $flight->departureAirport->city }}, {{ $flight->departureAirport->country }}</div>
                                <div class="mt-3">
                                    <div class="fw-bold">{{ $flight->departure_time->format('M d, Y') }}</div>
                                    <div class="h5 text-primary">{{ $flight->departure_time->format('H:i') }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 d-flex align-items-center justify-content-center">
                            <div class="text-center">
                                <i class="fas fa-plane fa-2x text-muted"></i>
                                <div class="small text-muted mt-2">{{ floor($flight->duration_minutes / 60) }}h {{ $flight->duration_minutes % 60 }}m</div>
                            </div>
                        </div>
                        <div class="col-md-5">
                            <div class="text-center">
                                <h6 class="fw-bold">Arrival</h6>
                                <div class="display-6 fw-bold text-success">{{ $flight->arrivalAirport->code }}</div>
                                <div class="fw-bold">{{ $flight->arrivalAirport->name }}</div>
                                <div class="text-muted">{{ $flight->arrivalAirport->city }}, {{ $flight->arrivalAirport->country }}</div>
                                <div class="mt-3">
                                    <div class="fw-bold">{{ $flight->arrival_time->format('M d, Y') }}</div>
                                    <div class="h5 text-success">{{ $flight->arrival_time->format('H:i') }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bookings -->
            @if($flight->bookings->count() > 0)
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-ticket-alt me-2"></i>Bookings ({{ $flight->bookings->count() }})</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Booking Ref</th>
                                    <th>Passenger(s)</th>
                                    <th>Contact</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($flight->bookings as $booking)
                                <tr>
                                    <td>
                                        <div class="fw-bold">{{ $booking->booking_reference }}</div>
                                        <small class="text-muted">{{ $booking->booking_date->format('M d, Y') }}</small>
                                    </td>
                                    <td>
                                        <div>{{ $booking->passenger_count }} passenger(s)</div>
                                        @if($booking->passengers->count() > 0)
                                            <small class="text-muted">{{ $booking->passengers->first()->full_name }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <div>{{ $booking->contact_email }}</div>
                                        @if($booking->contact_phone)
                                            <small class="text-muted">{{ $booking->contact_phone }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="fw-bold">${{ number_format($booking->total_amount, 2) }}</span>
                                    </td>
                                    <td>
                                        @php
                                            $statusClass = match($booking->booking_status) {
                                                'Pending' => 'bg-warning',
                                                'Confirmed' => 'bg-success',
                                                'Cancelled' => 'bg-danger',
                                                'Completed' => 'bg-info',
                                                default => 'bg-secondary'
                                            };
                                        @endphp
                                        <span class="badge {{ $statusClass }}">{{ $booking->booking_status }}</span>
                                    </td>
                                    <td>
                                        <a href="{{ route('admin.bookings.show', $booking) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Statistics -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Flight Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="h4 fw-bold text-primary">{{ $flight->bookings->count() }}</div>
                            <div class="small text-muted">Total Bookings</div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="h4 fw-bold text-success">${{ number_format($flight->bookings->sum('total_amount'), 2) }}</div>
                            <div class="small text-muted">Total Revenue</div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="h4 fw-bold text-info">{{ $flight->bookings->sum('passenger_count') }}</div>
                            <div class="small text-muted">Total Passengers</div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="h4 fw-bold text-warning">{{ number_format(($flight->total_seats - $flight->available_seats) / $flight->total_seats * 100, 1) }}%</div>
                            <div class="small text-muted">Occupancy Rate</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cog me-2"></i>Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.flights.edit', $flight) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>Edit Flight
                        </a>
                        @if($flight->bookings->count() == 0)
                        <form action="{{ route('admin.flights.destroy', $flight) }}" method="POST" class="d-inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger w-100" onclick="return confirm('Are you sure you want to delete this flight?')">
                                <i class="fas fa-trash me-2"></i>Delete Flight
                            </button>
                        </form>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
