@extends('layouts.frontend')

@section('title', 'Book Tickets - {{ config("app.name", "FlyNow Airlines") }}')

@section('content')
<div class="container-fluid py-5" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh;">
    <div class="container">
        <!-- Header -->
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h1 class="text-white mb-3">
                    <i class="fas fa-plane me-3"></i>Book Your Flight
                </h1>
                <p class="text-white-50 lead">Find and book the perfect flight for your journey</p>
            </div>
        </div>

        <!-- Search Form -->
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card shadow-lg border-0" style="border-radius: 20px;">
                    <div class="card-body p-4">
                        <form id="flight-search-form" action="{{ route('flight-search.results') }}" method="GET">
                            <!-- Trip Type Selection -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <div class="btn-group w-100" role="group" aria-label="Trip type">
                                        <input type="radio" class="btn-check" name="trip_type" id="one_way" value="one_way" checked>
                                        <label class="btn btn-outline-primary" for="one_way">
                                            <i class="fas fa-arrow-right me-2"></i>One Way
                                        </label>
                                        
                                        <input type="radio" class="btn-check" name="trip_type" id="round_trip" value="round_trip">
                                        <label class="btn btn-outline-primary" for="round_trip">
                                            <i class="fas fa-exchange-alt me-2"></i>Round Trip
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Airport Selection -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-plane-departure me-2 text-primary"></i>From
                                    </label>
                                    <div class="position-relative">
                                        <input type="text" 
                                               class="form-control form-control-lg airport-search" 
                                               id="departure_airport" 
                                               name="departure_airport" 
                                               placeholder="Enter city or airport code"
                                               autocomplete="off"
                                               required>
                                        <div class="airport-suggestions" id="departure_suggestions"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-plane-arrival me-2 text-primary"></i>To
                                    </label>
                                    <div class="position-relative">
                                        <input type="text" 
                                               class="form-control form-control-lg airport-search" 
                                               id="arrival_airport" 
                                               name="arrival_airport" 
                                               placeholder="Enter city or airport code"
                                               autocomplete="off"
                                               required>
                                        <div class="airport-suggestions" id="arrival_suggestions"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Date Selection -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-calendar-alt me-2 text-primary"></i>Departure Date
                                    </label>
                                    <input type="date" 
                                           class="form-control form-control-lg" 
                                           id="departure_date" 
                                           name="departure_date" 
                                           min="{{ date('Y-m-d') }}"
                                           required>
                                </div>
                                <div class="col-md-6" id="return_date_container" style="display: none;">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-calendar-alt me-2 text-primary"></i>Return Date
                                    </label>
                                    <input type="date" 
                                           class="form-control form-control-lg" 
                                           id="return_date" 
                                           name="return_date" 
                                           min="{{ date('Y-m-d') }}">
                                </div>
                            </div>

                            <!-- Passengers and Class -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-users me-2 text-primary"></i>Passengers
                                    </label>
                                    <div class="dropdown">
                                        <button class="btn btn-outline-secondary btn-lg w-100 dropdown-toggle" 
                                                type="button" 
                                                id="passengerDropdown" 
                                                data-bs-toggle="dropdown" 
                                                aria-expanded="false">
                                            <span id="passenger-summary">1 Adult</span>
                                        </button>
                                        <div class="dropdown-menu p-3" style="min-width: 300px;">
                                            <div class="row mb-3">
                                                <div class="col-6">
                                                    <label class="form-label">Adults (12+)</label>
                                                </div>
                                                <div class="col-6">
                                                    <div class="input-group">
                                                        <button class="btn btn-outline-secondary" type="button" onclick="changePassengerCount('adults', -1)">-</button>
                                                        <input type="number" class="form-control text-center" id="adults" name="adults" value="1" min="1" max="9" readonly>
                                                        <button class="btn btn-outline-secondary" type="button" onclick="changePassengerCount('adults', 1)">+</button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row mb-3">
                                                <div class="col-6">
                                                    <label class="form-label">Children (2-11)</label>
                                                </div>
                                                <div class="col-6">
                                                    <div class="input-group">
                                                        <button class="btn btn-outline-secondary" type="button" onclick="changePassengerCount('children', -1)">-</button>
                                                        <input type="number" class="form-control text-center" id="children" name="children" value="0" min="0" max="9" readonly>
                                                        <button class="btn btn-outline-secondary" type="button" onclick="changePassengerCount('children', 1)">+</button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-6">
                                                    <label class="form-label">Infants (0-2)</label>
                                                </div>
                                                <div class="col-6">
                                                    <div class="input-group">
                                                        <button class="btn btn-outline-secondary" type="button" onclick="changePassengerCount('infants', -1)">-</button>
                                                        <input type="number" class="form-control text-center" id="infants" name="infants" value="0" min="0" max="9" readonly>
                                                        <button class="btn btn-outline-secondary" type="button" onclick="changePassengerCount('infants', 1)">+</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-chair me-2 text-primary"></i>Cabin Class
                                    </label>
                                    <select class="form-select form-select-lg" name="cabin_class" id="cabin_class">
                                        <option value="economy">Economy</option>
                                        <option value="premium_economy">Premium Economy</option>
                                        <option value="business">Business</option>
                                        <option value="first">First Class</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Search Button -->
                            <div class="row">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary btn-lg w-100 py-3" style="border-radius: 15px;">
                                        <i class="fas fa-search me-2"></i>Search Flights
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Popular Destinations -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="text-center mb-4">
                    <h3 class="text-white">Popular Destinations</h3>
                    <p class="text-white-50">Discover amazing places around the world</p>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="card border-0 shadow-sm h-100" style="border-radius: 15px;">
                            <div class="card-body text-center">
                                <h5 class="card-title">Islamabad → Bahrain</h5>
                                <p class="text-muted">From PKR 84,000</p>
                                <button class="btn btn-outline-primary btn-sm" onclick="setRoute('ISB', 'BAH')">Select</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card border-0 shadow-sm h-100" style="border-radius: 15px;">
                            <div class="card-body text-center">
                                <h5 class="card-title">Lahore → Dubai</h5>
                                <p class="text-muted">From PKR 75,000</p>
                                <button class="btn btn-outline-primary btn-sm" onclick="setRoute('LHE', 'DXB')">Select</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card border-0 shadow-sm h-100" style="border-radius: 15px;">
                            <div class="card-body text-center">
                                <h5 class="card-title">Karachi → Sharjah</h5>
                                <p class="text-muted">From PKR 70,000</p>
                                <button class="btn btn-outline-primary btn-sm" onclick="setRoute('KHI', 'SHJ')">Select</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card border-0 shadow-sm h-100" style="border-radius: 15px;">
                            <div class="card-body text-center">
                                <h5 class="card-title">Islamabad → Sharjah</h5>
                                <p class="text-muted">From PKR 78,000</p>
                                <button class="btn btn-outline-primary btn-sm" onclick="setRoute('ISB', 'SHJ')">Select</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.airport-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.airport-suggestion {
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
}

.airport-suggestion:hover,
.airport-suggestion.highlighted {
    background-color: #f8f9fa;
}

.airport-suggestion:last-child {
    border-bottom: none;
}

.airport-code {
    font-weight: bold;
    color: #007bff;
}

.airport-name {
    color: #333;
    margin-left: 8px;
}

.airport-city {
    color: #666;
    font-size: 0.9em;
}
</style>
@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Trip type toggle
    const oneWayRadio = document.getElementById('one_way');
    const roundTripRadio = document.getElementById('round_trip');
    const returnDateContainer = document.getElementById('return_date_container');
    const returnDateInput = document.getElementById('return_date');

    function toggleReturnDate() {
        if (roundTripRadio.checked) {
            returnDateContainer.style.display = 'block';
            returnDateInput.required = true;
        } else {
            returnDateContainer.style.display = 'none';
            returnDateInput.required = false;
            returnDateInput.value = '';
        }
    }

    oneWayRadio.addEventListener('change', toggleReturnDate);
    roundTripRadio.addEventListener('change', toggleReturnDate);

    // Set minimum date for return date based on departure date
    const departureDateInput = document.getElementById('departure_date');
    departureDateInput.addEventListener('change', function() {
        returnDateInput.min = this.value;
        if (returnDateInput.value && returnDateInput.value < this.value) {
            returnDateInput.value = this.value;
        }
    });

    // Set default departure date to tomorrow
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    departureDateInput.value = tomorrow.toISOString().split('T')[0];

    // Airport autocomplete functionality with AJAX
    let searchTimeout;

    function setupAirportSearch(inputId, suggestionsId) {
        const input = document.getElementById(inputId);
        const suggestions = document.getElementById(suggestionsId);

        input.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();

            if (query.length < 1) {
                suggestions.style.display = 'none';
                return;
            }

            searchTimeout = setTimeout(() => {
                // Show loading indicator
                suggestions.innerHTML = '<div class="airport-suggestion"><i class="fas fa-spinner fa-spin me-2"></i>Searching airports...</div>';
                suggestions.style.display = 'block';

                // Make AJAX request
                fetch(`{{ route("airports.search") }}?q=${encodeURIComponent(query)}`)
                    .then(response => response.json())
                    .then(airports => {
                        if (airports.length > 0) {
                            suggestions.innerHTML = airports.map(airport => `
                                <div class="airport-suggestion" onclick="selectAirport('${inputId}', '${airport.code}', '${airport.name}', '${airport.city}')">
                                    <span class="airport-code">${airport.code}</span>
                                    <span class="airport-name">${airport.name}</span>
                                    <div class="airport-city">${airport.city}, ${airport.country}</div>
                                </div>
                            `).join('');
                            suggestions.style.display = 'block';
                        } else {
                            suggestions.innerHTML = '<div class="airport-suggestion text-muted">No airports found</div>';
                            suggestions.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        console.error('Airport search error:', error);
                        suggestions.innerHTML = '<div class="airport-suggestion text-danger">Error searching airports</div>';
                        suggestions.style.display = 'block';
                    });
            }, 300);
        });

        // Hide suggestions when clicking outside
        document.addEventListener('click', function(e) {
            if (!input.contains(e.target) && !suggestions.contains(e.target)) {
                suggestions.style.display = 'none';
            }
        });

        // Handle keyboard navigation
        input.addEventListener('keydown', function(e) {
            const suggestionItems = suggestions.querySelectorAll('.airport-suggestion');
            let currentIndex = -1;

            // Find currently highlighted item
            suggestionItems.forEach((item, index) => {
                if (item.classList.contains('highlighted')) {
                    currentIndex = index;
                }
            });

            if (e.key === 'ArrowDown') {
                e.preventDefault();
                currentIndex = Math.min(currentIndex + 1, suggestionItems.length - 1);
                highlightSuggestion(suggestionItems, currentIndex);
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                currentIndex = Math.max(currentIndex - 1, 0);
                highlightSuggestion(suggestionItems, currentIndex);
            } else if (e.key === 'Enter' && currentIndex >= 0) {
                e.preventDefault();
                suggestionItems[currentIndex].click();
            } else if (e.key === 'Escape') {
                suggestions.style.display = 'none';
            }
        });
    }

    function highlightSuggestion(items, index) {
        items.forEach((item, i) => {
            if (i === index) {
                item.classList.add('highlighted');
            } else {
                item.classList.remove('highlighted');
            }
        });
    }

    setupAirportSearch('departure_airport', 'departure_suggestions');
    setupAirportSearch('arrival_airport', 'arrival_suggestions');
});

function selectAirport(inputId, code, name, city) {
    const input = document.getElementById(inputId);
    const suggestions = document.getElementById(inputId.replace('_airport', '_suggestions'));

    input.value = `${code} - ${city}`;
    input.dataset.airportCode = code;
    suggestions.style.display = 'none';
}

function changePassengerCount(type, change) {
    const input = document.getElementById(type);
    const currentValue = parseInt(input.value);
    const newValue = Math.max(parseInt(input.min), Math.min(parseInt(input.max), currentValue + change));

    // Special validation for infants (cannot exceed adults)
    if (type === 'infants') {
        const adults = parseInt(document.getElementById('adults').value);
        if (newValue > adults) {
            return;
        }
    }

    input.value = newValue;
    updatePassengerSummary();
}

function updatePassengerSummary() {
    const adults = parseInt(document.getElementById('adults').value);
    const children = parseInt(document.getElementById('children').value);
    const infants = parseInt(document.getElementById('infants').value);

    let summary = [];
    if (adults > 0) summary.push(`${adults} Adult${adults > 1 ? 's' : ''}`);
    if (children > 0) summary.push(`${children} Child${children > 1 ? 'ren' : ''}`);
    if (infants > 0) summary.push(`${infants} Infant${infants > 1 ? 's' : ''}`);

    document.getElementById('passenger-summary').textContent = summary.join(', ');
}

function setRoute(departure, arrival) {
    const departureInput = document.getElementById('departure_airport');
    const arrivalInput = document.getElementById('arrival_airport');

    // Find airport details
    const airports = [
        { code: 'ISB', city: 'Islamabad' },
        { code: 'LHE', city: 'Lahore' },
        { code: 'KHI', city: 'Karachi' },
        { code: 'BAH', city: 'Manama' },
        { code: 'DXB', city: 'Dubai' },
        { code: 'SHJ', city: 'Sharjah' }
    ];

    const depAirport = airports.find(a => a.code === departure);
    const arrAirport = airports.find(a => a.code === arrival);

    if (depAirport && arrAirport) {
        departureInput.value = `${departure} - ${depAirport.city}`;
        departureInput.dataset.airportCode = departure;
        arrivalInput.value = `${arrival} - ${arrAirport.city}`;
        arrivalInput.dataset.airportCode = arrival;
    }
}

// Form submission handling
document.getElementById('flight-search-form').addEventListener('submit', function(e) {
    const departureInput = document.getElementById('departure_airport');
    const arrivalInput = document.getElementById('arrival_airport');

    // Extract airport codes from the input values
    const departureCode = departureInput.dataset.airportCode || departureInput.value.split(' - ')[0];
    const arrivalCode = arrivalInput.dataset.airportCode || arrivalInput.value.split(' - ')[0];

    // Update form values with airport codes
    departureInput.value = departureCode;
    arrivalInput.value = arrivalCode;

    // Calculate total passengers
    const adults = parseInt(document.getElementById('adults').value);
    const children = parseInt(document.getElementById('children').value);
    const infants = parseInt(document.getElementById('infants').value);
    const totalPassengers = adults + children + infants;

    // Add hidden input for total passengers
    const passengersInput = document.createElement('input');
    passengersInput.type = 'hidden';
    passengersInput.name = 'passengers';
    passengersInput.value = totalPassengers;
    this.appendChild(passengersInput);
});
</script>
@endpush
@endsection
