@extends('layouts.frontend')

@section('title', 'Book Tickets - {{ config("app.name", "FlyNow Airlines") }}')

@section('content')
<div class="booking-container">
    <div class="container">
        <!-- Header -->
        <div class="row mb-5">
            <div class="col-12 text-center">
                <div class="booking-header">
                    <h1 class="booking-title">
                        <i class="fas fa-plane me-3"></i>Book Your Flight
                    </h1>
                    <p class="booking-subtitle">Find and book the perfect flight for your journey</p>
                </div>
            </div>
        </div>

        <!-- Search Form -->
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="booking-form-card">
                    <div class="booking-form-header">
                        <h3 class="form-section-title">
                            <i class="fas fa-search me-2"></i>Flight Search
                        </h3>
                    </div>
                    <div class="booking-form-body">
                        <form id="flight-search-form" action="{{ route('flight-search.results') }}" method="GET">
                            <!-- Trip Type Selection -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <div class="trip-type-selector">
                                        <input type="radio" class="btn-check" name="trip_type" id="one_way" value="one_way" checked>
                                        <label class="trip-type-btn" for="one_way">
                                            <i class="fas fa-arrow-right me-2"></i>One Way
                                        </label>

                                        <input type="radio" class="btn-check" name="trip_type" id="round_trip" value="round_trip">
                                        <label class="trip-type-btn" for="round_trip">
                                            <i class="fas fa-exchange-alt me-2"></i>Round Trip
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Airport Selection -->
                            <div class="form-section">
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <label class="booking-label">
                                            <i class="fas fa-plane-departure me-2"></i>From
                                        </label>
                                        <div class="position-relative">
                                            <input type="text"
                                                   class="booking-input airport-search"
                                                   id="departure_airport"
                                                   name="departure_airport"
                                                   placeholder="Enter city or airport code"
                                                   autocomplete="off"
                                                   required>
                                            <div class="airport-suggestions" id="departure_suggestions"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="booking-label">
                                            <i class="fas fa-plane-arrival me-2"></i>To
                                        </label>
                                        <div class="position-relative">
                                            <input type="text"
                                                   class="booking-input airport-search"
                                                   id="arrival_airport"
                                                   name="arrival_airport"
                                                   placeholder="Enter city or airport code"
                                                   autocomplete="off"
                                                   required>
                                            <div class="airport-suggestions" id="arrival_suggestions"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Date Selection -->
                            <div class="form-section">
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <label class="booking-label">
                                            <i class="fas fa-calendar-alt me-2"></i>Departure Date
                                        </label>
                                        <input type="date"
                                               class="booking-input"
                                               id="departure_date"
                                               name="departure_date"
                                               min="{{ date('Y-m-d') }}"
                                               required>
                                    </div>
                                    <div class="col-md-6" id="return_date_container" style="display: none;">
                                        <label class="booking-label">
                                            <i class="fas fa-calendar-alt me-2"></i>Return Date
                                        </label>
                                        <input type="date"
                                               class="booking-input"
                                               id="return_date"
                                               name="return_date"
                                               min="{{ date('Y-m-d') }}">
                                    </div>
                                </div>
                            </div>

                            <!-- Passengers and Class -->
                            <div class="form-section">
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <label class="booking-label">
                                            <i class="fas fa-users me-2"></i>Passengers
                                        </label>
                                        <div class="dropdown">
                                            <button class="booking-dropdown-btn"
                                                    type="button"
                                                    id="passengerDropdown"
                                                    data-bs-toggle="dropdown"
                                                    aria-expanded="false">
                                                <span id="passenger-summary">1 Adult</span>
                                                <i class="fas fa-chevron-down ms-auto"></i>
                                            </button>
                                            <div class="booking-dropdown-menu">
                                                <div class="passenger-row">
                                                    <div class="passenger-label">
                                                        <span>Adults (12+)</span>
                                                    </div>
                                                    <div class="passenger-controls">
                                                        <button class="passenger-btn" type="button" onclick="changePassengerCount('adults', -1)">-</button>
                                                        <input type="number" class="passenger-input" id="adults" name="adults" value="1" min="1" max="9" readonly>
                                                        <button class="passenger-btn" type="button" onclick="changePassengerCount('adults', 1)">+</button>
                                                    </div>
                                                </div>
                                                <div class="passenger-row">
                                                    <div class="passenger-label">
                                                        <span>Children (2-11)</span>
                                                    </div>
                                                    <div class="passenger-controls">
                                                        <button class="passenger-btn" type="button" onclick="changePassengerCount('children', -1)">-</button>
                                                        <input type="number" class="passenger-input" id="children" name="children" value="0" min="0" max="9" readonly>
                                                        <button class="passenger-btn" type="button" onclick="changePassengerCount('children', 1)">+</button>
                                                    </div>
                                                </div>
                                                <div class="passenger-row">
                                                    <div class="passenger-label">
                                                        <span>Infants (0-2)</span>
                                                    </div>
                                                    <div class="passenger-controls">
                                                        <button class="passenger-btn" type="button" onclick="changePassengerCount('infants', -1)">-</button>
                                                        <input type="number" class="passenger-input" id="infants" name="infants" value="0" min="0" max="9" readonly>
                                                        <button class="passenger-btn" type="button" onclick="changePassengerCount('infants', 1)">+</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="booking-label">
                                            <i class="fas fa-chair me-2"></i>Cabin Class
                                        </label>
                                        <select class="booking-select" name="cabin_class" id="cabin_class">
                                            <option value="economy">Economy</option>
                                            <option value="premium_economy">Premium Economy</option>
                                            <option value="business">Business</option>
                                            <option value="first">First Class</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Search Button -->
                            <div class="form-section">
                                <div class="row">
                                    <div class="col-12">
                                        <button type="submit" class="booking-search-btn">
                                            <i class="fas fa-search me-2"></i>Search Flights
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Popular Destinations -->
        <div class="popular-destinations">
            <div class="row mt-5">
                <div class="col-12">
                    <div class="destinations-header">
                        <h3 class="destinations-title">Popular Destinations</h3>
                        <p class="destinations-subtitle">Discover amazing places around the world</p>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="destination-card">
                                <div class="destination-content">
                                    <h5 class="destination-route">Islamabad → Bahrain</h5>
                                    <p class="destination-price">From PKR 84,000</p>
                                    <button class="destination-btn" onclick="setRoute('ISB', 'BAH')">Select Route</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="destination-card">
                                <div class="destination-content">
                                    <h5 class="destination-route">Lahore → Dubai</h5>
                                    <p class="destination-price">From PKR 75,000</p>
                                    <button class="destination-btn" onclick="setRoute('LHE', 'DXB')">Select Route</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="destination-card">
                                <div class="destination-content">
                                    <h5 class="destination-route">Karachi → Sharjah</h5>
                                    <p class="destination-price">From PKR 70,000</p>
                                    <button class="destination-btn" onclick="setRoute('KHI', 'SHJ')">Select Route</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="destination-card">
                                <div class="destination-content">
                                    <h5 class="destination-route">Islamabad → Sharjah</h5>
                                    <p class="destination-price">From PKR 78,000</p>
                                    <button class="destination-btn" onclick="setRoute('ISB', 'SHJ')">Select Route</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Professional Blue Color Scheme with White Gradients */
:root {
    --primary-blue: #4D73FC;
    --primary-blue-dark: #3B5BDB;
    --primary-blue-light: #748FFC;
    --gradient-blue-white: linear-gradient(135deg, #4D73FC 0%, #b6c3ff 100%);
    --gradient-blue-light: linear-gradient(135deg, #4D73FC 0%, #748FFC 50%, #FFFFFF 100%);
    --gradient-white-blue: linear-gradient(135deg, #FFFFFF 0%, #F8F8FF 50%, #4D73FC 100%);
    --text-primary: #2D3748;
    --text-secondary: #4A5568;
    --text-light: #718096;
    --border-color: #E2E8F0;
    --shadow-primary: 0 10px 25px rgba(77, 115, 252, 0.15);
    --shadow-secondary: 0 4px 12px rgba(77, 115, 252, 0.1);
    --border-radius: 16px;
    --border-radius-lg: 24px;
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Main Container */
.booking-container {
    background: var(--gradient-blue-light);
    min-height: 100vh;
    padding: 3rem 0;
    font-family: var(--font-family);
}

/* Header Styling */
.booking-header {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 248, 255, 0.9) 100%);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-primary);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    margin-bottom: 2rem;
}

.booking-title {
    color: var(--primary-blue);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(77, 115, 252, 0.1);
}

.booking-subtitle {
    color: var(--text-secondary);
    font-size: 1.125rem;
    font-weight: 400;
    margin: 0;
}

/* Form Card */
.booking-form-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 248, 255, 0.95) 100%);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-primary);
    border: 1px solid rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(15px);
    overflow: hidden;
}

.booking-form-header {
    background: var(--gradient-blue-white);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.form-section-title {
    color: white;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.booking-form-body {
    padding: 2rem;
}

/* Form Sections */
.form-section {
    background: linear-gradient(135deg, rgba(248, 248, 255, 0.5) 0%, rgba(255, 255, 255, 0.8) 100%);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(77, 115, 252, 0.1);
}

/* Trip Type Selector */
.trip-type-selector {
    display: flex;
    background: white;
    border-radius: var(--border-radius);
    padding: 0.25rem;
    box-shadow: var(--shadow-secondary);
    border: 1px solid var(--border-color);
}

.trip-type-btn {
    flex: 1;
    padding: 0.875rem 1.5rem;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    font-weight: 500;
    border-radius: calc(var(--border-radius) - 0.25rem);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.trip-type-btn:hover {
    background: linear-gradient(135deg, rgba(77, 115, 252, 0.1) 0%, rgba(255, 255, 255, 0.8) 100%);
    color: var(--primary-blue);
}

.btn-check:checked + .trip-type-btn {
    background: var(--gradient-blue-white);
    color: white;
    box-shadow: var(--shadow-secondary);
    transform: translateY(-1px);
}

/* Labels */
.booking-label {
    display: block;
    color: var(--primary-blue);
    font-weight: 600;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.booking-label i {
    color: var(--primary-blue-light);
}

/* Input Fields */
.booking-input {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: var(--font-family);
    background: white;
    color: var(--text-primary);
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.booking-input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(77, 115, 252, 0.1), var(--shadow-secondary);
    background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(248, 248, 255, 0.8) 100%);
}

.booking-input::placeholder {
    color: var(--text-light);
}

/* Select Fields */
.booking-select {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: var(--font-family);
    background: white;
    color: var(--text-primary);
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.booking-select:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(77, 115, 252, 0.1), var(--shadow-secondary);
}

/* Dropdown Button */
.booking-dropdown-btn {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: var(--font-family);
    background: white;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.booking-dropdown-btn:hover,
.booking-dropdown-btn:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(77, 115, 252, 0.1), var(--shadow-secondary);
}

/* Dropdown Menu */
.booking-dropdown-menu {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-primary);
    padding: 1rem;
    min-width: 300px;
    margin-top: 0.5rem;
}

.passenger-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(77, 115, 252, 0.1);
}

.passenger-row:last-child {
    border-bottom: none;
}

.passenger-label {
    color: var(--text-primary);
    font-weight: 500;
}

.passenger-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.passenger-btn {
    width: 32px;
    height: 32px;
    border: 1px solid var(--primary-blue);
    border-radius: 50%;
    background: white;
    color: var(--primary-blue);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    transition: all 0.3s ease;
}

.passenger-btn:hover {
    background: var(--gradient-blue-white);
    color: white;
    transform: scale(1.05);
}

.passenger-input {
    width: 50px;
    text-align: center;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 0.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Search Button */
.booking-search-btn {
    width: 100%;
    padding: 1rem 2rem;
    background: var(--gradient-blue-white);
    border: none;
    border-radius: var(--border-radius);
    color: white;
    font-size: 1.125rem;
    font-weight: 600;
    font-family: var(--font-family);
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-primary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.booking-search-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.booking-search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(77, 115, 252, 0.25);
    background: linear-gradient(135deg, var(--primary-blue-dark) 0%, var(--primary-blue) 100%);
}

.booking-search-btn:hover::before {
    left: 100%;
}

.booking-search-btn:active {
    transform: translateY(0);
}

/* Popular Destinations */
.popular-destinations {
    margin-top: 3rem;
}

.destinations-header {
    text-align: center;
    margin-bottom: 2rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 248, 255, 0.9) 100%);
    padding: 1.5rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-secondary);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.destinations-title {
    color: var(--primary-blue);
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.destinations-subtitle {
    color: var(--text-secondary);
    font-size: 1rem;
    margin: 0;
}

/* Destination Cards */
.destination-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 248, 255, 0.95) 100%);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-secondary);
    border: 1px solid rgba(77, 115, 252, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    backdrop-filter: blur(10px);
    overflow: hidden;
    position: relative;
}

.destination-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-blue-white);
}

.destination-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-primary);
    border-color: var(--primary-blue-light);
}

.destination-content {
    padding: 1.5rem;
    text-align: center;
}

.destination-route {
    color: var(--text-primary);
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.destination-price {
    color: var(--primary-blue);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.destination-btn {
    background: var(--gradient-blue-white);
    border: none;
    border-radius: var(--border-radius);
    color: white;
    padding: 0.5rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.destination-btn:hover {
    background: linear-gradient(135deg, var(--primary-blue-dark) 0%, var(--primary-blue) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(77, 115, 252, 0.3);
}

/* Airport Suggestions */
.airport-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
    box-shadow: var(--shadow-secondary);
}

.airport-suggestion {
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid rgba(77, 115, 252, 0.1);
    transition: all 0.2s ease;
}

.airport-suggestion:hover,
.airport-suggestion.highlighted {
    background: linear-gradient(135deg, rgba(77, 115, 252, 0.05) 0%, rgba(248, 248, 255, 0.8) 100%);
    border-left: 3px solid var(--primary-blue);
}

.airport-suggestion:last-child {
    border-bottom: none;
}

.airport-code {
    font-weight: 700;
    color: var(--primary-blue);
    font-size: 0.875rem;
}

.airport-name {
    color: var(--text-primary);
    margin-left: 0.5rem;
    font-weight: 500;
}

.airport-city {
    color: var(--text-light);
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .booking-container {
        padding: 1.5rem 0;
    }

    .booking-title {
        font-size: 2rem;
    }

    .booking-form-body {
        padding: 1.5rem;
    }

    .form-section {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .trip-type-btn {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }

    .booking-dropdown-menu {
        min-width: 280px;
    }

    .destinations-title {
        font-size: 1.5rem;
    }

    .destination-content {
        padding: 1rem;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000;
        --text-light: #333;
    }

    .booking-input,
    .booking-select,
    .booking-dropdown-btn {
        border-width: 2px;
    }
}
</style>
@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Trip type toggle
    const oneWayRadio = document.getElementById('one_way');
    const roundTripRadio = document.getElementById('round_trip');
    const returnDateContainer = document.getElementById('return_date_container');
    const returnDateInput = document.getElementById('return_date');

    function toggleReturnDate() {
        if (roundTripRadio.checked) {
            returnDateContainer.style.display = 'block';
            returnDateInput.required = true;
        } else {
            returnDateContainer.style.display = 'none';
            returnDateInput.required = false;
            returnDateInput.value = '';
        }
    }

    oneWayRadio.addEventListener('change', toggleReturnDate);
    roundTripRadio.addEventListener('change', toggleReturnDate);

    // Set minimum date for return date based on departure date
    const departureDateInput = document.getElementById('departure_date');
    departureDateInput.addEventListener('change', function() {
        returnDateInput.min = this.value;
        if (returnDateInput.value && returnDateInput.value < this.value) {
            returnDateInput.value = this.value;
        }
    });

    // Set default departure date to tomorrow
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    departureDateInput.value = tomorrow.toISOString().split('T')[0];

    // Airport autocomplete functionality with AJAX
    let searchTimeout;

    function setupAirportSearch(inputId, suggestionsId) {
        const input = document.getElementById(inputId);
        const suggestions = document.getElementById(suggestionsId);

        input.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();

            if (query.length < 1) {
                suggestions.style.display = 'none';
                return;
            }

            searchTimeout = setTimeout(() => {
                // Show loading indicator
                suggestions.innerHTML = '<div class="airport-suggestion"><i class="fas fa-spinner fa-spin me-2"></i>Searching airports...</div>';
                suggestions.style.display = 'block';

                // Make AJAX request
                fetch(`{{ route("airports.search") }}?q=${encodeURIComponent(query)}`)
                    .then(response => response.json())
                    .then(airports => {
                        if (airports.length > 0) {
                            suggestions.innerHTML = airports.map(airport => `
                                <div class="airport-suggestion" onclick="selectAirport('${inputId}', '${airport.code}', '${airport.name}', '${airport.city}')">
                                    <span class="airport-code">${airport.code}</span>
                                    <span class="airport-name">${airport.name}</span>
                                    <div class="airport-city">${airport.city}, ${airport.country}</div>
                                </div>
                            `).join('');
                            suggestions.style.display = 'block';
                        } else {
                            suggestions.innerHTML = '<div class="airport-suggestion text-muted">No airports found</div>';
                            suggestions.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        console.error('Airport search error:', error);
                        suggestions.innerHTML = '<div class="airport-suggestion text-danger">Error searching airports</div>';
                        suggestions.style.display = 'block';
                    });
            }, 300);
        });

        // Hide suggestions when clicking outside
        document.addEventListener('click', function(e) {
            if (!input.contains(e.target) && !suggestions.contains(e.target)) {
                suggestions.style.display = 'none';
            }
        });

        // Handle keyboard navigation
        input.addEventListener('keydown', function(e) {
            const suggestionItems = suggestions.querySelectorAll('.airport-suggestion');
            let currentIndex = -1;

            // Find currently highlighted item
            suggestionItems.forEach((item, index) => {
                if (item.classList.contains('highlighted')) {
                    currentIndex = index;
                }
            });

            if (e.key === 'ArrowDown') {
                e.preventDefault();
                currentIndex = Math.min(currentIndex + 1, suggestionItems.length - 1);
                highlightSuggestion(suggestionItems, currentIndex);
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                currentIndex = Math.max(currentIndex - 1, 0);
                highlightSuggestion(suggestionItems, currentIndex);
            } else if (e.key === 'Enter' && currentIndex >= 0) {
                e.preventDefault();
                suggestionItems[currentIndex].click();
            } else if (e.key === 'Escape') {
                suggestions.style.display = 'none';
            }
        });
    }

    function highlightSuggestion(items, index) {
        items.forEach((item, i) => {
            if (i === index) {
                item.classList.add('highlighted');
            } else {
                item.classList.remove('highlighted');
            }
        });
    }

    setupAirportSearch('departure_airport', 'departure_suggestions');
    setupAirportSearch('arrival_airport', 'arrival_suggestions');
});

function selectAirport(inputId, code, name, city) {
    const input = document.getElementById(inputId);
    const suggestions = document.getElementById(inputId.replace('_airport', '_suggestions'));

    input.value = `${code} - ${city}`;
    input.dataset.airportCode = code;
    suggestions.style.display = 'none';
}

function changePassengerCount(type, change) {
    const input = document.getElementById(type);
    const currentValue = parseInt(input.value);
    const newValue = Math.max(parseInt(input.min), Math.min(parseInt(input.max), currentValue + change));

    // Special validation for infants (cannot exceed adults)
    if (type === 'infants') {
        const adults = parseInt(document.getElementById('adults').value);
        if (newValue > adults) {
            return;
        }
    }

    input.value = newValue;
    updatePassengerSummary();
}

function updatePassengerSummary() {
    const adults = parseInt(document.getElementById('adults').value);
    const children = parseInt(document.getElementById('children').value);
    const infants = parseInt(document.getElementById('infants').value);

    let summary = [];
    if (adults > 0) summary.push(`${adults} Adult${adults > 1 ? 's' : ''}`);
    if (children > 0) summary.push(`${children} Child${children > 1 ? 'ren' : ''}`);
    if (infants > 0) summary.push(`${infants} Infant${infants > 1 ? 's' : ''}`);

    document.getElementById('passenger-summary').textContent = summary.join(', ');
}

function setRoute(departure, arrival) {
    const departureInput = document.getElementById('departure_airport');
    const arrivalInput = document.getElementById('arrival_airport');

    // Find airport details
    const airports = [
        { code: 'ISB', city: 'Islamabad' },
        { code: 'LHE', city: 'Lahore' },
        { code: 'KHI', city: 'Karachi' },
        { code: 'BAH', city: 'Manama' },
        { code: 'DXB', city: 'Dubai' },
        { code: 'SHJ', city: 'Sharjah' }
    ];

    const depAirport = airports.find(a => a.code === departure);
    const arrAirport = airports.find(a => a.code === arrival);

    if (depAirport && arrAirport) {
        departureInput.value = `${departure} - ${depAirport.city}`;
        departureInput.dataset.airportCode = departure;
        arrivalInput.value = `${arrival} - ${arrAirport.city}`;
        arrivalInput.dataset.airportCode = arrival;
    }
}

// Form submission handling
document.getElementById('flight-search-form').addEventListener('submit', function(e) {
    const departureInput = document.getElementById('departure_airport');
    const arrivalInput = document.getElementById('arrival_airport');

    // Extract airport codes from the input values
    const departureCode = departureInput.dataset.airportCode || departureInput.value.split(' - ')[0];
    const arrivalCode = arrivalInput.dataset.airportCode || arrivalInput.value.split(' - ')[0];

    // Update form values with airport codes
    departureInput.value = departureCode;
    arrivalInput.value = arrivalCode;

    // Calculate total passengers
    const adults = parseInt(document.getElementById('adults').value);
    const children = parseInt(document.getElementById('children').value);
    const infants = parseInt(document.getElementById('infants').value);
    const totalPassengers = adults + children + infants;

    // Add hidden input for total passengers
    const passengersInput = document.createElement('input');
    passengersInput.type = 'hidden';
    passengersInput.name = 'passengers';
    passengersInput.value = totalPassengers;
    this.appendChild(passengersInput);
});
</script>
@endpush
@endsection
