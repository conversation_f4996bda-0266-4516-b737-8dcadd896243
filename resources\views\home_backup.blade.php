@extends('layouts.frontend')

@section('title', 'FlyNow | Travling And Tours Template')
@section('description', 'FlyNow HTML5 Template')

@section('content')
<!-- Hero Banner start -->
<section class="hero-banner-1">
    <div class="container-fluid">
        <div class="content">
            <div class="vector-image">
                <svg xmlns="http://www.w3.org/2000/svg" width="1414" height="319" viewBox="0 0 1414 319" fill="none">
                    <path class="path"
                        d="M-0.5 215C62.4302 220.095 287 228 373 143.5C444.974 72.7818 368.5 -3.73136 320.5 1.99997C269.5 8.08952 231.721 43.5 253.5 119C275.279 194.5 367 248.212 541.5 207.325C675.76 175.867 795.5 82.7122 913 76.7122C967.429 73.9328 1072.05 88.6813 1085 207.325C1100 344.712 882 340.212 922.5 207.325C964.415 69.7967 1354 151.5 1479 183.5"
                        stroke="#ECECF2" stroke-width="6" stroke-linecap="round" stroke-dasharray="round"/>

                    <path class="dashed"
                        d="M-0.5 215C62.4302 220.095 287 228 373 143.5C444.974 72.7818 368.5 -3.73136 320.5 1.99997C269.5 8.08952 231.721 43.5 253.5 119C275.279 194.5 367 248.212 541.5 207.325C675.76 175.867 795.5 82.7122 913 76.7122C967.429 73.9328 1072.05 88.6813 1085 207.325C1100 344.712 882 340.212 922.5 207.325C964.415 69.7967 1354 151.5 1479 183.5"
                        stroke="#212627" stroke-width="6" stroke-linecap="round" stroke-dasharray="22 22"/>
                </svg>
                <div class="location-image">
                    <img src="{{ asset('assets/media/icons/location-blue.png') }}" alt="">
                </div>
            </div>
            <div class="row align-items-center row-gap-5">
                <div class="col-xxl-3 col-xl-4 col-lg-4 col-md-5 col-sm-5">
                    <div class="content-block">
                        <h1 class="lightest-black mb-16"><span class="color-primary">Book</span> Your Dream <span class="color-primary">Flights</span> Now!</h1>
                        <p class="dark-gray mb-24">Lorem ipsum dolor sit amet consectetur. Felis tristique pretium leo nisi at risus ac enim.</p>
                        <a href="{{ route('flights.booking') }}" class="cus-btn">Book Now</a>
                    </div>
                </div>
                <div class="col-xxl-9 col-xl-8 col-lg-8 col-md-7 col-sm-7">
                    <div class="image flynow-tilt"
                        data-tilt-options='{ "glare": false, "maxGlare": 0, "maxTilt": 3, "speed": 700, "scale": 1.02 }'>
                        <img src="{{ asset('assets/media/banner/plane.png') }}" alt="">
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- Hero Banner End -->

<!-- Booking Area Start -->
<section class="booking mb-20">
    <div class="container-fluid">
        <div class="content">
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" data-bs-tabs="tabs">
                        <li class="nav-item">
                            <a href="#flight" class="cus-btn primary active" aria-current="true" data-bs-toggle="tab">
                                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
                                    <g clip-path="url(#clip0_502_1331)">
                                        <path d="M31.6933 0.544584C30.6572 -0.491824 27.1402 1.34503 26.1041 2.38143L21.9545 6.53127H3.07887C2.63024 6.53127 2.24462 6.85011 2.16055 7.29104C2.07669 7.73189 2.31798 8.16995 2.73524 8.3348L15.2174 13.2677L7.5633 20.9216H0.323909C0.168651 20.9221 0.0346723 21.0323 0.00576263 21.1852C-0.023357 21.3385 0.060152 21.4901 0.20498 21.5471L6.29687 23.9548C6.33201 24.1078 6.38108 24.2574 6.44394 24.4038L6.17745 24.6709C5.79778 25.0503 5.79778 25.6651 6.17745 26.045C6.55664 26.4247 7.17263 26.4247 7.55182 26.045L7.81194 25.785C7.95935 25.8501 8.11132 25.9014 8.26623 25.9382L10.6144 31.9561C10.6709 32.1013 10.8229 32.1851 10.976 32.1568C11.0419 32.145 11.1002 32.1123 11.1451 32.0673C11.2044 32.0087 11.2399 31.9274 11.2399 31.8382V24.7512L19.0155 16.976L23.9019 29.4993C24.0654 29.9177 24.5037 30.1608 24.9452 30.0781C25.136 30.0421 25.3038 29.9498 25.4333 29.8212C25.6038 29.6499 25.7071 29.4151 25.7077 29.1591V10.284L29.8573 6.13423C30.893 5.09789 32.7293 1.58085 31.6933 0.544584Z" fill="#16191A"/>
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_502_1331">
                                            <rect width="32" height="32" fill="white"/>
                                        </clipPath>
                                    </defs>
                                </svg>
                                Flights
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#hotel" class="cus-btn primary" aria-current="false" data-bs-toggle="tab">
                                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
                                    <g clip-path="url(#clip0_502_1334)">
                                        <path d="M10.7589 9.00703H12.6339V10.882H10.7589V9.00703Z" fill="#16191A"/>
                                        <path d="M10.7589 13.3106H12.6339V15.1856H10.7589V13.3106Z" fill="#16191A"/>
                                        <path d="M19.3661 13.3106H21.2411V15.1856H19.3661V13.3106Z" fill="#16191A"/>
                                        <path d="M23.6696 13.3106H25.5446V15.1856H23.6696V13.3106Z" fill="#16191A"/>
                                        <path d="M19.3661 17.6142H21.2411V19.4892H19.3661V17.6142Z" fill="#16191A"/>
                                        <path d="M23.6696 17.6142H25.5446V19.4892H23.6696V17.6142Z" fill="#16191A"/>
                                        <path d="M19.3661 21.9178H21.2411V23.7928H19.3661V21.9178Z" fill="#16191A"/>
                                        <path d="M23.6696 21.9178H25.5446V23.7928H23.6696V21.9178Z" fill="#16191A"/>
                                        <path d="M19.3661 26.2213H21.2411V28.0963H19.3661V26.2213Z" fill="#16191A"/>
                                        <path d="M23.6696 26.2213H25.5446V28.0963H23.6696V26.2213Z" fill="#16191A"/>
                                        <path d="M6.45538 13.3106H8.33037V15.1856H6.45538V13.3106Z" fill="#16191A"/>
                                        <path d="M10.7589 17.6142H12.6339V19.4892H10.7589V17.6142Z" fill="#16191A"/>
                                        <path d="M6.45538 17.6142H8.33037V19.4892H6.45538V17.6142Z" fill="#16191A"/>
                                        <path d="M10.7589 21.9178H12.6339V23.7928H10.7589V21.9178Z" fill="#16191A"/>
                                        <path d="M6.45538 21.9178H8.33037V23.7928H6.45538V21.9178Z" fill="#16191A"/>
                                        <path d="M29.8482 30.5249V9.00703H16.9375V4.53428L14.7857 4.8929V0.399902H4.30356V6.63997L2.15181 6.99859V30.5249H0V32.3999H32V30.5249H29.8482ZM27.9732 10.882V30.5249H16.9375V10.882H27.9732ZM6.17856 2.2749H12.9107V5.2054L6.17856 6.32747V2.2749ZM4.02681 8.58696L15.0625 6.74765V30.5249H12.6339V26.2213H6.45538V30.5249H4.02681V8.58696ZM10.7589 30.5249H8.33037V28.0963H10.7589V30.5249Z" fill="#16191A"/>
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_502_1334">
                                            <rect width="32" height="32" fill="white"/>
                                        </clipPath>
                                    </defs>
                                </svg>
                                Hotel
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#car" class="cus-btn primary" aria-current="false" data-bs-toggle="tab">
                                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
                                    <path d="M30.4 12.8H28.8V9.6C28.8 8.16 27.64 6.4 26.24 6.4H5.76C4.36 6.4 3.2 8.16 3.2 9.6V12.8H1.6C0.72 12.8 0 13.52 0 14.4V19.2C0 20.08 0.72 20.8 1.6 20.8H3.2V22.4C3.2 23.28 3.92 24 4.8 24H6.4C7.28 24 8 23.28 8 22.4V20.8H24V22.4C24 23.28 24.72 24 25.6 24H27.2C28.08 24 28.8 23.28 28.8 22.4V20.8H30.4C31.28 20.8 32 20.08 32 19.2V14.4C32 13.52 31.28 12.8 30.4 12.8ZM6.4 17.6C5.52 17.6 4.8 16.88 4.8 16C4.8 15.12 5.52 14.4 6.4 14.4C7.28 14.4 8 15.12 8 16C8 16.88 7.28 17.6 6.4 17.6ZM25.6 17.6C24.72 17.6 24 16.88 24 16C24 15.12 24.72 14.4 25.6 14.4C26.48 14.4 27.2 15.12 27.2 16C27.2 16.88 26.48 17.6 25.6 17.6Z" fill="#16191A"/>
                                </svg>
                                Car
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content">
                        <div class="tab-pane active" id="flight">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <h5>Flight Booking System</h5>
                                        <p>Our professional flight booking system is coming soon! 
                                        @auth
                                            You can manage bookings from your <a href="{{ route('admin.dashboard') }}" class="alert-link">admin dashboard</a>.
                                        @else
                                            Please <a href="{{ route('login') }}" class="alert-link">login</a> to access the booking system.
                                        @endauth
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane" id="hotel">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <h5>Hotel Booking System</h5>
                                        <p>Hotel booking functionality will be available soon!</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane" id="car">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <h5>Car Rental System</h5>
                                        <p>Car rental booking functionality will be available soon!</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- Booking Area End -->

<!-- Features Section -->
<section class="features py-5">
    <div class="container-fluid">
        <div class="row text-center">
            <div class="col-md-4 mb-4">
                <div class="feature-card p-4">
                    <i class="fas fa-plane fa-3x text-primary mb-3"></i>
                    <h4>Professional Booking</h4>
                    <p>Advanced airline booking system with real-time availability and instant confirmation.</p>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="feature-card p-4">
                    <i class="fas fa-shield-alt fa-3x text-primary mb-3"></i>
                    <h4>Secure Payments</h4>
                    <p>Industry-standard security measures to protect your personal and payment information.</p>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="feature-card p-4">
                    <i class="fas fa-headset fa-3x text-primary mb-3"></i>
                    <h4>24/7 Support</h4>
                    <p>Round-the-clock customer support to assist you with any travel-related queries.</p>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- Features Section End -->
@endsection

@push('styles')
<style>
.feature-card {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}
.feature-card:hover {
    transform: translateY(-5px);
}
.features {
    background: #f8f9fa;
}
</style>
@endpush
