@extends('layouts.frontend')

@push('title')
    Contact Us - {{ config('app.name', 'Sky Avenue') }} |
@endpush

@push('description')
    Get in touch with {{ config('app.name', 'Sky Avenue') }}. We're here to help with your travel needs and questions.
@endpush

@push('content')
    <!-- Hero Banner start -->
    <section class="hero-banner-1">
        <div class="container-fluid">
            <div class="content">
                <div class="vector-image">
                    <svg xmlns="http://www.w3.org/2000/svg" width="1414" height="319" viewBox="0 0 1414 319" fill="none">
                        <path class="path"
                            d="M-0.5 215C62.4302 220.095 287 228 373 143.5C444.974 72.7818 368.5 -3.73136 320.5 1.99997C269.5 8.08952 231.721 43.5 253.5 119C275.279 194.5 367 248.212 541.5 207.325C675.76 175.867 795.5 82.7122 913 76.7122C967.429 73.9328 1072.05 88.6813 1085 207.325C1100 344.712 882 340.212 922.5 207.325C964.415 69.7967 1354 151.5 1479 183.5"
                            stroke="#ECECF2" stroke-width="6" stroke-linecap="round" stroke-dasharray="round" />
                    </svg>
                </div>
                <div class="row">
                    <div class="col-lg-6">
                        <div class="banner-content">
                            <h1 class="banner-title">Contact Us</h1>
                            <p class="banner-text">We're here to help you with all your travel needs. Get in touch with our friendly support team.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Hero Banner end -->

    <!-- Contact Section start -->
    <section class="contact-section py-5">
        <div class="container-fluid">
            <div class="content">
                <div class="row">
                    <!-- Contact Information -->
                    <div class="col-lg-4 mb-4">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body p-4">
                                <h4 class="card-title mb-4">
                                    <i class="fas fa-info-circle text-primary me-2"></i>
                                    Contact Information
                                </h4>
                                
                                <div class="contact-item mb-4">
                                    <div class="d-flex align-items-center">
                                        <div class="contact-icon">
                                            <i class="fas fa-phone text-primary"></i>
                                        </div>
                                        <div class="contact-details">
                                            <h6 class="mb-1">Phone</h6>
                                            <p class="mb-0 text-muted">+****************</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="contact-item mb-4">
                                    <div class="d-flex align-items-center">
                                        <div class="contact-icon">
                                            <i class="fas fa-envelope text-primary"></i>
                                        </div>
                                        <div class="contact-details">
                                            <h6 class="mb-1">Email</h6>
                                            <p class="mb-0 text-muted"><EMAIL></p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="contact-item mb-4">
                                    <div class="d-flex align-items-center">
                                        <div class="contact-icon">
                                            <i class="fas fa-map-marker-alt text-primary"></i>
                                        </div>
                                        <div class="contact-details">
                                            <h6 class="mb-1">Address</h6>
                                            <p class="mb-0 text-muted">123 Aviation Street<br>Sky City, SC 12345</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="contact-item">
                                    <div class="d-flex align-items-center">
                                        <div class="contact-icon">
                                            <i class="fas fa-clock text-primary"></i>
                                        </div>
                                        <div class="contact-details">
                                            <h6 class="mb-1">Business Hours</h6>
                                            <p class="mb-0 text-muted">Mon - Fri: 9:00 AM - 6:00 PM<br>Sat - Sun: 10:00 AM - 4:00 PM</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Contact Form -->
                    <div class="col-lg-8">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body p-4">
                                <h4 class="card-title mb-4">
                                    <i class="fas fa-paper-plane text-primary me-2"></i>
                                    Send us a Message
                                </h4>
                                
                                <form id="contactForm">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="firstName" class="form-label">First Name</label>
                                            <input type="text" class="form-control" id="firstName" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="lastName" class="form-label">Last Name</label>
                                            <input type="text" class="form-control" id="lastName" required>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="email" class="form-label">Email Address</label>
                                            <input type="email" class="form-control" id="email" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="phone" class="form-label">Phone Number</label>
                                            <input type="tel" class="form-control" id="phone">
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="subject" class="form-label">Subject</label>
                                        <select class="form-select" id="subject" required>
                                            <option value="">Choose a subject</option>
                                            <option value="booking">Booking Inquiry</option>
                                            <option value="support">Customer Support</option>
                                            <option value="feedback">Feedback</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <label for="message" class="form-label">Message</label>
                                        <textarea class="form-control" id="message" rows="5" required placeholder="Please describe your inquiry or message..."></textarea>
                                    </div>
                                    
                                    <button type="submit" class="cus-btn">
                                        <i class="fas fa-paper-plane me-2"></i>Send Message
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Contact Section end -->
@endpush

@push('styles')
    <style>
        .hero-banner-1 {
            background: linear-gradient(135deg, #4D73FC 0%, #6B8AFF 100%);
            color: white;
            padding: 100px 0;
            position: relative;
            overflow: hidden;
        }
        
        .banner-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            font-family: "Inter", sans-serif;
        }
        
        .banner-text {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }
        
        .vector-image {
            position: absolute;
            top: 0;
            right: 0;
            opacity: 0.1;
        }
        
        .contact-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #F8F8FF 0%, #ECECF2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            flex-shrink: 0;
        }
        
        .contact-icon i {
            font-size: 1.2rem;
        }
        
        .contact-details h6 {
            color: #16191A;
            font-weight: 600;
            font-family: "Inter", sans-serif;
        }
        
        .form-control, .form-select {
            border: 2px solid #ECECF2;
            border-radius: 8px;
            padding: 0.75rem 1rem;
            font-family: "Inter", sans-serif;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #4D73FC;
            box-shadow: 0 0 0 0.2rem rgba(77, 115, 252, 0.25);
        }
        
        .form-label {
            font-weight: 600;
            color: #16191A;
            font-family: "Inter", sans-serif;
            margin-bottom: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .banner-title {
                font-size: 2rem;
            }
            
            .contact-icon {
                width: 40px;
                height: 40px;
            }
        }
    </style>
@endpush

@push('scripts')
    <script>
        document.getElementById('contactForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Simple form validation and submission simulation
            const formData = new FormData(this);
            
            // Show success message
            alert('Thank you for your message! We will get back to you soon.');
            
            // Reset form
            this.reset();
        });
    </script>
@endpush
