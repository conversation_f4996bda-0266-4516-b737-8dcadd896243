<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Flight extends Model
{
    protected $fillable = [
        'flight_number',
        'airline_id',
        'departure_airport_id',
        'arrival_airport_id',
        'departure_time',
        'arrival_time',
        'duration',
        'duration_minutes',
        'aircraft_type',
        'total_seats',
        'available_seats',
        'base_price',
        'currency',
        'flight_type',
        'class_type',
        'status',
        'gate',
        'notes',
        'source',
        'api_provider',
        'api_flight_id',
        'api_data',
        'api_last_updated',
        'stops',
        'baggage_info',
        'cancellation_policy',
    ];

    protected $casts = [
        'departure_time' => 'datetime',
        'arrival_time' => 'datetime',
        'base_price' => 'decimal:2',
        'duration_minutes' => 'integer',
        'total_seats' => 'integer',
        'available_seats' => 'integer',
        'api_data' => 'array',
        'baggage_info' => 'array',
        'api_last_updated' => 'datetime',
        'stops' => 'integer',
    ];

    /**
     * Get the airline for this flight
     */
    public function airline(): BelongsTo
    {
        return $this->belongsTo(Airline::class);
    }

    /**
     * Get the departure airport
     */
    public function departureAirport(): BelongsTo
    {
        return $this->belongsTo(Airport::class, 'departure_airport_id');
    }

    /**
     * Get the arrival airport
     */
    public function arrivalAirport(): BelongsTo
    {
        return $this->belongsTo(Airport::class, 'arrival_airport_id');
    }

    /**
     * Get all bookings for this flight
     */
    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Get formatted flight duration
     */
    public function getFormattedDurationAttribute(): string
    {
        $hours = intval($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;
        return sprintf('%dh %02dm', $hours, $minutes);
    }

    /**
     * Get route display (e.g., "LHR-JFK")
     */
    public function getRouteAttribute(): string
    {
        return $this->departureAirport->code . '-' . $this->arrivalAirport->code;
    }

    /**
     * Check if flight is bookable
     */
    public function getIsBookableAttribute(): bool
    {
        return $this->status === 'Scheduled' &&
               $this->available_seats > 0 &&
               $this->departure_time > now();
    }

    /**
     * Scope for filtering by flight type
     */
    public function scopeByType($query, $type)
    {
        if ($type && $type !== 'All Types') {
            return $query->where('flight_type', $type);
        }
        return $query;
    }

    /**
     * Scope for filtering by status
     */
    public function scopeByStatus($query, $status)
    {
        if ($status) {
            return $query->where('status', $status);
        }
        return $query;
    }

    /**
     * Scope for searching flights
     */
    public function scopeSearch($query, $search)
    {
        if ($search) {
            return $query->where(function ($q) use ($search) {
                $q->where('flight_number', 'like', "%{$search}%")
                  ->orWhereHas('departureAirport', function ($q) use ($search) {
                      $q->where('code', 'like', "%{$search}%")
                        ->orWhere('city', 'like', "%{$search}%");
                  })
                  ->orWhereHas('arrivalAirport', function ($q) use ($search) {
                      $q->where('code', 'like', "%{$search}%")
                        ->orWhere('city', 'like', "%{$search}%");
                  });
            });
        }
        return $query;
    }
}
