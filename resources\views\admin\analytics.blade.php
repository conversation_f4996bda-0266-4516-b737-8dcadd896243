@extends('layouts.admin')

@section('page-title', 'Analytics Dashboard')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="page-title">Analytics Dashboard</h1>
            <p class="page-subtitle">Flight booking analytics and insights</p>
        </div>
        <div class="d-flex gap-2">
            <select class="form-select" style="width: 150px;">
                <option value="7">Last 7 days</option>
                <option value="30" selected>Last 30 days</option>
                <option value="90">Last 90 days</option>
                <option value="365">Last year</option>
            </select>
            <button class="btn btn-outline-primary">
                <i class="fas fa-download me-2"></i>Export Report
            </button>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="h4 fw-bold mb-0">1,247</div>
                            <div class="small">Total Bookings</div>
                        </div>
                        <div class="fs-1 opacity-50">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <span class="badge bg-light text-primary">+12.5%</span>
                        <span class="small">vs last month</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="h4 fw-bold mb-0">$284,750</div>
                            <div class="small">Total Revenue</div>
                        </div>
                        <div class="fs-1 opacity-50">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <span class="badge bg-light text-success">+8.3%</span>
                        <span class="small">vs last month</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="h4 fw-bold mb-0">2,891</div>
                            <div class="small">Total Passengers</div>
                        </div>
                        <div class="fs-1 opacity-50">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <span class="badge bg-light text-info">+15.7%</span>
                        <span class="small">vs last month</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="h4 fw-bold mb-0">78.5%</div>
                            <div class="small">Avg Occupancy</div>
                        </div>
                        <div class="fs-1 opacity-50">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <span class="badge bg-light text-warning">+3.2%</span>
                        <span class="small">vs last month</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Booking Trends Chart -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Booking Trends</h5>
                </div>
                <div class="card-body">
                    <canvas id="bookingTrendsChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <!-- Top Routes -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-route me-2"></i>Top Routes</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <div class="fw-bold">NYC → LAX</div>
                            <small class="text-muted">New York to Los Angeles</small>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold">156</div>
                            <small class="text-muted">bookings</small>
                        </div>
                    </div>
                    <div class="progress mb-3" style="height: 8px;">
                        <div class="progress-bar bg-primary" style="width: 85%"></div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <div class="fw-bold">LAX → SFO</div>
                            <small class="text-muted">Los Angeles to San Francisco</small>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold">134</div>
                            <small class="text-muted">bookings</small>
                        </div>
                    </div>
                    <div class="progress mb-3" style="height: 8px;">
                        <div class="progress-bar bg-success" style="width: 73%"></div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <div class="fw-bold">MIA → NYC</div>
                            <small class="text-muted">Miami to New York</small>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold">98</div>
                            <small class="text-muted">bookings</small>
                        </div>
                    </div>
                    <div class="progress mb-3" style="height: 8px;">
                        <div class="progress-bar bg-info" style="width: 53%"></div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <div class="fw-bold">CHI → DEN</div>
                            <small class="text-muted">Chicago to Denver</small>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold">87</div>
                            <small class="text-muted">bookings</small>
                        </div>
                    </div>
                    <div class="progress mb-3" style="height: 8px;">
                        <div class="progress-bar bg-warning" style="width: 47%"></div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="fw-bold">SEA → LAS</div>
                            <small class="text-muted">Seattle to Las Vegas</small>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold">72</div>
                            <small class="text-muted">bookings</small>
                        </div>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-secondary" style="width: 39%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revenue by Airline -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Revenue by Airline</h5>
                </div>
                <div class="card-body">
                    <canvas id="revenueByAirlineChart" height="150"></canvas>
                </div>
            </div>
        </div>

        <!-- Booking Status Distribution -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-donut me-2"></i>Booking Status</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="h3 fw-bold text-success">892</div>
                            <div class="small text-muted">Confirmed</div>
                            <div class="progress mt-2" style="height: 6px;">
                                <div class="progress-bar bg-success" style="width: 71.5%"></div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="h3 fw-bold text-warning">234</div>
                            <div class="small text-muted">Pending</div>
                            <div class="progress mt-2" style="height: 6px;">
                                <div class="progress-bar bg-warning" style="width: 18.8%"></div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="h3 fw-bold text-info">98</div>
                            <div class="small text-muted">Completed</div>
                            <div class="progress mt-2" style="height: 6px;">
                                <div class="progress-bar bg-info" style="width: 7.9%"></div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="h3 fw-bold text-danger">23</div>
                            <div class="small text-muted">Cancelled</div>
                            <div class="progress mt-2" style="height: 6px;">
                                <div class="progress-bar bg-danger" style="width: 1.8%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Recent Activity</h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <div class="fw-bold">New booking confirmed</div>
                                <div class="small text-muted">Flight AA123 - John Doe</div>
                                <div class="small text-muted">2 minutes ago</div>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-warning"></div>
                            <div class="timeline-content">
                                <div class="fw-bold">Flight delayed</div>
                                <div class="small text-muted">Flight UA456 delayed by 30 minutes</div>
                                <div class="small text-muted">15 minutes ago</div>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <div class="fw-bold">Passenger checked in</div>
                                <div class="small text-muted">Flight DL789 - Jane Smith</div>
                                <div class="small text-muted">1 hour ago</div>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-danger"></div>
                            <div class="timeline-content">
                                <div class="fw-bold">Booking cancelled</div>
                                <div class="small text-muted">Flight SW101 - Mike Johnson</div>
                                <div class="small text-muted">2 hours ago</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-tachometer-alt me-2"></i>Performance Metrics</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 text-center mb-3">
                            <div class="h4 fw-bold text-primary">$228</div>
                            <div class="small text-muted">Avg Booking Value</div>
                        </div>
                        <div class="col-6 text-center mb-3">
                            <div class="h4 fw-bold text-success">2.3</div>
                            <div class="small text-muted">Avg Passengers/Booking</div>
                        </div>
                        <div class="col-6 text-center mb-3">
                            <div class="h4 fw-bold text-info">94.2%</div>
                            <div class="small text-muted">On-time Performance</div>
                        </div>
                        <div class="col-6 text-center mb-3">
                            <div class="h4 fw-bold text-warning">1.8%</div>
                            <div class="small text-muted">Cancellation Rate</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Booking Trends Chart
const bookingTrendsCtx = document.getElementById('bookingTrendsChart').getContext('2d');
new Chart(bookingTrendsCtx, {
    type: 'line',
    data: {
        labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
        datasets: [{
            label: 'Bookings',
            data: [285, 312, 298, 352],
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4
        }, {
            label: 'Revenue ($000)',
            data: [65, 72, 68, 81],
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Revenue by Airline Chart
const revenueByAirlineCtx = document.getElementById('revenueByAirlineChart').getContext('2d');
new Chart(revenueByAirlineCtx, {
    type: 'doughnut',
    data: {
        labels: ['American Airlines', 'Delta', 'United', 'Southwest', 'Others'],
        datasets: [{
            data: [85000, 72000, 68000, 45000, 14750],
            backgroundColor: [
                '#007bff',
                '#28a745',
                '#ffc107',
                '#dc3545',
                '#6c757d'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 17px;
    width: 2px;
    height: calc(100% + 8px);
    background-color: #dee2e6;
}
</style>
@endpush
