<?php

if (!function_exists('app_name')) {
    /**
     * Get the application name from settings or config
     */
    function app_name()
    {
        return \Illuminate\Support\Facades\Cache::get('settings.site_name', config('app.name', 'FlyNow Airlines'));
    }
}

if (!function_exists('app_description')) {
    /**
     * Get the application description from settings
     */
    function app_description()
    {
        return \Illuminate\Support\Facades\Cache::get('settings.site_description', 'Your trusted airline booking partner');
    }
}

if (!function_exists('contact_email')) {
    /**
     * Get the contact email from settings
     */
    function contact_email()
    {
        return \Illuminate\Support\Facades\Cache::get('settings.contact_email', '<EMAIL>');
    }
}

if (!function_exists('contact_phone')) {
    /**
     * Get the contact phone from settings
     */
    function contact_phone()
    {
        return \Illuminate\Support\Facades\Cache::get('settings.contact_phone', '+****************');
    }
}
