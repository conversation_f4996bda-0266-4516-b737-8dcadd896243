<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Sky Avenue') }} - Admin Dashboard</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Bootstrap CSS -->
    <link href="{{ asset('assets/css/vendor/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/vendor/font-awesome.css') }}" rel="stylesheet">
    
    <!-- Custom Admin CSS -->
    <style>
        :root {
            --primary-blue: #4D73FC;
            --primary-blue-dark: #3B5BDB;
            --primary-blue-light: #748FFC;
            --gradient-blue-white: linear-gradient(135deg, #4D73FC 0%, #FFFFFF 100%);
            --gradient-blue-light: linear-gradient(135deg, #4D73FC 0%, #748FFC 50%, #FFFFFF 100%);
            --gradient-white-blue: linear-gradient(135deg, #FFFFFF 0%, #F8F8FF 50%, #4D73FC 100%);
            --secondary-color: #64748b;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #06b6d4;
            --light-color: #f8fafc;
            --dark-color: #1e293b;
            --text-primary: #2D3748;
            --text-secondary: #4A5568;
            --text-light: #718096;
            --border-color: #E2E8F0;
            --shadow-primary: 0 10px 25px rgba(77, 115, 252, 0.15);
            --shadow-secondary: 0 4px 12px rgba(77, 115, 252, 0.1);
            --border-radius: 16px;
            --border-radius-lg: 24px;
            --sidebar-width: 280px;
            --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        body {
            font-family: var(--font-family);
            background: var(--gradient-blue-light);
            min-height: 100vh;
        }

        .admin-wrapper {
            display: flex;
            min-height: 100vh;
        }

        .admin-sidebar {
            width: var(--sidebar-width);
            background: var(--gradient-blue-white);
            color: white;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-primary);
            backdrop-filter: blur(15px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
        }

        .admin-sidebar.collapsed {
            width: 70px;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            backdrop-filter: blur(10px);
        }

        .sidebar-header .logo {
            font-size: 1.5rem;
            font-weight: 600;
            color: white;
            text-decoration: none;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-item {
            margin: 0.25rem 0;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            border-radius: 0 var(--border-radius) var(--border-radius) 0;
            margin: 0.25rem 0;
            font-weight: 500;
        }

        .nav-link:hover,
        .nav-link.active {
            color: white;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
            border-left-color: white;
            transform: translateX(5px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .nav-link i {
            width: 20px;
            margin-right: 0.75rem;
            text-align: center;
        }

        .admin-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            transition: all 0.3s ease;
            background: linear-gradient(135deg, rgba(248, 248, 255, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%);
            min-height: 100vh;
            backdrop-filter: blur(10px);
        }

        .admin-content.expanded {
            margin-left: 70px;
        }

        .admin-header {
            background: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-main {
            padding: 2rem;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 248, 255, 0.9) 100%);
            border-radius: var(--border-radius-lg);
            margin: 1rem;
            box-shadow: var(--shadow-secondary);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .page-title {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--primary-blue);
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(77, 115, 252, 0.1);
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
            margin-bottom: 2rem;
            font-weight: 400;
        }

        .card {
            border: none;
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 1.5rem;
        }

        .card-header {
            background: white;
            border-bottom: 1px solid #e2e8f0;
            padding: 1.25rem;
            font-weight: 600;
        }

        .btn-primary {
            background: var(--gradient-blue-white);
            border: none;
            color: white;
            font-weight: 600;
            border-radius: var(--border-radius);
            padding: 0.5rem 1.5rem;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-secondary);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-blue-dark) 0%, var(--primary-blue) 100%);
            transform: translateY(-1px);
            box-shadow: var(--shadow-primary);
            color: white;
        }

        .btn-outline-primary {
            border: 2px solid var(--primary-blue);
            color: var(--primary-blue);
            background: transparent;
            font-weight: 600;
            border-radius: var(--border-radius);
            padding: 0.5rem 1.5rem;
            transition: all 0.3s ease;
        }

        .btn-outline-primary:hover {
            background: var(--gradient-blue-white);
            color: white;
            border-color: var(--primary-blue);
            transform: translateY(-1px);
            box-shadow: var(--shadow-secondary);
        }

        /* Modern Statistics Cards */
        .modern-stats-card {
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
            border-radius: var(--border-radius-lg);
            padding: 1.5rem;
            box-shadow: var(--shadow-secondary);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(15px);
        }

        .modern-stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.05));
            pointer-events: none;
        }

        .modern-stats-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-primary);
        }

        .gradient-primary {
            --gradient-start: #4D73FC;
            --gradient-end: #FFFFFF;
        }

        .gradient-success {
            --gradient-start: #4D73FC;
            --gradient-end: #748FFC;
        }

        .gradient-info {
            --gradient-start: #748FFC;
            --gradient-end: #FFFFFF;
        }

        .gradient-warning {
            --gradient-start: #3B5BDB;
            --gradient-end: #4D73FC;
        }

        .stats-content {
            position: relative;
            z-index: 2;
        }

        .stats-header {
            display: flex;
            justify-content: between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .stats-icon-modern {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            backdrop-filter: blur(10px);
        }

        .stats-trend {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.875rem;
            font-weight: 600;
        }

        .trend-value {
            color: rgba(255, 255, 255, 0.9);
        }

        .stats-number-modern {
            font-size: 2.5rem;
            font-weight: 800;
            color: white;
            margin-bottom: 0.25rem;
            line-height: 1;
        }

        .stats-label-modern {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .stats-description {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.875rem;
        }

        .stats-chart {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 100px;
            height: 40px;
            opacity: 0.3;
        }

        /* Modern Widgets */
        .modern-widget {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 248, 255, 0.95) 100%);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-secondary);
            transition: all 0.3s ease;
            border: 1px solid rgba(77, 115, 252, 0.1);
            overflow: hidden;
            backdrop-filter: blur(15px);
        }

        .modern-widget:hover {
            box-shadow: var(--shadow-primary);
            transform: translateY(-2px);
            border-color: var(--primary-blue-light);
        }

        .widget-header {
            padding: 1.5rem 1.5rem 1rem;
            border-bottom: 1px solid #f1f5f9;
        }

        .widget-title {
            font-size: 1.125rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.25rem;
        }

        .widget-subtitle {
            font-size: 0.875rem;
            color: #64748b;
            margin: 0;
        }

        .widget-body {
            padding: 1.5rem;
        }

        .widget-controls .btn-group .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 0.75rem;
        }

        /* Activity Feed */
        .activity-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .pulse-dot {
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
        }

        .modern-activity-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .modern-activity-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            padding: 1rem 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .modern-activity-item:last-child {
            border-bottom: none;
        }

        .activity-avatar {
            flex-shrink: 0;
        }

        .avatar-img {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            object-fit: cover;
        }

        .system-avatar {
            width: 32px;
            height: 32px;
            background: #6366f1;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.875rem;
        }

        .activity-content {
            flex: 1;
            min-width: 0;
        }

        .activity-text {
            font-size: 0.875rem;
            color: #374151;
            margin-bottom: 0.25rem;
        }

        .activity-meta {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.75rem;
            color: #6b7280;
        }

        .activity-user {
            font-weight: 500;
        }

        .activity-time::before {
            content: '•';
            margin-right: 0.5rem;
        }

        .activity-type {
            flex-shrink: 0;
            font-size: 1rem;
        }

        .activity-footer {
            padding-top: 1rem;
            border-top: 1px solid #f1f5f9;
            margin-top: 1rem;
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 2rem 1rem;
        }

        .empty-icon {
            width: 64px;
            height: 64px;
            background: #f1f5f9;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: #9ca3af;
        }

        .empty-title {
            font-size: 1rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .empty-text {
            font-size: 0.875rem;
            color: #6b7280;
            margin: 0;
        }

        /* Performance Metrics */
        .performance-widget .widget-body {
            padding: 1rem 1.5rem 1.5rem;
        }

        .performance-metrics {
            display: flex;
            justify-content: space-around;
            gap: 1rem;
        }

        .metric-item {
            text-align: center;
            flex: 1;
        }

        .metric-label {
            font-size: 0.75rem;
            font-weight: 600;
            color: #6b7280;
            margin-bottom: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .progress-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            position: relative;
            background: conic-gradient(#6366f1 0deg, #e5e7eb 0deg);
        }

        .progress-circle::before {
            content: '';
            position: absolute;
            width: 40px;
            height: 40px;
            background: white;
            border-radius: 50%;
        }

        .percentage {
            position: relative;
            z-index: 1;
            font-size: 0.875rem;
            font-weight: 700;
            color: #374151;
        }

        /* Route List */
        .route-list {
            space-y: 1rem;
        }

        .route-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            background: #f8fafc;
            border-radius: 0.75rem;
            margin-bottom: 0.75rem;
        }

        .route-info {
            flex: 1;
        }

        .route-cities {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.5rem;
        }

        .departure, .arrival {
            font-weight: 700;
            color: #374151;
            font-size: 0.875rem;
        }

        .route-icon {
            color: #6366f1;
            font-size: 0.75rem;
        }

        .route-stats {
            display: flex;
            gap: 1rem;
            font-size: 0.75rem;
            color: #6b7280;
        }

        .route-trend {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
        }

        /* Quick Actions Grid */
        .quick-actions-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .quick-action-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: #f8fafc;
            border-radius: 0.75rem;
            text-decoration: none;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .quick-action-item:hover {
            background: #f1f5f9;
            border-color: #e2e8f0;
            text-decoration: none;
            transform: translateY(-1px);
        }

        .action-icon {
            width: 40px;
            height: 40px;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
            flex-shrink: 0;
        }

        .action-content {
            flex: 1;
            min-width: 0;
        }

        .action-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.125rem;
        }

        .action-subtitle {
            font-size: 0.75rem;
            color: #6b7280;
        }

        /* Chart Container */
        .chart-container {
            position: relative;
            height: 300px;
        }

        @media (max-width: 768px) {
            .admin-sidebar {
                width: 70px;
            }

            .admin-content {
                margin-left: 70px;
            }

            .nav-link span {
                display: none;
            }

            .performance-metrics {
                flex-direction: column;
                gap: 1.5rem;
            }

            .metric-item {
                display: flex;
                align-items: center;
                gap: 1rem;
                text-align: left;
            }

            .progress-circle {
                margin: 0;
            }

            .quick-actions-grid {
                grid-template-columns: 1fr;
            }

            .route-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.75rem;
            }

            .route-trend {
                align-self: flex-end;
            }

            .modern-stats-card {
                margin-bottom: 1rem;
            }

            .stats-header {
                flex-direction: column;
                gap: 0.5rem;
            }

            .chart-container {
                height: 250px;
            }
        }
    </style>

    <!-- Scripts -->
    <script src="{{ asset('assets/js/vendor/jquery-3.6.3.min.js') }}"></script>
    <script src="{{ asset('assets/js/vendor/bootstrap.min.js') }}"></script>
</head>
<body>
    <div class="admin-wrapper">
        <!-- Sidebar -->
        <nav class="admin-sidebar" id="adminSidebar">
            <div class="sidebar-header">
                <a href="{{ route('admin.dashboard') }}" class="logo">
                    <i class="fas fa-plane"></i>
                    <span class="ms-2">{{ config('app.name', 'FlyNow Airlines') }}</span>
                </a>
            </div>
            
            <div class="sidebar-nav">
                <div class="nav-item">
                    <a href="{{ route('admin.dashboard') }}" class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="{{ route('admin.flights.index') }}" class="nav-link {{ request()->routeIs('admin.flights.*') ? 'active' : '' }}">
                        <i class="fas fa-plane"></i>
                        <span>Flight Management</span>
                    </a>
                </div>

                {{-- <div class="nav-item">
                    <a href="{{ route('admin.flight-api.index') }}" class="nav-link {{ request()->routeIs('admin.flight-api.*') ? 'active' : '' }}">
                        <i class="fas fa-cloud-download-alt"></i>
                        <span>Flight API</span>
                    </a>
                </div> --}}

                {{-- <div class="nav-item">
                    <a href="{{ route('admin.flight-search.index') }}" class="nav-link {{ request()->routeIs('admin.flight-search.*') ? 'active' : '' }}">
                        <i class="fas fa-search"></i>
                        <span>Flight Search</span>
                    </a>
                </div> --}}

                <div class="nav-item">
                    <a href="{{ route('admin.book-tickets.index') }}" class="nav-link {{ request()->routeIs('admin.book-tickets.*') ? 'active' : '' }}">
                        <i class="fas fa-ticket-alt"></i>
                        <span>Book Tickets</span>
                    </a>
                </div>

                {{-- <div class="nav-item">
                    <a href="{{ route('admin.airports.index') }}" class="nav-link {{ request()->routeIs('admin.airports.*') ? 'active' : '' }}">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>Airport Management</span>
                    </a>
                </div> --}}

                {{-- <div class="nav-item">
                    <a href="{{ route('admin.bookings.index') }}" class="nav-link {{ request()->routeIs('admin.bookings.*') ? 'active' : '' }}">
                        <i class="fas fa-ticket-alt"></i>
                        <span>Booking Management</span>
                    </a>
                </div> --}}

                <div class="nav-item">
                    <a href="{{ route('admin.users.index') }}" class="nav-link {{ request()->routeIs('admin.users.*') ? 'active' : '' }}">
                        <i class="fas fa-users"></i>
                        <span>Users</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a href="{{ route('admin.roles.index') }}" class="nav-link {{ request()->routeIs('admin.roles.*') ? 'active' : '' }}">
                        <i class="fas fa-user-shield"></i>
                        <span>Roles & Permissions</span>
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="{{ route('admin.content.index') }}" class="nav-link {{ request()->routeIs('admin.content.*') ? 'active' : '' }}">
                        <i class="fas fa-file-alt"></i>
                        <span>Content Management</span>
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="{{ route('admin.analytics') }}" class="nav-link {{ request()->routeIs('admin.analytics') ? 'active' : '' }}">
                        <i class="fas fa-chart-bar"></i>
                        <span>Analytics</span>
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="{{ route('admin.settings') }}" class="nav-link {{ request()->routeIs('admin.settings') ? 'active' : '' }}">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="{{ route('admin.activity-logs') }}" class="nav-link {{ request()->routeIs('admin.activity-logs') ? 'active' : '' }}">
                        <i class="fas fa-history"></i>
                        <span>Activity Logs</span>
                    </a>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="admin-content" id="adminContent">
            <!-- Header -->
            <header class="admin-header">
                <div class="d-flex align-items-center">
                    <button class="btn btn-link text-dark me-3" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div>
                        <h1 class="h4 mb-0">@stack('page-title')Dashboard</h1>
                    </div>
                </div>
                
                <div class="d-flex align-items-center">
                    <!-- Notifications -->
                    <div class="dropdown me-3">
                        <button class="btn btn-link text-dark position-relative" data-bs-toggle="dropdown">
                            <i class="fas fa-bell"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                3
                            </span>
                        </button>
                        <div class="dropdown-menu dropdown-menu-end">
                            <h6 class="dropdown-header">Notifications</h6>
                            <a class="dropdown-item" href="#">New user registered</a>
                            <a class="dropdown-item" href="#">System update available</a>
                            <a class="dropdown-item" href="#">Backup completed</a>
                        </div>
                    </div>
                    
                    <!-- User Menu -->
                    <div class="dropdown">
                        <button class="btn btn-link text-dark d-flex align-items-center" data-bs-toggle="dropdown">
                            <img src="https://ui-avatars.com/api/?name={{ urlencode(Auth::user()->name) }}&background=667eea&color=fff" 
                                 class="rounded-circle me-2" width="32" height="32" alt="Avatar">
                            <span>{{ Auth::user()->name }}</span>
                            <i class="fas fa-chevron-down ms-2"></i>
                        </button>
                        <div class="dropdown-menu dropdown-menu-end">
                            <a class="dropdown-item" href="{{ route('admin.profile') }}">
                                <i class="fas fa-user me-2"></i>Profile
                            </a>
                            <a class="dropdown-item" href="{{ route('admin.settings') }}">
                                <i class="fas fa-cog me-2"></i>Settings
                            </a>
                            <div class="dropdown-divider"></div>
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit" class="dropdown-item">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Main Content Area -->
            <main class="admin-main">
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @yield('content')
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <script src="{{ asset('assets/js/vendor/jquery-3.6.3.min.js') }}"></script>
    <script src="{{ asset('assets/js/vendor/bootstrap.min.js') }}"></script>
    
    <script>
        // Sidebar toggle functionality
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            const sidebar = document.getElementById('adminSidebar');
            const content = document.getElementById('adminContent');
            
            sidebar.classList.toggle('collapsed');
            content.classList.toggle('expanded');
        });

        // Auto-dismiss alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>

    @stack('scripts')
</body>
</html>
