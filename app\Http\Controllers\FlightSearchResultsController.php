<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\FlightApiService;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;

class FlightSearchResultsController extends Controller
{
    protected $flightApiService;

    public function __construct(FlightApiService $flightApiService)
    {
        $this->flightApiService = $flightApiService;
    }

    /**
     * Display flight search results
     */
    public function index(Request $request)
    {
        $searchParams = $request->all();
        
        // Validate search parameters
        $request->validate([
            'departure_airport' => 'required|string|size:3',
            'arrival_airport' => 'required|string|size:3',
            'departure_date' => 'required|date|after_or_equal:today',
            'return_date' => 'nullable|date|after:departure_date',
            'passengers' => 'required|integer|min:1|max:9',
            'cabin_class' => 'required|in:economy,premium_economy,business,first'
        ]);

        try {
            // Search for flights
            $flights = $this->flightApiService->searchFlights($searchParams);
            
            // Get airport information
            $departureAirport = $this->getAirportInfo($searchParams['departure_airport']);
            $arrivalAirport = $this->getAirportInfo($searchParams['arrival_airport']);
            
            // Store search parameters in session for booking
            Session::put('flight_search_params', $searchParams);
            
            return view('flight-search.results', compact(
                'flights', 
                'searchParams', 
                'departureAirport', 
                'arrivalAirport'
            ));
            
        } catch (\Exception $e) {
            Log::error('Flight search failed: ' . $e->getMessage());
            
            return redirect()->back()
                ->withErrors(['search' => 'Flight search failed. Please try again.'])
                ->withInput();
        }
    }

    /**
     * Get airport information
     */
    protected function getAirportInfo($code)
    {
        // Mock airport data - in real implementation, this would come from database or API
        $airports = [
            'ISB' => ['code' => 'ISB', 'name' => 'Islamabad International Airport', 'city' => 'Islamabad'],
            'BAH' => ['code' => 'BAH', 'name' => 'Bahrain International Airport', 'city' => 'Manama'],
            'SHJ' => ['code' => 'SHJ', 'name' => 'Sharjah International Airport', 'city' => 'Sharjah'],
            'DXB' => ['code' => 'DXB', 'name' => 'Dubai International Airport', 'city' => 'Dubai'],
            'LHE' => ['code' => 'LHE', 'name' => 'Allama Iqbal International Airport', 'city' => 'Lahore'],
            'KHI' => ['code' => 'KHI', 'name' => 'Jinnah International Airport', 'city' => 'Karachi'],
        ];
        
        return $airports[$code] ?? ['code' => $code, 'name' => $code, 'city' => $code];
    }

    /**
     * Filter flights by airline
     */
    public function filterByAirline(Request $request)
    {
        $searchParams = Session::get('flight_search_params');
        $airline = $request->input('airline');
        
        if (!$searchParams) {
            return redirect()->route('home')->withErrors(['search' => 'Please perform a new search.']);
        }
        
        $flights = $this->flightApiService->searchFlights($searchParams);
        
        if ($airline && $airline !== 'all') {
            $flights = array_filter($flights, function($flight) use ($airline) {
                return $flight['airline_code'] === $airline;
            });
        }
        
        return response()->json(['flights' => array_values($flights)]);
    }

    /**
     * Filter flights by price range
     */
    public function filterByPrice(Request $request)
    {
        $searchParams = Session::get('flight_search_params');
        $minPrice = $request->input('min_price', 0);
        $maxPrice = $request->input('max_price', 999999);
        
        if (!$searchParams) {
            return redirect()->route('home')->withErrors(['search' => 'Please perform a new search.']);
        }
        
        $flights = $this->flightApiService->searchFlights($searchParams);
        
        $flights = array_filter($flights, function($flight) use ($minPrice, $maxPrice) {
            return $flight['price'] >= $minPrice && $flight['price'] <= $maxPrice;
        });
        
        return response()->json(['flights' => array_values($flights)]);
    }

    /**
     * Sort flights
     */
    public function sortFlights(Request $request)
    {
        $searchParams = Session::get('flight_search_params');
        $sortBy = $request->input('sort_by', 'price');
        $sortOrder = $request->input('sort_order', 'asc');
        
        if (!$searchParams) {
            return redirect()->route('home')->withErrors(['search' => 'Please perform a new search.']);
        }
        
        $flights = $this->flightApiService->searchFlights($searchParams);
        
        usort($flights, function($a, $b) use ($sortBy, $sortOrder) {
            $result = 0;
            
            switch ($sortBy) {
                case 'price':
                    $result = $a['price'] <=> $b['price'];
                    break;
                case 'duration':
                    $result = $a['duration_minutes'] <=> $b['duration_minutes'];
                    break;
                case 'departure_time':
                    $result = strtotime($a['departure_time']) <=> strtotime($b['departure_time']);
                    break;
                case 'arrival_time':
                    $result = strtotime($a['arrival_time']) <=> strtotime($b['arrival_time']);
                    break;
            }
            
            return $sortOrder === 'desc' ? -$result : $result;
        });
        
        return response()->json(['flights' => $flights]);
    }
}
