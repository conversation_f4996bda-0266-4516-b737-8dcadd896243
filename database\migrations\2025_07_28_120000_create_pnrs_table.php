<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pnrs', function (Blueprint $table) {
            $table->id();
            $table->string('pnr_code', 6)->unique(); // e.g., 'ABC123'
            $table->foreignId('flight_id')->constrained()->onDelete('cascade');
            $table->integer('total_inventory_seats'); // Total seats added to database
            $table->integer('live_seats'); // Subset of inventory seats available for sale
            $table->integer('booked_seats')->default(0); // Number of seats actually booked
            $table->enum('status', ['inventory_only', 'available', 'sold_out', 'cancelled'])->default('inventory_only');
            $table->decimal('base_price', 10, 2); // Base price per seat
            $table->string('currency', 3)->default('PKR');
            $table->text('notes')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamp('live_date')->nullable(); // When seats were made live
            $table->timestamp('sold_out_date')->nullable(); // When all live seats were sold
            $table->timestamps();

            // Indexes for better performance
            $table->index('pnr_code');
            $table->index(['flight_id', 'status']);
            $table->index(['status', 'is_active']);
            $table->index('live_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pnrs');
    }
};
