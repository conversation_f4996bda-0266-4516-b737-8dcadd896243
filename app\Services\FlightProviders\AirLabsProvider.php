<?php

namespace App\Services\FlightProviders;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class AirLabsProvider implements FlightProviderInterface
{
    protected array $config;
    protected string $baseUrl;
    protected string $apiKey;

    public function __construct(array $config)
    {
        $this->config = $config;
        $this->baseUrl = $config['base_url'];
        $this->apiKey = $config['api_key'];
    }

    public function isEnabled(): bool
    {
        return $this->config['enabled'] ?? false;
    }

    public function searchFlights(array $params): array
    {
        try {
            // AirLabs doesn't provide flight search, so we'll use it for airport data
            // and generate mock flights based on real airport data
            $departureAirport = $this->getAirportInfo($params['departure_airport']);
            $arrivalAirport = $this->getAirportInfo($params['arrival_airport']);

            if (!$departureAirport || !$arrivalAirport) {
                return $this->generateMockFlights($params);
            }

            // Generate realistic flights based on real airport data
            return $this->generateRealisticFlights($params, $departureAirport, $arrivalAirport);

        } catch (Exception $e) {
            Log::error('AirLabs flight search error: ' . $e->getMessage());
            return $this->generateMockFlights($params);
        }
    }

    public function searchAirports(string $query): array
    {
        try {
            if ($this->apiKey === 'demo-key') {
                return $this->getMockAirports($query);
            }

            $response = Http::timeout($this->config['timeout'])
                ->get($this->baseUrl . '/airports', [
                    'api_key' => $this->apiKey,
                    'name' => $query,
                    '_limit' => 10
                ]);

            if ($response->successful()) {
                $data = $response->json();
                return $this->formatAirports($data['response'] ?? []);
            }

            return $this->getMockAirports($query);

        } catch (Exception $e) {
            Log::error('AirLabs airport search error: ' . $e->getMessage());
            return $this->getMockAirports($query);
        }
    }

    public function getFlightDetails(string $flightId): ?array
    {
        // Mock implementation for flight details
        return [
            'id' => $flightId,
            'status' => 'available',
            'provider' => 'airlabs',
            'last_updated' => now()->toISOString()
        ];
    }

    protected function getAirportInfo(string $code): ?array
    {
        try {
            if ($this->apiKey === 'demo-key') {
                return $this->getMockAirportInfo($code);
            }

            $response = Http::timeout($this->config['timeout'])
                ->get($this->baseUrl . '/airports', [
                    'api_key' => $this->apiKey,
                    'iata_code' => $code
                ]);

            if ($response->successful()) {
                $data = $response->json();
                return $data['response'][0] ?? null;
            }

            return $this->getMockAirportInfo($code);

        } catch (Exception $e) {
            Log::error('AirLabs airport info error: ' . $e->getMessage());
            return $this->getMockAirportInfo($code);
        }
    }

    protected function formatAirports(array $airports): array
    {
        $formatted = [];
        
        foreach ($airports as $airport) {
            $formatted[] = [
                'code' => $airport['iata_code'] ?? $airport['icao_code'] ?? '',
                'name' => $airport['name'] ?? '',
                'city' => $airport['city'] ?? '',
                'country' => $airport['country_code'] ?? '',
                'provider' => 'airlabs'
            ];
        }

        return $formatted;
    }

    protected function generateRealisticFlights(array $params, array $depAirport, array $arrAirport): array
    {
        $flights = [];
        $numFlights = rand(3, 8);

        $airlines = [
            'AA' => 'American Airlines',
            'DL' => 'Delta Air Lines', 
            'UA' => 'United Airlines',
            'BA' => 'British Airways',
            'EK' => 'Emirates',
            'LH' => 'Lufthansa'
        ];

        for ($i = 0; $i < $numFlights; $i++) {
            $airline = array_rand($airlines);
            $flightNumber = $airline . rand(100, 9999);
            
            $departureTime = $this->generateDepartureTime();
            $duration = $this->calculateRealisticDuration($depAirport, $arrAirport);
            $stops = rand(0, 2);
            $price = $this->calculateRealisticPrice($params, $duration, $stops);

            $flights[] = [
                'id' => 'airlabs_' . uniqid(),
                'flight_number' => $flightNumber,
                'airline' => $airlines[$airline],
                'airline_code' => $airline,
                'departure_airport' => $params['departure_airport'],
                'arrival_airport' => $params['arrival_airport'],
                'departure_time' => $departureTime,
                'arrival_time' => $this->calculateArrivalTime($departureTime, $duration),
                'departure_date' => $params['departure_date'],
                'duration' => $this->formatDuration($duration),
                'stops' => $stops,
                'price' => $price,
                'currency' => 'USD',
                'cabin_class' => $params['cabin_class'] ?? 'economy',
                'available_seats' => rand(5, 30),
                'provider' => 'airlabs',
                'booking_url' => '#'
            ];
        }

        return $flights;
    }

    protected function generateMockFlights(array $params): array
    {
        // Fallback to mock data if API fails
        $mockProvider = new MockProvider(['enabled' => true, 'max_results' => 10]);
        return $mockProvider->searchFlights($params);
    }

    protected function getMockAirports(string $query): array
    {
        $airports = [
            'JFK' => ['name' => 'John F. Kennedy International Airport', 'city' => 'New York', 'country' => 'US'],
            'LAX' => ['name' => 'Los Angeles International Airport', 'city' => 'Los Angeles', 'country' => 'US'],
            'LHR' => ['name' => 'London Heathrow Airport', 'city' => 'London', 'country' => 'GB'],
            'CDG' => ['name' => 'Charles de Gaulle Airport', 'city' => 'Paris', 'country' => 'FR'],
            'DXB' => ['name' => 'Dubai International Airport', 'city' => 'Dubai', 'country' => 'AE'],
            'NRT' => ['name' => 'Narita International Airport', 'city' => 'Tokyo', 'country' => 'JP'],
            'SYD' => ['name' => 'Sydney Kingsford Smith Airport', 'city' => 'Sydney', 'country' => 'AU'],
            'SIN' => ['name' => 'Singapore Changi Airport', 'city' => 'Singapore', 'country' => 'SG'],
        ];

        $results = [];
        $query = strtolower($query);

        foreach ($airports as $code => $airport) {
            if (
                strpos(strtolower($code), $query) !== false ||
                strpos(strtolower($airport['name']), $query) !== false ||
                strpos(strtolower($airport['city']), $query) !== false
            ) {
                $results[] = [
                    'code' => $code,
                    'name' => $airport['name'],
                    'city' => $airport['city'],
                    'country' => $airport['country'],
                    'provider' => 'airlabs'
                ];
            }
        }

        return array_slice($results, 0, 10);
    }

    protected function getMockAirportInfo(string $code): ?array
    {
        $airports = [
            'JFK' => ['name' => 'John F. Kennedy International Airport', 'city' => 'New York', 'lat' => 40.6413, 'lng' => -73.7781],
            'LAX' => ['name' => 'Los Angeles International Airport', 'city' => 'Los Angeles', 'lat' => 33.9425, 'lng' => -118.4081],
            'LHR' => ['name' => 'London Heathrow Airport', 'city' => 'London', 'lat' => 51.4700, 'lng' => -0.4543],
            'CDG' => ['name' => 'Charles de Gaulle Airport', 'city' => 'Paris', 'lat' => 49.0097, 'lng' => 2.5479],
            'DXB' => ['name' => 'Dubai International Airport', 'city' => 'Dubai', 'lat' => 25.2532, 'lng' => 55.3657],
        ];

        return $airports[$code] ?? null;
    }

    protected function generateDepartureTime(): string
    {
        $hours = range(6, 22);
        $minutes = [0, 15, 30, 45];
        
        $hour = $hours[array_rand($hours)];
        $minute = $minutes[array_rand($minutes)];
        
        return sprintf('%02d:%02d', $hour, $minute);
    }

    protected function calculateRealisticDuration(array $depAirport, array $arrAirport): int
    {
        // Simple distance-based duration calculation
        if (isset($depAirport['lat']) && isset($arrAirport['lat'])) {
            $distance = $this->calculateDistance(
                $depAirport['lat'], $depAirport['lng'],
                $arrAirport['lat'], $arrAirport['lng']
            );
            
            // Rough estimate: 500 mph average speed + 30 min for takeoff/landing
            return max(60, round(($distance / 500) * 60) + 30);
        }

        return rand(120, 480); // 2-8 hours fallback
    }

    protected function calculateDistance(float $lat1, float $lng1, float $lat2, float $lng2): float
    {
        $earthRadius = 3959; // miles
        
        $dLat = deg2rad($lat2 - $lat1);
        $dLng = deg2rad($lng2 - $lng1);
        
        $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLng/2) * sin($dLng/2);
        $c = 2 * atan2(sqrt($a), sqrt(1-$a));
        
        return $earthRadius * $c;
    }

    protected function calculateRealisticPrice(array $params, int $duration, int $stops): float
    {
        $basePrice = 150 + ($duration * 0.8); // Base price increases with duration
        
        $classMultipliers = [
            'economy' => 1.0,
            'premium_economy' => 1.6,
            'business' => 3.2,
            'first' => 6.0
        ];
        
        $class = $params['cabin_class'] ?? 'economy';
        $price = $basePrice * ($classMultipliers[$class] ?? 1.0);
        
        // Stops discount
        $price *= (1 - ($stops * 0.12));
        
        // Add some randomness
        $price *= (0.8 + (rand(0, 40) / 100));
        
        return round($price, 2);
    }

    protected function calculateArrivalTime(string $departureTime, int $durationMinutes): string
    {
        $departure = \DateTime::createFromFormat('H:i', $departureTime);
        $arrival = clone $departure;
        $arrival->add(new \DateInterval('PT' . $durationMinutes . 'M'));
        
        return $arrival->format('H:i');
    }

    protected function formatDuration(int $minutes): string
    {
        $hours = floor($minutes / 60);
        $mins = $minutes % 60;
        return sprintf('%dh %02dm', $hours, $mins);
    }

    /**
     * Get real-time flight prices
     */
    public function getFlightPrices(array $flightIds): array
    {
        // AirLabs doesn't provide real-time pricing, return mock data
        $prices = [];
        foreach ($flightIds as $flightId) {
            $prices[$flightId] = [
                'price' => rand(200, 1500),
                'currency' => 'USD',
                'last_updated' => now()->toISOString(),
                'provider' => 'airlabs_mock'
            ];
        }
        return $prices;
    }

    /**
     * Book a flight
     */
    public function bookFlight(array $bookingData): array
    {
        // AirLabs doesn't provide booking, return mock response
        return [
            'success' => false,
            'message' => 'Booking not available through AirLabs provider',
            'provider' => 'airlabs'
        ];
    }

    /**
     * Get airline information
     */
    public function getAirlineInfo(string $airlineCode): ?array
    {
        try {
            $response = Http::timeout(10)->get($this->baseUrl . '/airlines', [
                'api_key' => $this->apiKey,
                'iata_code' => $airlineCode
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['response'][0])) {
                    $airline = $data['response'][0];
                    return [
                        'code' => $airline['iata_code'] ?? $airlineCode,
                        'name' => $airline['name'] ?? 'Unknown Airline',
                        'country' => $airline['country_code'] ?? 'Unknown',
                        'fleet_size' => $airline['fleet_size'] ?? null,
                        'founded' => $airline['founded'] ?? null,
                        'provider' => 'airlabs'
                    ];
                }
            }
        } catch (Exception $e) {
            Log::error('AirLabs airline info error: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Check flight availability
     */
    public function checkAvailability(string $flightId): bool
    {
        // AirLabs doesn't provide real-time availability, return mock
        return rand(0, 10) > 1; // 90% chance of availability
    }

    /**
     * Get booking status
     */
    public function getBookingStatus(string $bookingReference): ?array
    {
        // AirLabs doesn't provide booking status, return null
        return null;
    }
}
