<?php

namespace App\Services\FlightProviders;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class TravelPayoutsProvider implements FlightProviderInterface
{
    protected array $config;

    public function __construct(array $config)
    {
        $this->config = $config;
    }

    public function isEnabled(): bool
    {
        return $this->config['enabled'] ?? false;
    }

    /**
     * Make request to TravelPayouts API
     */
    protected function makeRequest(string $endpoint, array $params = []): array
    {
        $params['token'] = $this->config['api_key'];
        
        $response = Http::timeout($this->config['timeout'])
            ->get($this->config['base_url'] . $endpoint, $params);

        if ($response->successful()) {
            return $response->json();
        }

        throw new Exception('TravelPayouts API request failed: ' . $response->body());
    }

    /**
     * Search for flights
     */
    public function searchFlights(array $searchParams): array
    {
        try {
            $params = [
                'origin' => $searchParams['departure_airport'],
                'destination' => $searchParams['arrival_airport'],
                'depart_date' => $searchParams['departure_date'],
                'currency' => $searchParams['currency'] ?? 'USD',
            ];

            if (!empty($searchParams['return_date'])) {
                $params['return_date'] = $searchParams['return_date'];
            }

            // Use the cheap flights endpoint for basic search
            $response = $this->makeRequest('/v1/prices/cheap', $params);
            
            return $this->transformFlightResults($response);
        } catch (Exception $e) {
            Log::error('TravelPayouts flight search failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Transform TravelPayouts results to standard format
     */
    protected function transformFlightResults(array $response): array
    {
        $flights = [];
        $data = $response['data'] ?? [];

        foreach ($data as $route => $flightData) {
            if (!is_array($flightData)) continue;

            foreach ($flightData as $flight) {
                $flights[] = [
                    'id' => uniqid('tp_'),
                    'airline' => $flight['airline'] ?? '',
                    'flight_number' => ($flight['airline'] ?? '') . ($flight['flight_number'] ?? ''),
                    'departure_airport' => $flight['origin'] ?? '',
                    'arrival_airport' => $flight['destination'] ?? '',
                    'departure_time' => $flight['depart_date'] ?? '',
                    'arrival_time' => $flight['return_date'] ?? '',
                    'duration' => 'PT2H30M', // Default duration
                    'stops' => $flight['transfers'] ?? 0,
                    'price' => (float) ($flight['value'] ?? 0),
                    'currency' => $flight['currency'] ?? 'USD',
                    'cabin_class' => 'economy',
                    'available_seats' => 9,
                    'baggage_info' => [
                        'carry_on' => '1 piece',
                        'checked' => 'Varies by airline',
                    ],
                    'cancellation_policy' => 'Please check with airline',
                    'raw_data' => $flight,
                ];
            }
        }

        return $flights;
    }

    /**
     * Get flight details by ID
     */
    public function getFlightDetails(string $flightId): ?array
    {
        return null;
    }

    /**
     * Get real-time flight prices
     */
    public function getFlightPrices(array $flightIds): array
    {
        return [];
    }

    /**
     * Book a flight
     */
    public function bookFlight(array $bookingData): array
    {
        throw new Exception('Direct booking not supported by TravelPayouts API');
    }

    /**
     * Search airports
     */
    public function searchAirports(string $query): array
    {
        try {
            $params = [
                'term' => $query,
                'locale' => 'en',
            ];

            $response = $this->makeRequest('/data/en/cities.json', $params);
            
            return array_map(function ($city) {
                return [
                    'code' => $city['code'] ?? '',
                    'name' => $city['name'] ?? '',
                    'city' => $city['name'] ?? '',
                    'country' => $city['country_code'] ?? '',
                    'timezone' => $city['time_zone'] ?? '',
                ];
            }, array_slice($response, 0, 20));
        } catch (Exception $e) {
            Log::error('TravelPayouts airport search failed: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get airline information
     */
    public function getAirlineInfo(string $airlineCode): ?array
    {
        try {
            $response = $this->makeRequest('/data/en/airlines.json');
            
            foreach ($response as $airline) {
                if ($airline['iata_code'] === $airlineCode || $airline['icao_code'] === $airlineCode) {
                    return [
                        'code' => $airline['iata_code'],
                        'name' => $airline['name'],
                        'country' => '',
                    ];
                }
            }
            
            return null;
        } catch (Exception $e) {
            Log::error('TravelPayouts airline info failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Check flight availability
     */
    public function checkAvailability(string $flightId): bool
    {
        return true;
    }

    /**
     * Get booking status
     */
    public function getBookingStatus(string $bookingReference): ?array
    {
        throw new Exception('Booking status not supported by TravelPayouts API');
    }
}
