<!DOCTYPE html>
<html lang="en">


<!-- Mirrored from uiparadox.co.uk/templates/flynow/flight-listing.html by HTTrack Website Copier/3.x [XR&CO'2014], Thu, 03 Jul 2025 14:26:54 GMT -->
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="FlyNow HTML5 Template">

    <title>FlyNow | Travling And Tours Template</title>

    <!-- Favicon -->
    <link rel="shortcut icon" type="image/x-icon" href="assets/media/favicon.png">

    <!-- All CSS files -->
    <link rel="stylesheet" href="assets/css/vendor/font-awesome.css">
    <link rel="stylesheet" href="assets/css/fonts/icomoon/style.css">
    <link rel="stylesheet" href="assets/css/vendor/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/vendor/slick.css">
    <link rel="stylesheet" href="assets/css/vendor/slick-theme.css">
    <link rel="stylesheet" href="assets/sass/helpers/_search-popup.html">
    <link rel="stylesheet" href="assets/css/vendor/aksVideoPlayer.css">
    <link rel="stylesheet" href="assets/css/vendor/datetime.css">
    <link rel="stylesheet" href="assets/css/vendor/calendar/classic.css">
    <link rel="stylesheet" href="assets/css/vendor/calendar/classic.date.css">
    <link rel="stylesheet" href="assets/css/vendor/jquery.timepicker.min.css">
    <link rel="stylesheet" href="assets/css/vendor/ui-autocomplete.css">
    <link rel="stylesheet" href="assets/css/vendor/ionrangeslider.css">
    <link rel="stylesheet" href="assets/css/vendor/sal.css">

    <link rel="stylesheet" href="assets/css/app.css">
</head>

<body class="o-scroll" id="js-scroll">
    <!-- Preloader-->
    <div id="preloader">
        <div class="loader">
            <div class="plane">
                <img src="../../../www.zupimages.net/viewerfdbe.html" class="plane-img" alt="">
            </div>
            <div class="earth-wrapper">
                <div class="earth"></div>
            </div>
        </div>
    </div>
    <!-- Back To Top Start -->
    <a href="#main-wrapper" id="backto-top" class="back-to-top"><i class="fas fa-angle-up"></i></a>
    <!-- Main Wrapper Start -->
    <div id="main-wrapper" class="main-wrapper overflow-hidden">

        <!-- Header Area Start -->
        <header >
            <nav class="main-menu">
                <div class="container-fluid">
                    <div class="main-menu__block">
                        <div class="main-menu__left">

                            <div class="main-menu__logo">
                                <a href="index.html">
                                    <img src="assets/media/logo.png" alt="FlyNow">
                                </a>
                            </div>

                            <div class="main-menu__nav">
                                <ul class="main-menu__list">
                                    <li><a href="index.html">Home</a></li>
                                    <li class="dropdown">
                                        <a href="javascript:void(0);" class="active">Flight</a>
                                        <ul class="sub-menu">
                                            <li><a href="flight-listing.html" class="active">Flight Listing</a></li>
                                            <li><a href="flight-booking.html">Flight Booking</a></li>
                                        </ul>
                                    </li>
                                    <li class="dropdown">
                                        <a href="javascript:void(0);">Car</a>
                                        <ul class="sub-menu">
                                            <li><a href="car-listing.html">Car Listing</a></li>
                                            <li><a href="car-booking.html">Car Booking</a></li>
                                            <li><a href="car-detail.html">Car Detail</a></li>
                                        </ul>
                                    </li>
                                    <li class="dropdown">
                                        <a href="javascript:void(0);">Hotel</a>
                                        <ul class="sub-menu">
                                            <li><a href="hotel-listing.html">Hotel Listing</a></li>
                                            <li><a href="hotel-booking.html">Hotel Booking</a></li>
                                            <li><a href="hotel-detail.html">Hotel Detail</a></li>
                                        </ul>
                                    </li>
                                    <li class="dropdown">
                                        <a href="javascript:void(0);">Tour</a>
                                        <ul>
                                            <li><a href="tour-packages.html">Tour Packages</a></li>
                                            <li><a href="tour-detail.html">Tour Detail</a></li>
                                        </ul>
                                    </li>
                                    <li class="dropdown">
                                        <a href="javascript:void(0);">Pages</a>
                                        <ul class="sub-menu">
                                            <li><a href="about-us.html">About</a></li>
                                            <li><a href="contact.html">Contact</a></li>
                                            <li><a href="privacy-policy.html">Privacy Policy</a></li>
                                            <li><a href="login.html">Login</a></li>
                                            <li><a href="sign-up.html">Signup</a></li>
                                        </ul>
                                    </li>
                                    <li class="dropdown">
                                        <a href="javascript:void(0);">News</a>
                                        <ul>
                                            
                                            <li><a href="blog-listing.html">News Listing</a></li>
                                            <li><a href="blog-detail.html">News Detail</a></li>
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="main-menu__right">
                            <a href="#" class="main-menu__search search-toggler d-xl-flex d-none">
                                <i class="fal fa-search"></i>
                            </a>
                            <div class="main-menu-signup__login d-xl-flex d-none">
                                <a href="login.html" class="main-menu__login">
                                    Login
                                </a>
                                <div class="center_slach d-xl-flex d-none">/</div>
                                <a href="sign-up.html" class="main-menu__login">
                                    Signup
                                </a>
                            </div>
                            <a href="#" class="main-menu__toggler mobile-nav__toggler">
                                <i class="fa fa-bars"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </nav>
        </header>
        <!-- Header Area end -->


        <!-- Title-Banner Start -->
        <div class="title-banner">
            <div class="container-fluid">
                <div class="row">
                    <div class="banner-area v-2">
                        <img src="assets/media/banner/vacation.png" alt="" class="left-image">
                        <div class="content-box text-alignment">
                            <h1 class="fw-700 lightest-black">Flight Listing</h1>
                        </div>
                        <img src="assets/media/banner/plane-2.png" alt="" class="right-image">
                    </div>
                </div>
            </div>
        </div>
        <!-- Title-Banner End -->

        <!-- Main Content Start -->
        <div class="page-content m-0">

            <!-- Booking Area Start -->
            <section class="booking mb-60">
                <div class="container-fluid">
                    <div class="content">

                        <div class="card">
                            <div class="card-header">
                                <ul class="nav nav-tabs card-header-tabs " data-bs-tabs="tabs">
                                    <li class="nav-item flight-sec">
                                        <a href="#flight" class="cus-btn primary-light primary active"
                                            aria-current="true" data-bs-toggle="tab">
                                            Flight
                                        </a>
                                    </li>
                                    <li class="nav-item flight-sec">
                                        <a href="#stopover" class="cus-btn primary-light primary" aria-current="false"
                                            data-bs-toggle="tab">
                                            Stopover
                                        </a>
                                    </li>
                                    <li class="nav-item flight-sec">
                                        <a href="#manage" class="cus-btn primary-light primary" aria-current="false"
                                            data-bs-toggle="tab">
                                            Manage Booking
                                        </a>
                                    </li>
                                    <li class="nav-item flight-sec">
                                        <a href="#status" class="cus-btn primary-light primary" aria-current="false"
                                            data-bs-toggle="tab">
                                            Flight Status
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-body tab-content">

                                <!-- Flight tab Start -->
                                <div class="tab-pane fade show active" id="flight">
                                    <form action="https://uiparadox.co.uk/templates/flynow/flight-listing.html" method="post">
                                        <div class="custom-control mb-32">
                                            <div class=" radio-button">
                                                <input type="radio" name="way" class="custom-control-input" id="one">
                                                <label class="custom-control-label" for="one">One way</label>
                                            </div>
                                            <div class=" radio-button">
                                                <input type="radio" name="way" class="custom-control-input" id="round">
                                                <label class="custom-control-label" for="round">Round-trip</label>
                                            </div>
                                            <div class="radio-button">
                                                <input type="radio" name="way" class="custom-control-input" id="multi">
                                                <label class="custom-control-label" for="multi">Multi-City</label>
                                            </div>
                                        </div>
                                        <div class="booking-info mb-32" data-sal="slide-left" data-sal-duration="800" data-sal-delay="100" data-sal-easing="ease-in-out">
                                            <div class="d-xl-flex align-items-center gap-24 d-lg-block d-flex">
                                                <div class="custom-sel-input-block">
                                                    <label for="depFrom" class="h6 color-medium-gray">From</label>
                                                    <input type="text" class="sel-input auto-input" id="depFrom"
                                                        placeholder="From">
                                                    <div class="slector-wrapper"></div>
                                                </div>
                                                <div class="arrows">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none">
                                                        <g clip-path="url(#clip0_518_2277)">
                                                            <path
                                                                d="M23.6804 6.07409L18.2259 0.619583C17.7999 0.193537 17.1092 0.193537 16.6831 0.619583C16.2571 1.04563 16.2571 1.73628 16.6831 2.16233L20.2754 5.75464H1.09096C0.488472 5.75464 3.51626e-05 6.24307 3.51626e-05 6.84556C3.51626e-05 7.44804 0.488472 7.93648 1.09096 7.93648H20.2754L16.6832 11.5287C16.2571 11.9548 16.2571 12.6455 16.6832 13.0715C16.8961 13.2845 17.1753 13.391 17.4545 13.391C17.7337 13.391 18.0129 13.2845 18.2258 13.0714L23.6804 7.61688C24.1064 7.19084 24.1064 6.50013 23.6804 6.07409Z"
                                                                fill="#4D73FC"/>
                                                            <path
                                                                d="M22.9091 16.6637H3.72462L7.31683 13.0714C7.74288 12.6453 7.74288 11.9547 7.31683 11.5286C6.89088 11.1026 6.20013 11.1026 5.77409 11.5286L0.319535 16.9831C-0.106512 17.4092 -0.106512 18.0998 0.319535 18.5259L5.77404 23.9804C5.98713 24.1935 6.26627 24.3 6.54546 24.3C6.82465 24.3 7.10388 24.1935 7.31679 23.9804C7.74283 23.5544 7.74283 22.8637 7.31679 22.4377L3.72457 18.8455H22.9091C23.5116 18.8455 24 18.357 24 17.7546C24 17.1521 23.5116 16.6637 22.9091 16.6637Z"
                                                                fill="#4D73FC"/>
                                                        </g>
                                                        <defs>
                                                            <clipPath id="clip0_518_2277">
                                                                <rect width="24" height="24" fill="white" />
                                                            </clipPath>
                                                        </defs>
                                                    </svg>
                                                </div>
                                                <div class="custom-sel-input-block">
                                                    <label for="arrivalTo" class="h6 color-medium-gray">To</label>
                                                    <input type="text" class="sel-input auto-input" id="arrivalTo"
                                                        placeholder="To">
                                                    <div class="slector-wrapper"></div>
                                                </div>
                                                <div class="vertical-line d-xl-block d-none"></div>
                                            </div>
                                            <div class="d-xl-flex align-items-center gap-24 d-lg-block ">
                                                <div class="input-date-picker">
                                                    <label for="dep" class="h6 color-medium-gray">Departing</label>
                                                    <input type="text" class="sel-input date_from" id="dep"
                                                        placeholder="Aug 18, 2023">
                                                </div>
                                                <div class="input-date-picker">
                                                    <label for="arrival" class="h6 color-medium-gray">Returning</label>
                                                    <input type="text" class="sel-input date_to" id="arrival"
                                                        placeholder="Aug 20, 2023">
                                                </div>
                                                <div class="custom-sel-input-block">
                                                    <div class="h6 color-medium-gray">Passengers and Class</div>
                                                    <div class="seat-booking color-black"> <span class="total-pasenger">2</span> Passengers /
                                                        <span class="pasenger-class">Business</span>
                                                    </div>

                                                    <div class="passenger-area pessenger-box bg-white light-shadow br-5 p-24">
                                                        <h4 class="color-black mb-32">Passenger</h4>
                                                        <div class="passenger-box mb-24">
                                                            <div class="row">
                                                                <div class="col-md-7 col-sm-6 col-6">
                                                                    <div class="content-box">
                                                                        <h5 class="lightest-black">Adult</h5>
                                                                        <p class="color-medium-gray light"> Above 12 years.</p>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-5 col-sm-6 col-6">
                                                                    <div class="quantity quantity-wrap">
                                                                        <input class="decrement" type="button" value="-">
                                                                        <input type="text" name="quantity" value="0" maxlength="2" size="1" id="adult" class="number">
                                                                        <input class="increment" type="button" value="+">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="passenger-box mb-24">
                                                            <div class="row">
                                                                <div class="col-md-7 col-sm-6 col-6">
                                                                    <div class="content-box">
                                                                        <h5 class="lightest-black">Child</h5>
                                                                        <p class="color-medium-gray light"> 2-11 years on travel date.</p>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-5 col-sm-6 col-6">
                                                                    <div class="quantity quantity-wrap">
                                                                        <input class="decrement" type="button" value="-">
                                                                        <input type="text" name="quantity" value="0" maxlength="2" size="1" id="child" class="number">
                                                                        <input class="increment" type="button" value="+">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="passenger-box mb-32">
                                                            <div class="row">
                                                                <div class="col-md-7 col-sm-6 col-6">
                                                                    <div class="content-box">
                                                                        <h5 class="lightest-black">Infant</h5>
                                                                        <p class="color-medium-gray light"> Below 2 years.</p>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-5 col-sm-6 col-6">
                                                                    <div class="quantity quantity-wrap">
                                                                        <input class="decrement" type="button" value="-">
                                                                        <input type="text" name="quantity" value="0" maxlength="2" size="1" id="infant" class="number">
                                                                        <input class="increment" type="button" value="+">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <p class="color-medium-gray light mb-32">Please
                                                            note: You can book for a maximum of nine
                                                            passengers.</p>
                                                        <h4 class="color-black mb-32">Travel Class</h4>
                                                        <div class="radio-tile-group">
                                                            <div class="input-container">
                                                                <input id="economy" class="radio-button"
                                                                    type="radio" name="radio"
                                                                    value="Economy">
                                                                <div class="radio-tile">
                                                                    <label for="economy"
                                                                        class="radio-tile-label">Economy</label>
                                                                </div>
                                                            </div>
                                                            <div class="input-container">
                                                                <input id="business" class="radio-button"
                                                                    type="radio" name="radio"
                                                                    value="Business">
                                                                <div class="radio-tile">
                                                                    <label for="business"
                                                                        class="radio-tile-label">Business</label>
                                                                </div>
                                                            </div>
                                                            <div class="input-container">
                                                                <input id="firstClass" class="radio-button"
                                                                    type="radio" name="radio"
                                                                    value="First Class">
                                                                <div class="radio-tile">
                                                                    <label for="firstClass"
                                                                        class="radio-tile-label">First
                                                                        Class</label>
                                                                </div>
                                                            </div>
                                                            <div class="input-container">
                                                                <input id="pre-eco" class="radio-button"
                                                                    type="radio" name="radio"
                                                                    value="Premium Economy">
                                                                <div class="radio-tile">
                                                                    <label for="pre-eco"
                                                                        class="radio-tile-label">Premium
                                                                        Economy</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row d-flex justify-content-end">
                                            <div class="col-xl-4 col-lg-6 col-md-6 col-sm-9">
                                                <div class="row align-items-center ">
                                                    <div class="col-sm-6">
                                                        <div class="booking-info promo-code mb-sm-0 mb-16">
                                                            <div class="custom-sel-input-block m-0">
                                                                <input type="text" class="sel-input p-0" id="promoCode" placeholder="Enter Promo Code">
                                                                <div class="slector-wrapper"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-6 d-flex justify-content-end">
                                                        <button type="submit" class="cus-btn">Show
                                                            Flight
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="24"
                                                                height="24" viewBox="0 0 32 32" fill="none">
                                                                <g clip-path="url(#clip0_502_1331)">
                                                                    <path
                                                                        d="M31.6933 0.544584C30.6572 -0.491824 27.1402 1.34503 26.1041 2.38143L21.9545 6.53127H3.07887C2.63024 6.53127 2.24462 6.85011 2.16055 7.29104C2.07669 7.73189 2.31798 8.16995 2.73524 8.3348L15.2174 13.2677L7.5633 20.9216H0.323909C0.168651 20.9221 0.0346723 21.0323 0.00576263 21.1852C-0.023357 21.3385 0.060152 21.4901 0.20498 21.5471L6.29687 23.9548C6.33201 24.1078 6.38108 24.2574 6.44394 24.4038L6.17745 24.6709C5.79778 25.0503 5.79778 25.6651 6.17745 26.045C6.55664 26.4247 7.17263 26.4247 7.55182 26.045L7.81194 25.785C7.95935 25.8501 8.11132 25.9014 8.26623 25.9382L10.6144 31.9561C10.6709 32.1013 10.8229 32.1851 10.976 32.1568C11.0419 32.145 11.1002 32.1123 11.1451 32.0673C11.2044 32.0087 11.2399 31.9274 11.2399 31.8382V24.7512L19.0155 16.976L23.9019 29.4993C24.0654 29.9177 24.5037 30.1608 24.9452 30.0781C25.136 30.0421 25.3038 29.9498 25.4333 29.8212C25.6038 29.6499 25.7071 29.4151 25.7077 29.1591V10.284L29.8573 6.13423C30.893 5.09789 32.7293 1.58085 31.6933 0.544584Z"
                                                                        fill="white" />
                                                                </g>
                                                                <defs>
                                                                    <clipPath id="clip0_502_1351">
                                                                        <rect width="32" height="32" fill="black" />
                                                                    </clipPath>
                                                                </defs>
                                                            </svg>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                <!-- Flight tab End -->

                                <!-- StopOver tab Start -->
                                <div class="tab-pane fade" id="stopover">
                                    <form action="https://uiparadox.co.uk/templates/flynow/flight-listing.html" method="post">
                                        <div class="custom-control mb-32">
                                            <div class=" radio-button">
                                                <input type="radio" name="way" class="custom-control-input" id="way">
                                                <label class="custom-control-label" for="way">One way</label>
                                            </div>
                                            <div class=" radio-button">
                                                <input type="radio" name="way" class="custom-control-input" id="trip">
                                                <label class="custom-control-label" for="trip">Round-trip</label>
                                            </div>
                                            <div class="radio-button">
                                                <input type="radio" name="way" class="custom-control-input" id="city">
                                                <label class="custom-control-label" for="city">Multi-City</label>
                                            </div>
                                        </div>
                                        <div class="booking-info mb-32">
                                            <div class="d-xl-flex align-items-center gap-24 d-lg-block d-flex">
                                                <div class="custom-sel-input-block">
                                                    <label for="flightFrom" class="h6 color-medium-gray">From</label>
                                                    <input type="text" class="sel-input auto-input" id="flightFrom"
                                                        placeholder="From">
                                                    <div class="slector-wrapper"></div>
                                                </div>
                                                <div class="arrows">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none">
                                                        <g clip-path="url(#clip0_518_2277)">
                                                            <path
                                                                d="M23.6804 6.07409L18.2259 0.619583C17.7999 0.193537 17.1092 0.193537 16.6831 0.619583C16.2571 1.04563 16.2571 1.73628 16.6831 2.16233L20.2754 5.75464H1.09096C0.488472 5.75464 3.51626e-05 6.24307 3.51626e-05 6.84556C3.51626e-05 7.44804 0.488472 7.93648 1.09096 7.93648H20.2754L16.6832 11.5287C16.2571 11.9548 16.2571 12.6455 16.6832 13.0715C16.8961 13.2845 17.1753 13.391 17.4545 13.391C17.7337 13.391 18.0129 13.2845 18.2258 13.0714L23.6804 7.61688C24.1064 7.19084 24.1064 6.50013 23.6804 6.07409Z"
                                                                fill="#4D73FC" />
                                                            <path
                                                                d="M22.9091 16.6637H3.72462L7.31683 13.0714C7.74288 12.6453 7.74288 11.9547 7.31683 11.5286C6.89088 11.1026 6.20013 11.1026 5.77409 11.5286L0.319535 16.9831C-0.106512 17.4092 -0.106512 18.0998 0.319535 18.5259L5.77404 23.9804C5.98713 24.1935 6.26627 24.3 6.54546 24.3C6.82465 24.3 7.10388 24.1935 7.31679 23.9804C7.74283 23.5544 7.74283 22.8637 7.31679 22.4377L3.72457 18.8455H22.9091C23.5116 18.8455 24 18.357 24 17.7546C24 17.1521 23.5116 16.6637 22.9091 16.6637Z"
                                                                fill="#4D73FC" />
                                                        </g>
                                                        <defs>
                                                            <clipPath id="clip0_518_2257">
                                                                <rect width="24" height="24" fill="white" />
                                                            </clipPath>
                                                        </defs>
                                                    </svg>
                                                </div>
                                                <div class="custom-sel-input-block">
                                                    <label for="flightGo" class="h6 color-medium-gray">To</label>
                                                    <input type="text" class="sel-input auto-input" id="flightGo" placeholder="To">
                                                    <div class="slector-wrapper"></div>
                                                </div>
                                                <div class="vertical-line d-xl-block d-none"></div>
                                            </div>
                                            <div class="d-xl-flex align-items-center gap-24 d-lg-block ">
                                                <div class="input-date-picker">
                                                    <label for="flightgone" class="h6 color-medium-gray">Departing</label>
                                                    <input type="text" class="sel-input date_from" id="flightgone" placeholder="Aug 18, 2023">
                                                </div>
                                                <div class="input-date-picker">
                                                    <label for="returnTime" class="h6 color-medium-gray">Returning</label>
                                                    <input type="text" class="sel-input date_to" id="returnTime" placeholder="Aug 20, 2023">
                                                </div>
                                                <div class="custom-sel-input-block">
                                                    <div class="h6 color-medium-gray" id="passengers">Passengers and Class</div>
                                                    <div class="seat-booking color-black"><span class="total-pasenger">2</span> Passengers /
                                                        <span class="pasenger-class">Business</span>
                                                    </div>
                                                    <div class="passenger-area bg-white light-shadow br-5 p-24">
                                                        <h4 class="color-black mb-32">Passenger</h4>
                                                        <div class="passenger-box mb-24">
                                                            <div class="row">
                                                                <div class="col-md-7 col-sm-6 col-6">
                                                                    <div class="content-box">
                                                                        <h5 class="lightest-black">Adult</h5>
                                                                        <p class="color-medium-gray light"> Above 12 years.</p>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-5 col-sm-6 col-6">
                                                                    <div class="quantity quantity-wrap">
                                                                        <input class="decrement" type="button" value="-">
                                                                        <input type="text" name="quantity" value="0" maxlength="2" size="1" id="quantityScale" class="number">
                                                                        <input class="increment" type="button" value="+">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="passenger-box mb-24">
                                                            <div class="row">
                                                                <div class="col-md-7 col-sm-6 col-6">
                                                                    <div class="content-box">
                                                                        <h5 class="lightest-black">Child
                                                                        </h5>
                                                                        <p class="color-medium-gray light">
                                                                            2-11 years on travel date.</p>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-5 col-sm-6 col-6">
                                                                    <div class="quantity quantity-wrap">
                                                                        <input class="decrement" type="button" value="-">
                                                                        <input type="text" name="quantity" value="0" maxlength="2" size="1" id="childScale" class="number">
                                                                        <input class="increment" type="button" value="+">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="passenger-box mb-32">
                                                            <div class="row">
                                                                <div class="col-md-7 col-sm-6 col-6">
                                                                    <div class="content-box">
                                                                        <h5 class="lightest-black">Infant
                                                                        </h5>
                                                                        <p class="color-medium-gray light">
                                                                            Below 2 years.</p>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-5 col-sm-6 col-6">
                                                                    <div class="quantity quantity-wrap">
                                                                        <input class="decrement" type="button" value="-">
                                                                        <input type="text" name="quantity" value="0"  maxlength="2" size="1" id="infantpass" class="number">
                                                                        <input class="increment" type="button" value="+">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <p class="color-medium-gray light mb-32">Please
                                                            note: You can book for a maximum of nine
                                                            passengers.</p>
                                                        <h4 class="color-black mb-32">Travel Class</h4>
                                                        <div class="radio-tile-group">
                                                            <div class="input-container">
                                                                <input id="economybtn" class="radio-button" type="radio" name="radio" value="Economy">
                                                                <div class="radio-tile">
                                                                    <label for="economybtn" class="radio-tile-label">Economy</label>
                                                                </div>
                                                            </div>
                                                            <div class="input-container">
                                                                <input id="businessradio" class="radio-button" type="radio" name="radio" value="Business">
                                                                <div class="radio-tile">
                                                                    <label for="businessradio" class="radio-tile-label">Business</label>
                                                                </div>
                                                            </div>
                                                            <div class="input-container">
                                                                <input id="firstClassradio" class="radio-button" type="radio" name="radio" value="First Class">
                                                                <div class="radio-tile">
                                                                    <label for="firstClassradio" class="radio-tile-label">First Class</label>
                                                                </div>
                                                            </div>
                                                            <div class="input-container">
                                                                <input id="pre-ecoBtn" class="radio-button" type="radio" name="radio" value="Premium Economy">
                                                                <div class="radio-tile">
                                                                    <label for="pre-ecoBtn" class="radio-tile-label">Premium Economy</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-xl-5 col-lg-5 col-md-5 col-sm-5 mb-md-0 mb-24">
                                                <h5 class="color-medium-gray mb-24">When would you like to
                                                    add a stop in dubai during your journey?</h5>
                                                <div class="custom-control ">
                                                    <div class=" radio-button">
                                                        <input type="radio" name="flight" class="custom-control-input"
                                                            id="departure">
                                                        <label class="custom-control-label"
                                                            for="departure">Departure</label>
                                                    </div>
                                                    <div class=" radio-button">
                                                        <input type="radio" name="flight" class="custom-control-input"
                                                            id="return">
                                                        <label class="custom-control-label" for="return">Return</label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div
                                                class="col-xl-5 col-lg-5 offset-lg-0 offset-md-2 col-md-5 offset-sm-2 col-sm-5">
                                                <h5 class="color-medium-gray mb-24">How many days would you
                                                    like to stay in Dubai?</h5>
                                                <div class="quantity quantity-wrap">
                                                    <input class="decrement" type="button" value="-">
                                                    <input type="text" name="quantity" value="0" maxlength="2" size="1" id="adt" class="number">
                                                    <input class="increment" type="button" value="+">
                                                </div>
                                            </div>
                                            <div class="col-xl-2 d-flex align-items-center">
                                                <button type="submit" class="cus-btn mt-lg-0 mt-32">Show
                                                    Flight
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 32 32" fill="none">
                                                        <g clip-path="url(#clip0_502_1331)">
                                                            <path
                                                                d="M31.6933 0.544584C30.6572 -0.491824 27.1402 1.34503 26.1041 2.38143L21.9545 6.53127H3.07887C2.63024 6.53127 2.24462 6.85011 2.16055 7.29104C2.07669 7.73189 2.31798 8.16995 2.73524 8.3348L15.2174 13.2677L7.5633 20.9216H0.323909C0.168651 20.9221 0.0346723 21.0323 0.00576263 21.1852C-0.023357 21.3385 0.060152 21.4901 0.20498 21.5471L6.29687 23.9548C6.33201 24.1078 6.38108 24.2574 6.44394 24.4038L6.17745 24.6709C5.79778 25.0503 5.79778 25.6651 6.17745 26.045C6.55664 26.4247 7.17263 26.4247 7.55182 26.045L7.81194 25.785C7.95935 25.8501 8.11132 25.9014 8.26623 25.9382L10.6144 31.9561C10.6709 32.1013 10.8229 32.1851 10.976 32.1568C11.0419 32.145 11.1002 32.1123 11.1451 32.0673C11.2044 32.0087 11.2399 31.9274 11.2399 31.8382V24.7512L19.0155 16.976L23.9019 29.4993C24.0654 29.9177 24.5037 30.1608 24.9452 30.0781C25.136 30.0421 25.3038 29.9498 25.4333 29.8212C25.6038 29.6499 25.7071 29.4151 25.7077 29.1591V10.284L29.8573 6.13423C30.893 5.09789 32.7293 1.58085 31.6933 0.544584Z"
                                                                fill="white" />
                                                        </g>
                                                        <defs>
                                                            <clipPath id="clip0_502_1341">
                                                                <rect width="32" height="32" fill="black" />
                                                            </clipPath>
                                                        </defs>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                <!-- StopOver tab End -->

                                <!-- Manage tab Start -->
                                <div class="tab-pane fade" id="manage">
                                    <div class="booking-bar">
                                        <ul class="nav nav-tabs " data-bs-tabs="tabs">
                                            <li class="nav-item">
                                                <a href="#manageBooking" class="nav-link active" aria-current="true"
                                                    data-bs-toggle="tab">
                                                    Manage Booking
                                                </a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="#checkIn" class="nav-link " aria-current="false"
                                                    data-bs-toggle="tab">
                                                    Check in
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="card-body tab-content">
                                        <div class="tab-pane fade show active" id="manageBooking">
                                            <form action="https://uiparadox.co.uk/templates/flynow/flight-listing.html" method="post">
                                                <div class="row align-items-center">
                                                    <div class="col-xl-10 col-lg-10 col-md-10">
                                                        <div class="booking-info d-block">
                                                            <div class="row">
                                                                <div class="col-lg-6 col-md-6 col-sm-6">
                                                                    <div class="custom-sel-input-block v-2">
                                                                        <input type="text" class="sel-input"
                                                                            name="reference" id="ref-name"
                                                                            placeholder="Booking Reference">
                                                                    </div>
                                                                </div>
                                                                <div class="col-lg-6 col-md-6 col-sm-6">
                                                                    <div class="custom-sel-input-block m-0">
                                                                        <input type="text" class="sel-input" name="name"
                                                                            id="last-name" placeholder="Last Name">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-xl-2 col-lg-2 col-md-2 mt-md-0 mt-32">
                                                        <button type="submit" class="cus-btn">Check
                                                            Booking</button>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                        <div class="tab-pane fade" id="checkIn">
                                            <form action="https://uiparadox.co.uk/templates/flynow/flight-listing.html" method="post">
                                                <div class="row align-items-center">
                                                    <div class="col-xl-10 col-lg-10 col-md-10">
                                                        <div class="booking-info d-block">
                                                            <div class="row">
                                                                <div class="col-lg-6 col-md-6 col-sm-6">
                                                                    <div class="custom-sel-input-block v-2 m-0">
                                                                        <input type="text" class="sel-input" name="reference" id="referName" placeholder="Booking Reference">
                                                                    </div>
                                                                </div>
                                                                <div class="col-lg-6 col-md-6 col-sm-6">
                                                                    <div class="custom-sel-input-block m-0">
                                                                        <input type="text" class="sel-input" name="name" id="lastName" placeholder="Last Name">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-xl-2 col-lg-2 col-md-2 mt-md-0 mt-32">
                                                        <button type="submit" class="cus-btn">Check Booking</button>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <!-- Manage tab End -->

                                <!-- Flight Status tab Start -->
                                <div class="tab-pane fade" id="status">
                                    <div class="booking-bar">
                                        <ul class="nav nav-tabs " data-bs-tabs="tabs">
                                            <li class="nav-item">
                                                <a href="#byRoute" class="nav-link active" aria-current="true"
                                                    data-bs-toggle="tab"> By Route
                                                </a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="#flightNo" class="nav-link " aria-current="false"
                                                    data-bs-toggle="tab"> By flight Number
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="card-body tab-content">
                                        <div class="tab-pane fade show active" id="byRoute">
                                            <form action="https://uiparadox.co.uk/templates/flynow/flight-listing.html" method="post">
                                                <div class="row align-items-center">
                                                    <div class="col-xl-10 col-lg-10">
                                                        <div class="booking-info v-2 d-md-flex d-inline-block justify-content-between">
                                                            
                                                                <div class="custom-sel-input-block m-0">
                                                                    <input type="text" class="sel-input auto-input" id="flightDepart" placeholder="From">
                                                                    <div class="slector-wrapper"></div>
                                                                </div>
                                                                <div class="custom-sel-input-block m-0 d-flex flex-row align-items-center">
                                                                    <div class="vertical-line d-md-flex d-none"></div>
                                                                    <input type="text" class="sel-input auto-input" id="flightArrive" placeholder="To">
                                                                    <div class="slector-wrapper"></div>
                                                                </div>
                                                            
                                                            <div>
                                                                <div class="input-date-picker date-time m-0">
                                                                    <input type="text" class="sel-input date_from" id="flightTimeDate" placeholder="Aug 20, 2023">
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-xl-2 col-lg-2 d-flex align-items-center mt-lg-0 mt-32">
                                                        <button type="submit" class="cus-btn">Show Flight
                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                width="24" height="24" viewBox="0 0 32 32"
                                                                fill="none">
                                                                <g clip-path="url(#clip0_502_1331)">
                                                                    <path
                                                                        d="M31.6933 0.544584C30.6572 -0.491824 27.1402 1.34503 26.1041 2.38143L21.9545 6.53127H3.07887C2.63024 6.53127 2.24462 6.85011 2.16055 7.29104C2.07669 7.73189 2.31798 8.16995 2.73524 8.3348L15.2174 13.2677L7.5633 20.9216H0.323909C0.168651 20.9221 0.0346723 21.0323 0.00576263 21.1852C-0.023357 21.3385 0.060152 21.4901 0.20498 21.5471L6.29687 23.9548C6.33201 24.1078 6.38108 24.2574 6.44394 24.4038L6.17745 24.6709C5.79778 25.0503 5.79778 25.6651 6.17745 26.045C6.55664 26.4247 7.17263 26.4247 7.55182 26.045L7.81194 25.785C7.95935 25.8501 8.11132 25.9014 8.26623 25.9382L10.6144 31.9561C10.6709 32.1013 10.8229 32.1851 10.976 32.1568C11.0419 32.145 11.1002 32.1123 11.1451 32.0673C11.2044 32.0087 11.2399 31.9274 11.2399 31.8382V24.7512L19.0155 16.976L23.9019 29.4993C24.0654 29.9177 24.5037 30.1608 24.9452 30.0781C25.136 30.0421 25.3038 29.9498 25.4333 29.8212C25.6038 29.6499 25.7071 29.4151 25.7077 29.1591V10.284L29.8573 6.13423C30.893 5.09789 32.7293 1.58085 31.6933 0.544584Z"
                                                                        fill="white" />
                                                                </g>
                                                                <defs>
                                                                    <clipPath id="clip0_502_1391">
                                                                        <rect width="32" height="32" fill="black" />
                                                                    </clipPath>
                                                                </defs>
                                                            </svg>
                                                        </button>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                        <div class="tab-pane fade" id="flightNo">
                                            <form action="https://uiparadox.co.uk/templates/flynow/flight-listing.html" method="post">
                                                <div class="row align-items-center">
                                                    <div class="col-xl-10 col-lg-10">
                                                        <div class="booking-info v-2 d-md-flex d-inline-block justify-content-between">
                                                            
                                                                <div class="custom-sel-input-block m-0">
                                                                    <input type="text" class="sel-input auto-input" id="flightDepat" placeholder="From">
                                                                    <div class="slector-wrapper"></div>
                                                                </div>
                                                                <div class="custom-sel-input-block m-0 d-flex flex-row align-items-center">
                                                                    <div class="vertical-line d-md-flex d-none"></div>
                                                                    <input type="text" class="sel-input auto-input" id="flightArive" placeholder="To">
                                                                    <div class="slector-wrapper"></div>
                                                                </div>
                                                            
                                                            <div>
                                                                <div class="input-date-picker date-time m-0">
                                                                    <input type="text" class="sel-input date_from" id="flightTieDate" placeholder="Aug 20, 2023">
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-xl-2 col-lg-2 d-flex align-items-center mt-lg-0 mt-32">
                                                        <button type="submit" class="cus-btn">Show Flight
                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                width="24" height="24" viewBox="0 0 32 32"
                                                                fill="none">
                                                                <g clip-path="url(#clip0_502_1331)">
                                                                    <path
                                                                        d="M31.6933 0.544584C30.6572 -0.491824 27.1402 1.34503 26.1041 2.38143L21.9545 6.53127H3.07887C2.63024 6.53127 2.24462 6.85011 2.16055 7.29104C2.07669 7.73189 2.31798 8.16995 2.73524 8.3348L15.2174 13.2677L7.5633 20.9216H0.323909C0.168651 20.9221 0.0346723 21.0323 0.00576263 21.1852C-0.023357 21.3385 0.060152 21.4901 0.20498 21.5471L6.29687 23.9548C6.33201 24.1078 6.38108 24.2574 6.44394 24.4038L6.17745 24.6709C5.79778 25.0503 5.79778 25.6651 6.17745 26.045C6.55664 26.4247 7.17263 26.4247 7.55182 26.045L7.81194 25.785C7.95935 25.8501 8.11132 25.9014 8.26623 25.9382L10.6144 31.9561C10.6709 32.1013 10.8229 32.1851 10.976 32.1568C11.0419 32.145 11.1002 32.1123 11.1451 32.0673C11.2044 32.0087 11.2399 31.9274 11.2399 31.8382V24.7512L19.0155 16.976L23.9019 29.4993C24.0654 29.9177 24.5037 30.1608 24.9452 30.0781C25.136 30.0421 25.3038 29.9498 25.4333 29.8212C25.6038 29.6499 25.7071 29.4151 25.7077 29.1591V10.284L29.8573 6.13423C30.893 5.09789 32.7293 1.58085 31.6933 0.544584Z"
                                                                        fill="white" />
                                                                </g>
                                                                <defs>
                                                                    <clipPath id="clip0_502_1321">
                                                                        <rect width="32" height="32" fill="black" />
                                                                    </clipPath>
                                                                </defs>
                                                            </svg>
                                                        </button>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <!-- Flight Status tab End -->
                            </div>
                        </div>

                    </div>
                </div>
            </section>
            <!-- Booking Area End -->

            <!-- Flight Listing Start -->
            <section class="flight-listing-page mb-60">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-xl-4 col-lg-4 mb-xl-0 mb-32">
                            <div class="sidebar bg-white br-10 light-shadow">
                                <div class="sidebar-title">
                                    <h4 class="lightest-black">Filter Search</h4>
                                </div>
                                <div class="filter-block p-24 box-1"  data-sal="slide-left" data-sal-duration="800" data-sal-delay="100" data-sal-easing="ease-in-out">
                                    <div class="title mb-32" data-count="1">
                                        <h4 class="color-black">Cabin </h4>
                                        <i class="fal fa-chevron-up color-primary"></i>
                                    </div>
                                    <div class="content-block mb-32">
                                        <div class="radio-tile-group sidebar">
                                            <div class="input-container">
                                                <input id="economyClas" class="radio-button"  type="radio" name="radio" value="Economy">
                                                <div class="radio-tile sidebar-radio">
                                                    <label for="economyClas" class="radio-tile-label sidebar-label">Economy Class</label>
                                                </div>
                                            </div>
                                            <div class="input-container">
                                                <input id="businessCl" class="radio-button" type="radio" name="radio" value="Business">
                                                <div class="radio-tile sidebar-radio">
                                                    <label for="businessCl" class="radio-tile-label sidebar-label">Business Class</label>
                                                </div>
                                            </div>
                                            <div class="input-container">
                                                <input id="firstClas" class="radio-button" type="radio" name="radio" value="First Class">
                                                <div class="radio-tile sidebar-radio">
                                                    <label for="firstClas" class="radio-tile-label sidebar-label">First Class</label>
                                                </div>
                                            </div>
                                            <div class="input-container">
                                                <input id="preEco" class="radio-button" type="radio" name="radio" value="Premium Economy">
                                                <div class="radio-tile sidebar-radio">
                                                    <label for="preEco" class="radio-tile-label sidebar-label">Premium Economy</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <hr class="bg-sec-gray mb-8">
                                </div>
                                <div class="filter-block plr-24 box-2"  data-sal="slide-right" data-sal-duration="800" data-sal-delay="100" data-sal-easing="ease-in-out">
                                    <div class="title mb-32" data-count="2">
                                        <h4 class="color-black">Price Range </h4>
                                        <i class="fal fa-chevron-up color-primary"></i>
                                    </div>
                                    <div class="content-block">
                                        <div class="slider-track mb-32">
                                            <div class="d-flex justify-content-between mb-4p">
                                                <h5>$100</h5>
                                                <h5>$3000</h5>
                                            </div>
                                            <input type="text" class="js-slider form-control" value="0">
                                        </div>
                                    </div>
                                    <hr class="bg-sec-gray mb-32">
                                </div>
                                <div class="filter-block plr-24 box-3"  data-sal="slide-left" data-sal-duration="800" data-sal-delay="100" data-sal-easing="ease-in-out">
                                    <div class="title mb-32" data-count="3">
                                        <h4 class="color-black fw-500">Airlines</h4>
                                        <i class="fal fa-chevron-up color-primary"></i>
                                    </div>
                                    <div class="content-block">
                                        <div class="custom-control mb-32">
                                            <div class="d-flex justify-content-between align-items-center mb-24">
                                                <div class="radio-button">
                                                    <input type="radio" name="airline" class="custom-control-input" id="emirates">
                                                    <label class="custom-control-label lightest-black" for="emirates">Emirates Airline</label>
                                                </div>
                                                <h5 class="light-black">(02)</h5>
                                            </div>
                                            <div class="d-flex justify-content-between align-items-center mb-24">
                                                <div class="radio-button">
                                                    <input type="radio" name="airline" class="custom-control-input" id="qatar">
                                                    <label class="custom-control-label lightest-black" for="qatar">Qatar Airways</label>
                                                </div>
                                                <h5 class="light-black">(04)</h5>
                                            </div>
                                            <div class="d-flex justify-content-between align-items-center mb-24">
                                                <div class="radio-button">
                                                    <input type="radio" name="airline" class="custom-control-input" id="arabian">
                                                    <label class="custom-control-label lightest-black" for="arabian">Saudi Arabian Airlines</label>
                                                </div>
                                                <h5 class="light-black">(06)</h5>
                                            </div>
                                            <div class="d-flex justify-content-between align-items-center mb-24">
                                                <div class="radio-button">
                                                    <input type="radio" name="airline" class="custom-control-input" id="asian">
                                                    <label class="custom-control-label lightest-black" for="asian">Air Asia</label>
                                                </div>
                                                <h5 class="light-black">(08)</h5>
                                            </div>
                                            <div class="filter-block-2 box-6">
                                                <div class="title justify-content-start load-more-btn h5 mb-24 color-primary" data-count="6"> Show all airlines&nbsp;&nbsp;<i class="fas fa-caret-down"></i> </div>
                                                <div class="content-block">
                                                    <div class="d-flex justify-content-between align-items-center mb-24">
                                                        <div class="radio-button">
                                                            <input type="radio" name="airline" class="custom-control-input" id="saudi">
                                                            <label class="custom-control-label lightest-black" for="saudi">Saudi Arabian Airlines</label>
                                                        </div>
                                                        <h5 class="light-black">(06)</h5>
                                                    </div>
                                                    <div class="d-flex justify-content-between align-items-center mb-24">
                                                        <div class="radio-button">
                                                            <input type="radio" name="airline" class="custom-control-input" id="air_asia">
                                                            <label class="custom-control-label lightest-black" for="air_asia">Air Asia</label>
                                                        </div>
                                                        <h5 class="light-black">(08)</h5>
                                                    </div>
                                                    <div class="d-flex justify-content-between align-items-center mb-24">
                                                        <div class="radio-button">
                                                            <input type="radio" name="airline" class="custom-control-input" id="pia-air">
                                                            <label class="custom-control-label lightest-black" for="pia-air">Pakistan International Airline</label>
                                                        </div>
                                                        <h5 class="light-black">(10)</h5>
                                                    </div>  
                                                </div>
                                            </div>
                                            
                                        </div>
                                    </div>
                                    <hr class="bg-sec-gray mb-32">
                                </div>
                                <div class="filter-block plr-24 border-0 box-4"  data-sal="slide-right" data-sal-duration="800" data-sal-delay="100" data-sal-easing="ease-in-out">
                                    <div class="title mb-32" data-count="4">
                                        <h4 class="color-black">Stops</h4>
                                        <i class="fal fa-chevron-up color-primary"></i>
                                    </div>
                                    <div class="content-block mb-32">
                                        <div class="content-block">
                                            <div class="radio-tile-group sidebar mb-24">
                                                <div class="input-container">
                                                    <input id="any" class="radio-button" type="radio" name="radio" value="any">
                                                    <div class="radio-tile sidebar-radio">
                                                        <label for="any" class="radio-tile-label sidebar-label">Any</label>
                                                    </div>
                                                </div>
                                                <div class="input-container">
                                                    <input id="non-stop" class="radio-button" type="radio" name="radio" value="non-stop">
                                                    <div class="radio-tile sidebar-radio">
                                                        <label for="non-stop" class="radio-tile-label sidebar-label">Non-Stop</label>
                                                    </div>
                                                </div>
                                                <div class="input-container">
                                                    <input id="stop_1" class="radio-button" type="radio" name="radio" value="stop_1">
                                                    <div class="radio-tile sidebar-radio">
                                                        <label for="stop_1" class="radio-tile-label sidebar-label">1 Stop</label>
                                                    </div>
                                                </div>
                                                <div class="input-container">
                                                    <input id="stop_2" class="radio-button" type="radio" name="radio" value="stop_2">
                                                    <div class="radio-tile sidebar-radio">
                                                        <label for="stop_2" class="radio-tile-label sidebar-label">2 Stop</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="filter-checkbox">
                                            <input type="checkbox" id="night">
                                            <label for="night" class="allow_over">Allow overnight stopovers</label>
                                        </div>
                                    </div>
                                    <hr class="bg-sec-gray mb-32">
                                </div>
                                <div class="filter-block plr-24 box-5"  data-sal="slide-left" data-sal-duration="800" data-sal-delay="100" data-sal-easing="ease-in-out">
                                    <div class="title mb-16 pb-4" data-count="5">
                                        <h4 class="color-black">Departure </h4>
                                        <i class="fal fa-chevron-up color-primary"></i>
                                    </div>
                                    <div class="content-block">
                                        <div class="radio-tile-group sidebar pb-24">
                                            <div class="input-container">
                                                <input id="e-morning" class="radio-button" type="radio" name="clock-time" value="e-morning">
                                                <div class="radio-tile sidebar-departure-radio">
                                                    <i class="fal fa-clock"></i>
                                                    <label for="e-morning" class="radio-tile-label departure-radio">Early Morning</label>
                                                    <h6>4:00am - 5:59am</h6>
                                                </div>
                                            </div>
                                            <div class="input-container">
                                                <input id="morning" class="radio-button" type="radio" name="clock-time" value="morning">
                                                <div class="radio-tile sidebar-departure-radio">
                                                    <i class="fal fa-clock"></i>
                                                    <label for="morning" class="radio-tile-label departure-radio">Morning</label>
                                                    <h6>6:00am - 11:59pm</h6>
                                                </div>
                                            </div>
                                            <div class="input-container">
                                                <input id="afternoon" class="radio-button" type="radio" name="clock-time" value="after-noon">
                                                <div class="radio-tile sidebar-departure-radio">
                                                    <i class="fal fa-clock"></i>
                                                    <label for="afternoon" class="radio-tile-label departure-radio">Afternoon</label>
                                                    <h6>12:00pm - 4:59pm</h6>
                                                </div>
                                            </div>
                                            <div class="input-container">
                                                <input id="evening" class="radio-button" type="radio" name="clock-time" value="evening">
                                                <div class="radio-tile sidebar-departure-radio">
                                                    <i class="fal fa-clock"></i>
                                                    <label for="evening" class="radio-tile-label departure-radio">Evening</label>
                                                    <h6>5:00pm - 7:59pm</h6>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-8 col-lg-8">
                            <div class="flight-block bg-white light-shadow p-24 br-10 mb-16"   data-sal="slide-up" data-sal-duration="800" data-sal-delay="100" data-sal-easing="ease-in-out">
                                <div class="flight-area">
                                    <div class="airline-name">
                                        <img src="assets/media/flight_icon/icon-1.png" alt="">
                                        <div>
                                            <h5 class="lightest-black mb-8">Feel Dubai Airline</h5>
                                            <h6 class="dark-gray">Boeing 777-90</h6>
                                        </div>
                                    </div>
                                    <div class="flight-detail">
                                        <div class="flight-departure">
                                            <h5 class="color-black">12:00</h5>
                                            <h5 class="dark-gray text-end">DUB</h5>
                                        </div>
                                        <div class="d-inline-flex align-items-center gap-8">
                                            <span class="">To</span>
                                            <div class="from-to text-center">
                                                <h5 class="dark-gray">0h 50m</h5>
                                                <img src="assets/media/icons/route-plan.png" alt="">
                                                <h6 class="color-black">1 Stop</h6>
                                            </div>
                                            <span class="">From</span>
                                        </div>
                                        <div class="flight-departure">
                                            <h5 class="color-black">12:50</h5>
                                            <h5 class="dark-gray">SHJ</h5>
                                        </div>
                                    </div>
                                    <div class="flight-button">
                                        <div class="amount">
                                            <h5 class="color-black">$240</h5>
                                            <h6 class="dark-gray text-end">Price</h6>
                                        </div>
                                        <a href="flight-booking.html" class="cus-btn btn-sec">Book Now</a>
                                    </div>
                                </div>
                                <hr class="bg-light-gray mt-24 mb-24">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="color-black">Monday 14 August</h5>
                                    <div>
                                        <a href="#" class="accordion-button color-primary h5 collapsed" data-bs-toggle="collapse" data-bs-target="#flightDetail"
                                            aria-expanded="true"> <i class="fal fa-chevron-down color-primary "></i>&nbsp;Flight Detail</a>
                                    </div>
                                </div>
                            </div>
                            <div id="flightDetail" class="accordion-collapse collapse mb-32" data-bs-parent="#accordionExample">
                                <div class="row bg-white br-10 light-shadow p-24 m-0 align-items-center">
                                    <div class="col-lg-3 col-sm-4">
                                        <div class="time-detail">
                                            <h6 class="flight-date mb-32"> 14 August, 2023</h6>
                                             <h6 class="color-black mb-8">Monday, Aug 14 - 12:00</h6>
                                             <h6 class="dark-gray mb-16">0h 50m</h6>
                                             <h6 class="dark-gray">Monday, Aug 14 - 12:50</h6>
                                        </div>
                                    </div>
                                    <div class="col-lg-9 col-sm-8">
                                        <div class="detail-block">
                                            <div class="d-sm-flex d-block align-items-center gap-24">
                                                <img src="assets/media/flight_icon/icon-1.png" alt="">
                                                <div class="content">
                                                    <h6 class="dark-gray">Tpm Line</h6>
                                                    <h6 class="dark-gray">Operated by Feel Dubai Airlines</h6>
                                                    <h6 class="dark-gray">Economy | Flight FK234 | Aircraft BOEING 777-90</h6>
                                                    <h6 class="dark-gray">Adult(s): 25KG luggage free</h6>
                                                </div>
                                            </div>
    
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="flight-block bg-white light-shadow p-24 br-10 mb-16"  data-sal="slide-down" data-sal-duration="800" data-sal-delay="100" data-sal-easing="ease-in-out">
                                <div class="flight-area">
                                    <div class="airline-name">
                                        <img src="assets/media/flight_icon/icon-2.png" alt="">
                                        <div>
                                            <h5 class="lightest-black mb-8">United Dubai Airlines</h5>
                                            <h6 class="dark-gray">Boeing 777-90</h6>
                                        </div>
                                    </div>
                                    <div class="flight-detail">
                                        <div class="flight-departure">
                                            <h5 class="color-black">12:00</h5>
                                            <h5 class="dark-gray text-end">DUB</h5>
                                        </div>
                                        <div class="d-inline-flex align-items-center gap-8">
                                            <span class="">To</span>
                                            <div class="from-to text-center">
                                                <h5 class="dark-gray">0h 50m</h5>
                                                <img src="assets/media/icons/route-plan.png" alt="">
                                                <h6 class="color-black">1 Stop</h6>
                                            </div>
                                            <span class="">From</span>
                                        </div>
                                        <div class="flight-departure">
                                            <h5 class="color-black">12:50</h5>
                                            <h5 class="dark-gray">SHJ</h5>
                                        </div>
                                    </div>
                                    <div class="flight-button">
                                        <div class="amount">
                                            <h5 class="color-black">$240</h5>
                                            <h6 class="dark-gray text-end">Price</h6>
                                        </div>
                                        <a href="flight-booking.html" class="cus-btn btn-sec">Book Now</a>
                                    </div>
                                </div>
                                <hr class="bg-light-gray mt-24 mb-24">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="color-black">Monday 14 August</h5>
                                    <div>
                                        <a href="#" class="accordion-button color-primary h5 collapsed" data-bs-toggle="collapse" data-bs-target="#unitedDubai"
                                            aria-expanded="true"> <i class="fal fa-chevron-down color-primary "></i>&nbsp;Flight Detail</a>
                                    </div>
                                </div>
                            </div>
                            <div id="unitedDubai" class="accordion-collapse collapse mb-32" data-bs-parent="#accordionExample">
                                <div class="row bg-white br-10 light-shadow p-24 m-0 align-items-center">
                                    <div class="col-lg-3 col-sm-4">
                                        <div class="time-detail">
                                            <h6 class="flight-date mb-32"> 14 August, 2023</h6>
                                             <h6 class="color-black mb-8">Monday, Aug 14 - 12:00</h6>
                                             <h6 class="dark-gray mb-16">0h 50m</h6>
                                             <h6 class="dark-gray">Monday, Aug 14 - 12:50</h6>
                                        </div>
                                    </div>
                                    <div class="col-lg-9 col-sm-8">
                                        <div class="detail-block">
                                            <div class="d-sm-flex d-block align-items-center gap-24">
                                                <img src="assets/media/flight_icon/icon-2.png" alt="">
                                                <div class="content">
                                                    <h6 class="dark-gray">Tpm Line</h6>
                                                    <h6 class="dark-gray">Operated by Feel Dubai Airlines</h6>
                                                    <h6 class="dark-gray">Economy | Flight FK234 | Aircraft BOEING 777-90</h6>
                                                    <h6 class="dark-gray">Adult(s): 25KG luggage free</h6>
                                                </div>
                                            </div>
    
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="flight-block bg-white light-shadow p-24 br-10 mb-16"  data-sal="slide-up" data-sal-duration="800" data-sal-delay="100" data-sal-easing="ease-in-out">
                                <div class="flight-area">
                                    <div class="airline-name">
                                        <img src="assets/media/flight_icon/icon-1.png" alt="">
                                        <div>
                                            <h5 class="lightest-black mb-8">Feel Dubai Airline</h5>
                                            <h6 class="dark-gray">Boeing 777-90</h6>
                                        </div>
                                    </div>
                                    <div class="flight-detail">
                                        <div class="flight-departure">
                                            <h5 class="color-black">12:00</h5>
                                            <h5 class="dark-gray text-end">DUB</h5>
                                        </div>
                                        <div class="d-inline-flex align-items-center gap-8">
                                            <span class="">To</span>
                                            <div class="from-to text-center">
                                                <h5 class="dark-gray">0h 50m</h5>
                                                <img src="assets/media/icons/route-plan.png" alt="">
                                                <h6 class="color-black">1 Stop</h6>
                                            </div>
                                            <span class="">From</span>
                                        </div>
                                        <div class="flight-departure">
                                            <h5 class="color-black">12:50</h5>
                                            <h5 class="dark-gray">SHJ</h5>
                                        </div>
                                    </div>
                                    <div class="flight-button">
                                        <div class="amount">
                                            <h5 class="color-black">$240</h5>
                                            <h6 class="dark-gray text-end">Price</h6>
                                        </div>
                                        <a href="flight-booking.html" class="cus-btn btn-sec">Book Now</a>
                                    </div>
                                </div>
                                <hr class="bg-light-gray mt-24 mb-24">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="color-black">Monday 14 August</h5>
                                    <div>
                                        <a href="#" class="accordion-button color-primary h5 collapsed" data-bs-toggle="collapse" data-bs-target="#feelDubai"
                                            aria-expanded="true"> <i class="fal fa-chevron-down color-primary "></i>&nbsp;Flight Detail</a>
                                    </div>
                                </div>
                            </div>
                            <div id="feelDubai" class="accordion-collapse collapse mb-32" data-bs-parent="#accordionExample">
                                <div class="row bg-white br-10 light-shadow p-24 m-0 align-items-center">
                                    <div class="col-lg-3 col-sm-4">
                                        <div class="time-detail">
                                            <h6 class="flight-date mb-32"> 14 August, 2023</h6>
                                             <h6 class="color-black mb-8">Monday, Aug 14 - 12:00</h6>
                                             <h6 class="dark-gray mb-16">0h 50m</h6>
                                             <h6 class="dark-gray">Monday, Aug 14 - 12:50</h6>
                                        </div>
                                    </div>
                                    <div class="col-lg-9 col-sm-8">
                                        <div class="detail-block">
                                            <div class="d-sm-flex d-block align-items-center gap-24">
                                                <img src="assets/media/flight_icon/icon-1.png" alt="">
                                                <div class="content">
                                                    <h6 class="dark-gray">Tpm Line</h6>
                                                    <h6 class="dark-gray">Operated by Feel Dubai Airlines</h6>
                                                    <h6 class="dark-gray">Economy | Flight FK234 | Aircraft BOEING 777-90</h6>
                                                    <h6 class="dark-gray">Adult(s): 25KG luggage free</h6>
                                                </div>
                                            </div>
    
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="flight-block bg-white light-shadow p-24 br-10 mb-16"  data-sal="slide-down" data-sal-duration="800" data-sal-delay="100" data-sal-easing="ease-in-out">
                                <div class="flight-area">
                                    <div class="airline-name">
                                        <img src="assets/media/flight_icon/icon-2.png" alt="">
                                        <div>
                                            <h5 class="lightest-black mb-8">United Dubai Airline</h5>
                                            <h6 class="dark-gray">Boeing 777-90</h6>
                                        </div>
                                    </div>
                                    <div class="flight-detail">
                                        <div class="flight-departure">
                                            <h5 class="color-black">12:00</h5>
                                            <h5 class="dark-gray text-end">DUB</h5>
                                        </div>
                                        <div class="d-inline-flex align-items-center gap-8">
                                            <span class="">To</span>
                                            <div class="from-to text-center">
                                                <h5 class="dark-gray">0h 50m</h5>
                                                <img src="assets/media/icons/route-plan.png" alt="">
                                                <h6 class="color-black">1 Stop</h6>
                                            </div>
                                            <span class="">From</span>
                                        </div>
                                        <div class="flight-departure">
                                            <h5 class="color-black">12:50</h5>
                                            <h5 class="dark-gray">SHJ</h5>
                                        </div>
                                    </div>
                                    <div class="flight-button">
                                        <div class="amount">
                                            <h5 class="color-black">$240</h5>
                                            <h6 class="dark-gray text-end">Price</h6>
                                        </div>
                                        <a href="flight-booking.html" class="cus-btn btn-sec">Book Now</a>
                                    </div>
                                </div>
                                <hr class="bg-light-gray mt-24 mb-24">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="color-black">Monday 14 August</h5>
                                    <div>
                                        <a href="#" class="accordion-button color-primary collapsed h5" data-bs-toggle="collapse" data-bs-target="#dubaiAirlines"
                                            aria-expanded="true"> <i class="fal fa-chevron-down color-primary "></i>&nbsp;Flight Detail</a>
                                    </div>
                                </div>
                            </div>
                            <div id="dubaiAirlines" class="accordion-collapse collapse mb-32" data-bs-parent="#accordionExample">
                                <div class="row bg-white br-10 light-shadow p-24 m-0 align-items-center">
                                    <div class="col-lg-3 col-sm-4">
                                        <div class="time-detail">
                                            <h6 class="flight-date mb-32"> 14 August, 2023</h6>
                                             <h6 class="color-black mb-8">Monday, Aug 14 - 12:00</h6>
                                             <h6 class="dark-gray mb-16">0h 50m</h6>
                                             <h6 class="dark-gray">Monday, Aug 14 - 12:50</h6>
                                        </div>
                                    </div>
                                    <div class="col-lg-9 col-sm-8">
                                        <div class="detail-block">
                                            <div class="d-sm-flex d-block align-items-center gap-24">
                                                <img src="assets/media/flight_icon/icon-2.png" alt="">
                                                <div class="content">
                                                    <h6 class="dark-gray">Tpm Line</h6>
                                                    <h6 class="dark-gray">Operated by Feel Dubai Airlines</h6>
                                                    <h6 class="dark-gray">Economy | Flight FK234 | Aircraft BOEING 777-90</h6>
                                                    <h6 class="dark-gray">Adult(s): 25KG luggage free</h6>
                                                </div>
                                            </div>
    
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="flight-block bg-white light-shadow p-24 br-10 mb-16"  data-sal="slide-up" data-sal-duration="800" data-sal-delay="100" data-sal-easing="ease-in-out">
                                <div class="flight-area">
                                    <div class="airline-name">
                                        <img src="assets/media/flight_icon/icon-1.png" alt="">
                                        <div>
                                            <h5 class="lightest-black mb-8">Feel Dubai Airline</h5>
                                            <h6 class="dark-gray">Boeing 777-90</h6>
                                        </div>
                                    </div>
                                    <div class="flight-detail">
                                        <div class="flight-departure">
                                            <h5 class="color-black">12:00</h5>
                                            <h5 class="dark-gray text-end">DUB</h5>
                                        </div>
                                        <div class="d-inline-flex align-items-center gap-8">
                                            <span class="">To</span>
                                            <div class="from-to text-center">
                                                <h5 class="dark-gray">0h 50m</h5>
                                                <img src="assets/media/icons/route-plan.png" alt="">
                                                <h6 class="color-black">1 Stop</h6>
                                            </div>
                                            <span class="">From</span>
                                        </div>
                                        <div class="flight-departure">
                                            <h5 class="color-black">12:50</h5>
                                            <h5 class="dark-gray">SHJ</h5>
                                        </div>
                                    </div>
                                    <div class="flight-button">
                                        <div class="amount">
                                            <h5 class="color-black">$240</h5>
                                            <h6 class="dark-gray text-end">Price</h6>
                                        </div>
                                        <a href="flight-booking.html" class="cus-btn btn-sec">Book Now</a>
                                    </div>
                                </div>
                                <hr class="bg-light-gray mt-24 mb-24">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="color-black">Monday 14 August</h5>
                                    <div>
                                        <a href="#" class="accordion-button color-primary collapsed h5" data-bs-toggle="collapse" data-bs-target="#unitedAirline"
                                            aria-expanded="true"> <i class="fal fa-chevron-down color-primary "></i>&nbsp;Flight Detail</a>
                                    </div>
                                </div>
                            </div>
                            <div id="unitedAirline" class="accordion-collapse collapse mb-32" data-bs-parent="#accordionExample">
                                <div class="row bg-white br-10 light-shadow p-24 m-0 align-items-center">
                                    <div class="col-lg-3 col-sm-4">
                                        <div class="time-detail">
                                            <h6 class="flight-date mb-32"> 14 August, 2023</h6>
                                             <h6 class="color-black mb-8">Monday, Aug 14 - 12:00</h6>
                                             <h6 class="dark-gray mb-16">0h 50m</h6>
                                             <h6 class="dark-gray">Monday, Aug 14 - 12:50</h6>
                                        </div>
                                    </div>
                                    <div class="col-lg-9 col-sm-8">
                                        <div class="detail-block">
                                            <div class="d-sm-flex d-block align-items-center gap-24">
                                                <img src="assets/media/flight_icon/icon-1.png" alt="">
                                                <div class="content">
                                                    <h6 class="dark-gray">Tpm Line</h6>
                                                    <h6 class="dark-gray">Operated by Feel Dubai Airlines</h6>
                                                    <h6 class="dark-gray">Economy | Flight FK234 | Aircraft BOEING 777-90</h6>
                                                    <h6 class="dark-gray">Adult(s): 25KG luggage free</h6>
                                                </div>
                                            </div>
    
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="flight-block bg-white light-shadow  p-24 br-10 mb-16"  data-sal="slide-down" data-sal-duration="800" data-sal-delay="100" data-sal-easing="ease-in-out">
                                <div class="flight-area">
                                    <div class="airline-name">
                                        <img src="assets/media/flight_icon/icon-2.png" alt="">
                                        <div>
                                            <h5 class="lightest-black mb-8">United Dubai Airline</h5>
                                            <h6 class="dark-gray">Boeing 777-90</h6>
                                        </div>
                                    </div>
                                    <div class="flight-detail">
                                        <div class="flight-departure">
                                            <h5 class="color-black">12:00</h5>
                                            <h5 class="dark-gray text-end">DUB</h5>
                                        </div>
                                        <div class="d-inline-flex align-items-center gap-8">
                                            <span class="">To</span>
                                            <div class="from-to text-center">
                                                <h5 class="dark-gray">0h 50m</h5>
                                                <img src="assets/media/icons/route-plan.png" alt="">
                                                <h6 class="color-black">1 Stop</h6>
                                            </div>
                                            <span class="">From</span>
                                        </div>
                                        <div class="flight-departure">
                                            <h5 class="color-black">12:50</h5>
                                            <h5 class="dark-gray">SHJ</h5>
                                        </div>
                                    </div>
                                    <div class="flight-button">
                                        <div class="amount">
                                            <h5 class="color-black">$240</h5>
                                            <h6 class="dark-gray text-end">Price</h6>
                                        </div>
                                        <a href="flight-booking.html" class="cus-btn btn-sec">Book Now</a>
                                    </div>
                                </div>
                                <hr class="bg-light-gray mt-24 mb-24">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="color-black">Monday 14 August</h5>
                                    <div>
                                        <a href="#" class="accordion-button color-primary collapsed h5" data-bs-toggle="collapse" data-bs-target="#feelAirline"
                                            aria-expanded="true"> <i class="fal fa-chevron-down color-primary "></i>&nbsp;Flight Detail</a>
                                    </div>
                                </div>
                            </div>
                            <div id="feelAirline" class="accordion-collapse collapse mb-32" data-bs-parent="#accordionExample">
                                <div class="row bg-white br-10 light-shadow p-24 m-0 align-items-center">
                                    <div class="col-lg-3 col-sm-4">
                                        <div class="time-detail">
                                            <h6 class="flight-date mb-32"> 14 August, 2023</h6>
                                             <h6 class="color-black mb-8">Monday, Aug 14 - 12:00</h6>
                                             <h6 class="dark-gray mb-16">0h 50m</h6>
                                             <h6 class="dark-gray">Monday, Aug 14 - 12:50</h6>
                                        </div>
                                    </div>
                                    <div class="col-lg-9 col-sm-8">
                                        <div class="detail-block">
                                            <div class="d-sm-flex d-block align-items-center gap-24">
                                                <img src="assets/media/flight_icon/icon-2.png" alt="">
                                                <div class="content">
                                                    <h6 class="dark-gray">Tpm Line</h6>
                                                    <h6 class="dark-gray">Operated by Feel Dubai Airlines</h6>
                                                    <h6 class="dark-gray">Economy | Flight FK234 | Aircraft BOEING 777-90</h6>
                                                    <h6 class="dark-gray">Adult(s): 25KG luggage free</h6>
                                                </div>
                                            </div>
    
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="paginations mt-8">
                                <ul class="unstyled">
                                  <li class="active"><a href="#" class="h6 fw-600 mb-0">1</a></li>
                                  <li><a href="#" class="h6 fw-600 mb-0">2</a></li>
                                  <li><a href="#" class="h6 fw-600 mb-0">3</a></li>
                                  <li><a href="#" class="h6 fw-600 mb-0">4</a></li>
                                  <li class="arrow"><a href="#" class="h4 fw-600 mb-0"><i class="far fa-chevron-right"></i></a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Flight Listing End -->
        </div>
        <!-- Main Content End -->

        <!-- Footer Start -->
        <footer class="bg-white p-40"  data-sal="slide-up" data-sal-duration="800" data-sal-delay="100" data-sal-easing="ease-in-out">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-6 mb-lg-0 mb-32 ">
                        <a href="#"><img src="assets/media/logo.png" alt="" class="mb-16"></a>
                        <p class="dark-gray mb-16">Lorem ipsum dolor sit amet consectetur. Aliquet vulputate augue
                            penatibus in
                            libero et id aliquam. In ridiculus pretium est velit euismod. </p>
                        <h6 class="lightest-black mb-8">Subscribe to our special offers</h6>
                        <form action="https://uiparadox.co.uk/templates/flynow/index.html" method="post">
                            <input type="email" class="form-control" placeholder="Email address" name="email">
                            <button type="submit" class="subscribe">Subscribe</button>
                        </form>
                    </div>
                    <div class="col-xl-2 col-lg-2 col-md-6 col-6 mb-lg-0 mb-32">
                        <h4 class="light-black mb-24">Booking</h4>
                        <div class="our-links">
                            <ul class="unstyled">
                                <li class="mb-16"><a href="flight-booking.html" class="light-black">Book Flights</a>
                                </li>
                                <li class="mb-16"><a href="tour-packages.html" class="light-black">Travel Services</a>
                                </li>
                                <li class="mb-16"><a href="car-booking.html" class="light-black">Transportation</a></li>
                                <li class="mb-16"><a href="tour-packages.html" class="light-black">Planning Your
                                        Trip</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-xl-2 col-lg-2 col-md-6 col-6 mb-lg-0 mb-32">
                        <h4 class="light-black mb-24">Useful Links</h4>
                        <div class="our-links">
                            <ul class="unstyled">
                                <li class="mb-16"><a href="index.html" class="light-black">Home</a>
                                </li>
                                <li class="mb-16"><a href="blog-listing.html" class="light-black">Blogs</a>
                                </li>
                                <li class="mb-16"><a href="about-us.html" class="light-black">About </a></li>
                                <li class="mb-16"><a href="contact.html" class="light-black">Contact Us</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-xl-2 col-lg-2 col-md-6 col-6 mb-md-0 mb-32">
                        <h4 class="light-black mb-24">Manage</h4>
                        <div class="our-links">
                            <ul class="unstyled">
                                <li class="mb-16"><a href="flight-booking.html" class="light-black">Check-in</a></li>
                                <li class="mb-16"><a href="tour-packages.html" class="light-black">Manage Your
                                        Booking</a></li>
                                <li class="mb-16"><a href="car-listing.html" class="light-black">Chaurfeur Drive</a>
                                </li>
                                <li class="mb-16"><a href="flight-listing.html" class="light-black">Flight Status</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-6">
                        <h4 class="light-black mb-16">Contact Us</h4>
                        <div class="follow-us">
                            <ul class="unstyled">
                                <li class="mb-8"><img src="assets/media/icons/location-bk.png" alt="">&nbsp;&nbsp;123
                                    Main Street, Anytown, USA.</li>
                                <li class="mb-8 h4 color-primary"><img src="assets/media/icons/telephone.png"
                                        alt="">&nbsp;&nbsp;****** 567 890</li>
                                <li class="mb-24"><a href="#"><img src="assets/media/icons/mail.png"
                                            alt="">&nbsp;&nbsp;<EMAIL></a></li>
                            </ul>
                        </div>
                        <div class="social-link mb-32">
                            <h6 class="light-black mb-8">Follow Us!</h6>
                            <ul class="unstyled">
                                <li><a href="#" class="active"><img src="assets/media/icons/instagram.png" alt=""></a></li>
                                <li><a href="#"><img src="assets/media/icons/facebook.png" alt=""></a></li>
                                <li><a href="#"><img src="assets/media/icons/linkedin.png" alt=""></a></li>
                                <li><a href="#"><img src="assets/media/icons/twitter.png" alt=""></a></li>
                            </ul>
                        </div>
                        <p class="light-black">©2025 FlyNow All Rights Reserved.</p>

                    </div>
                </div>
            </div>
        </footer>
        <!-- Footer Area End  -->

        <!-- modal-popup area Start  -->
        <div class="search-popup">
            <div class="search-popup__overlay search-toggler"></div>
            <div class="search-popup__content">
                <form role="search" method="get" class="search-popup__form" action="https://uiparadox.co.uk/templates/flynow/index.html">
                    <input type="text" id="search" placeholder="Search Here...">
                    <button type="submit"><i class="fal fa-search"></i></button>
                </form>
            </div>
        </div>
        <!-- search-popup end-->
    </div>


    <!-- Mobile Menu Start -->
    <div class="mobile-nav__wrapper">
        <div class="mobile-nav__overlay mobile-nav__toggler"></div>
        <div class="mobile-nav__content">
            <span class="mobile-nav__close mobile-nav__toggler"></span>
            <div class="logo-box">
                <a href="index.html" aria-label="logo image"><img src="assets/media/logo.png" alt="" class="invisible"></a>
            </div>
            <div class="mobile-nav__container"></div>
            <ul class="mobile-nav__contact list-unstyled">
                <li>
                    <i class="fas fa-envelope"></i>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </li>
                <li>
                    <i class="fa fa-phone-alt"></i>
                    <a href="tel:+12345678">+123 (4567) -890</a>
                </li>
            </ul>
            <!-- <div class="mobile-nav__social">
        <a href="https://twitter.com/"><i class="fab fa-twitter"></i></a>
        <a href="https://www.facebook.com/"><i class="fab fa-facebook"></i></a>
        <a href="https://www.instagram.com/"><i class="fab fa-instagram"></i></a>
      </div> -->
        </div>
    </div>
    <!-- Mobile Menu End -->

    <!-- Jquery Js -->
    <script src="assets/js/vendor/jquery-3.6.3.min.js"></script>
    <script src="assets/js/vendor/calendar/popper.min.js"></script>
    <script src="assets/js/vendor/bootstrap.min.js"></script>
    <script src="assets/js/vendor/slick.min.js"></script>
    <script src="assets/js/vendor/jquery-appear.js"></script>
    <script src="assets/js/vendor/jquery-validator.js"></script>
    <script src="assets/js/vendor/aksVideoPlayer.js"></script>
    <script src="assets/js/vendor/tilt.jquery.js"></script>
    <script src="assets/js/vendor/calendar/picker.js"></script>
    <script src="assets/js/vendor/calendar/picker.date.js"></script>
    <script src="assets/js/vendor/autoComplete.js"></script>
    <script src="assets/js/vendor/jquery.timepicker.min.js"></script>
    <script src="assets/js/vendor/ionrangeslider.js"></script>
    <script src="assets/js/vendor/sal.js"></script>

    <!-- Site Scripts -->
    <script src="assets/js/date.js"></script>
    <script src="assets/js/app.js"></script>
</body>


<!-- Mirrored from uiparadox.co.uk/templates/flynow/flight-listing.html by HTTrack Website Copier/3.x [XR&CO'2014], Thu, 03 Jul 2025 14:26:57 GMT -->
</html>