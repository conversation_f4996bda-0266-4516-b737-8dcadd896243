/*---------------------------------------------"
// Template Name: Fly Now
// Template URL: https://techpedia.co.uk/template/flyNow
// Description:  Fly Now Html Template
// Version: 1.0.0

===============================================   
STYLE SHEET INDEXING
|
|___ Fonts
|___ Variables
|___ Responsive
|___ Reset Styles
|___ Spacing
|___ Helper Classes
|___ Buttons
|___ Headings
|___ Layout Styles
|___ END STYLE SHEET INDEXING

--------------------------------------------*/
/*-------------------------
    Fonts
-------------------------*/
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&amp;display=swap");
/*-------------------------
    Variables
-------------------------*/
/* Fonts */
/* Colors */
/* Transitions */
/* Gradients */
/* Shadows */
/*-------------------------
    Responsive Styles
-------------------------*/
/*-------------------------
    Reset Styles
-------------------------*/
img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}

ul.list-style,
ol.list-style {
  padding: 0;
}

ul.list-style li::marker,
ol.list-style li::marker {
  color: #4D73FC;
}

.unstyled {
  padding-left: 0;
  list-style: none;
  margin-bottom: 0;
}

@media (min-width: 768px) {
  .container,
  .container-fluid {
    padding: 0 5vw;
    width: 100%;
  }
}
@media (max-width: 575px) {
  .container,
  .container-fluid {
    --bs-gutter-x: 2rem;
    --bs-gutter-y: 0;
    width: 100%;
    padding-right: calc(var(--bs-gutter-x) * 0.5);
  }
}

.row {
  margin-left: -15px;
  margin-right: -15px;
}

.row > [class*=col] {
  padding-left: 15px;
  padding-right: 15px;
}

.row.g-0 {
  margin-left: 0;
  margin-right: 0;
}

.row.g-0 > [class*=col] {
  padding-left: 0;
  padding-right: 0;
}

.page-content {
  margin: 20px 0;
}

.main-wrapper {
  height: auto;
  overflow-x: hidden;
}

.star-cb-group {
  font-size: 0;
  unicode-bidi: bidi-override;
  direction: rtl;
  margin-bottom: 24px;
}

/*-------------------------
    Typography
-------------------------*/
html {
  scroll-behavior: smooth;
}

html[data-scroll-orientation=horizontal] body {
  width: fit-content;
}
html[data-scroll-orientation=horizontal] [data-scroll-container] {
  display: flex;
}

body {
  font-family: "Inter", sans-serif;
  color: #0B0C0D;
  background-color: #ECECF2;
  font-size: clamp(13px, 0.833vw, 22px);
  font-weight: 400;
  line-height: 140%;
  height: 100%;
  vertical-align: baseline;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}
body::-webkit-scrollbar {
  width: 10px;
}
body::-webkit-scrollbar-track {
  background-color: #e4e4e4;
  border-radius: 4px;
}
body::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background: #4D73FC;
}
@media (max-width: 991px) {
  body {
    font-size: clamp(13.5px, 1.778vw, 16px);
  }
}
@media (max-width: 767px) {
  body {
    font-size: clamp(13px, 2.286vw, 14px);
  }
}
@media (max-width: 575px) {
  body {
    font-size: clamp(11.5px, 3.59vw, 14px);
  }
}

.o-scroll {
  height: 100vh;
}

h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4,
h5,
.h5,
h6,
.h6,
address,
p,
pre,
blockquote,
table,
hr {
  margin: 0;
}

h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a {
  color: inherit;
}

a {
  display: inline-block;
  text-decoration: none;
  color: unset;
  transition: all 0.5s ease-in-out;
}
a:hover {
  color: #4D73FC;
}

span {
  display: inline-block;
}

b,
strong {
  font-family: "Inter", sans-serif;
}

h1,
.h1 {
  font-size: clamp(40px, 3.177vw, 122px);
  /* 61px */
  font-weight: 700;
  line-height: 120%;
  letter-spacing: -1.22px;
}
@media (max-width: 991px) {
  h1,
  .h1 {
    font-size: clamp(38px, 5.119vw, 48px);
  }
}
@media (max-width: 767px) {
  h1,
  .h1 {
    font-size: clamp(35px, 6.094vw, 45px);
  }
}
@media (max-width: 575px) {
  h1,
  .h1 {
    font-size: clamp(32px, 8.78vw, 40px);
  }
}

h2,
.h2 {
  font-size: clamp(30px, 2.448vw, 98px);
  /* 47px */
  font-weight: 500;
  line-height: 125%;
  letter-spacing: -0.47px;
}
@media (max-width: 991px) {
  h2,
  .h2 {
    font-size: clamp(30px, 4.048vw, 40px);
  }
}
@media (max-width: 767px) {
  h2,
  .h2 {
    font-size: clamp(28px, 4.844vw, 36px);
  }
}
@media (max-width: 575px) {
  h2,
  .h2 {
    font-size: clamp(24px, 6.829vw, 32px);
  }
}

h3,
.h3 {
  font-size: clamp(24px, 1.875vw, 72px);
  /* 36px */
  font-weight: 700;
  line-height: 120%;
  letter-spacing: -0.36px;
}
@media (max-width: 991px) {
  h3,
  .h3 {
    font-size: clamp(24px, 3.493vw, 28px);
  }
}
@media (max-width: 767px) {
  h3,
  .h3 {
    font-size: clamp(22px, 4.23vw, 24px);
  }
}
@media (max-width: 575px) {
  h3,
  .h3 {
    font-size: clamp(20px, 6.575vw, 22px);
  }
}

h4,
.h4 {
  font-size: clamp(20px, 1.406vw, 54px);
  /* 27px */
  font-weight: 500;
  line-height: 120%;
  letter-spacing: -0.27px;
}
@media (max-width: 991px) {
  h4,
  .h4 {
    font-size: clamp(20px, 2.738vw, 22px);
  }
}
@media (max-width: 767px) {
  h4,
  .h4 {
    font-size: clamp(19px, 3.438vw, 23px);
  }
}
@media (max-width: 575px) {
  h4,
  .h4 {
    font-size: clamp(16px, 5.25vw, 18px);
  }
}

h5,
.h5 {
  font-size: clamp(16px, 1.094vw, 24px);
  /* 21px */
  font-weight: 500;
  line-height: 130%;
}
@media (max-width: 991px) {
  h5,
  .h5 {
    font-size: clamp(16px, 2.381vw, 16px);
  }
}
@media (max-width: 767px) {
  h5,
  .h5 {
    font-size: clamp(15px, 2.969vw, 16px);
    letter-spacing: 0.2px;
  }
}
@media (max-width: 575px) {
  h5,
  .h5 {
    font-size: clamp(14.5px, 4.325vw, 15px);
  }
}

h6,
.h6 {
  font-size: clamp(13px, 0.833vw, 22px);
  font-weight: 500;
  line-height: 150%;
  letter-spacing: 0.32px;
}
@media (max-width: 991px) {
  h6,
  .h6 {
    font-size: clamp(13.5px, 1.778vw, 16px);
  }
}
@media (max-width: 767px) {
  h6,
  .h6 {
    font-size: clamp(13px, 2.286vw, 14px);
  }
}
@media (max-width: 575px) {
  h6,
  .h6 {
    font-size: clamp(11.5px, 3.59vw, 14px);
  }
}

p {
  font-size: clamp(13px, 0.833vw, 22px);
  font-weight: 400;
  line-height: 150%;
  letter-spacing: 0.32px;
}
@media (max-width: 991px) {
  p {
    font-size: clamp(13.5px, 1.778vw, 16px);
  }
}
@media (max-width: 767px) {
  p {
    font-size: clamp(13px, 2.286vw, 14px);
  }
}
@media (max-width: 575px) {
  p {
    font-size: clamp(11.5px, 3.59vw, 14px);
  }
}

.slick-arrow::before {
  transition: all 0.5s ease-in-out;
}
.slick-arrow:hover::before {
  background: #FC9C4D;
}

/*-------------------------
Helpers
-------------------------*/
.color-primary {
  color: #4D73FC !important;
}

.color-sec {
  color: #FC9C4D !important;
}

.bg-primary {
  background-color: #4D73FC !important;
}

.bg-sec {
  background-color: #FC9C4D !important;
}

.color-white {
  color: #F8F8FF !important;
}

.bg-white {
  background-color: #F8F8FF !important;
}

.light-black {
  color: #212627 !important;
}

.lightest-black {
  color: #16191A;
}

.bg-light-black {
  background-color: #212627 !important;
}

.color-black {
  color: #0B0C0D !important;
}

.bg-black {
  background-color: #0B0C0D !important;
}

.dark-black {
  color: #0D0D0D !important;
}

.bg-dark-black {
  background-color: #0D0D0D !important;
}

.light-gray {
  color: #C6CBD2 !important;
}

.bg-lightest-gray {
  background-color: #ECECF2 !important;
}

.bg-light-gray {
  background-color: #C6CBD2 !important;
}

.bg-medium-gray {
  background-color: #9EA2A8 !important;
}

.color-medium-gray {
  color: #9EA2A8 !important;
}

.dark-gray {
  color: #7A7F85 !important;
}

.bg-dark-gray {
  background-color: #7A7F85 !important;
}

.text-shadow {
  text-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 94px 48px 42px 0px rgba(0, 0, 0, 0.01);
}

.light-shadow {
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 94px 48px 42px 0px rgba(0, 0, 0, 0.01);
}

.br-30 {
  border-radius: 30px;
}

.br-25 {
  border-radius: 25px;
}

.br-20 {
  border-radius: 20px;
}

.br-15 {
  border-radius: 15px;
}

.br-12 {
  border-radius: 12px;
}

.br-10 {
  border-radius: 10px;
}

.br-5 {
  border-radius: 5px;
}

.b-unstyle {
  border: 0;
  background: transparent;
}

.shadow {
  box-shadow: 4px 4px 10px 0px rgba(77, 115, 252, 0.3) !important;
}

.dark-shadow {
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25) !important;
}

.heading {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.social-icons {
  display: flex;
  gap: 32px;
}
@media (max-width: 768px) {
  .social-icons {
    width: 100%;
    justify-content: center;
  }
}
.social-icons li a {
  font-size: 32px;
  color: #F8F8FF;
  transition: all 0.5s ease-in-out;
}
.social-icons li a:hover {
  color: #4D73FC;
}
.social-icons li a:hover svg {
  stroke: #4D73FC;
  stroke-width: #4D73FC;
}

.countdown {
  padding: 0;
}
.countdown li {
  font-weight: 700;
  font-size: 47px;
  line-height: 125%;
  letter-spacing: -0.01em;
  color: #F8F8FF;
  margin-right: 32px;
  display: inline-flex;
  align-items: center;
}
@media (max-width: 1600px) {
  .countdown li {
    margin-right: 24px;
  }
}
.countdown li:last-child {
  margin-right: 0;
}
.countdown li span {
  font-weight: 600;
  font-size: 16px;
  line-height: 150%;
  color: #F8F8FF;
  transform: rotate(-90deg);
  padding: 0;
  background: none;
  border-radius: 0;
  margin-left: 0px;
}

.gap-4 {
  gap: 4px;
}
@media (max-width: 768px) {
  .gap-4 {
    gap: 2px;
  }
}

.gap-8 {
  gap: 8px;
}
@media (max-width: 768px) {
  .gap-8 {
    gap: 4px;
  }
}

.gap-16 {
  gap: 16px;
}
@media (max-width: 768px) {
  .gap-16 {
    gap: 12px;
  }
}

.gap-24 {
  gap: 24px;
}
@media (max-width: 1199px) {
  .gap-24 {
    gap: 20px;
  }
}
@media (max-width: 992px) {
  .gap-24 {
    gap: 16px;
  }
}
@media (max-width: 768px) {
  .gap-24 {
    gap: 12px;
  }
}

.gap-32 {
  gap: 32px;
}
@media (max-width: 1199px) {
  .gap-32 {
    gap: 26px;
  }
}
@media (max-width: 992px) {
  .gap-32 {
    gap: 22px;
  }
}
@media (max-width: 768px) {
  .gap-32 {
    gap: 18px;
  }
}

@media (max-width: 768px) {
  .facility img {
    width: 24px;
  }
}
@media (max-width: 575px) {
  .facility img {
    width: 16px;
  }
}

.fw-400 {
  font-weight: 400;
}

.fw-500 {
  font-weight: 500;
}

.fw-600 {
  font-weight: 600;
}

.fw-700 {
  font-weight: 700;
}

/*----------------------------------------*/
/*  SPACE CSS START
/*----------------------------------------*/
.m-80 {
  margin: clamp(48px, 4.167vw, 160px) 0;
}
@media (max-width: 575px) {
  .m-80 {
    margin: 40px 0;
  }
}

.mt-80 {
  margin-top: clamp(48px, 4.167vw, 160px);
}
@media (max-width: 575px) {
  .mt-80 {
    margin-top: 40px;
  }
}

.mb-80 {
  margin-bottom: clamp(48px, 4.167vw, 160px);
}
@media (max-width: 575px) {
  .mb-80 {
    margin-bottom: 40px;
  }
}

.mt-64 {
  margin-top: clamp(32px, 3.33vw, 128px);
}
@media (max-width: 767px) {
  .mt-64 {
    margin-top: 26px;
  }
}

.mb-64 {
  margin-bottom: clamp(32px, 3.33vw, 128px);
}
@media (max-width: 767px) {
  .mb-64 {
    margin-bottom: 26px;
  }
}

.mb-60 {
  margin-bottom: 60px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .mb-60 {
    margin-bottom: 46px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .mb-60 {
    margin-bottom: 40px;
  }
}
@media (max-width: 767px) {
  .mb-60 {
    margin-bottom: 30px;
  }
}

.m-48 {
  margin: clamp(24px, 2.5vw, 96px) 0;
}
@media (max-width: 767px) {
  .m-48 {
    margin: 23px 0;
  }
}

.mb-48 {
  margin-bottom: clamp(24px, 2.5vw, 96px);
}
@media (max-width: 767px) {
  .mb-48 {
    margin-bottom: 23px;
  }
}

.mt-48 {
  margin-top: clamp(24px, 2.5vw, 96px);
}
@media (max-width: 767px) {
  .mt-48 {
    margin-top: 23px;
  }
}

.m-40 {
  margin: clamp(24px, 2.08vw, 80px) 0;
}
@media (max-width: 575px) {
  .m-40 {
    margin: 20px 0;
  }
}

.mt-40 {
  margin-top: clamp(24px, 2.08vw, 80px);
}
@media (max-width: 575px) {
  .mt-40 {
    margin-top: 20px;
  }
}

.mb-40 {
  margin-bottom: clamp(24px, 2.08vw, 80px);
}
@media (max-width: 575px) {
  .mb-40 {
    margin-bottom: 20px;
  }
}

.mt-32 {
  margin-top: 32px;
}
@media (max-width: 1599px) {
  .mt-32 {
    margin-top: 29px;
  }
}
@media (max-width: 1399px) {
  .mt-32 {
    margin-top: 27px;
  }
}
@media (max-width: 1199px) {
  .mt-32 {
    margin-top: 25px;
  }
}
@media (max-width: 991px) {
  .mt-32 {
    margin-top: 22px;
  }
}
@media (max-width: 767px) {
  .mt-32 {
    margin-top: 20px;
  }
}

.mb-32 {
  margin-bottom: 32px;
}
@media (max-width: 1599px) {
  .mb-32 {
    margin-bottom: 29px;
  }
}
@media (max-width: 1399px) {
  .mb-32 {
    margin-bottom: 27px;
  }
}
@media (max-width: 1199px) {
  .mb-32 {
    margin-bottom: 25px;
  }
}
@media (max-width: 991px) {
  .mb-32 {
    margin-bottom: 22px;
  }
}
@media (max-width: 767px) {
  .mb-32 {
    margin-bottom: 20px;
  }
}

.mb-30 {
  margin-bottom: 30px;
}
@media (max-width: 1199px) {
  .mb-30 {
    margin-bottom: 28px;
  }
}
@media (max-width: 991px) {
  .mb-30 {
    margin-bottom: 24px;
  }
}
@media (max-width: 767px) {
  .mb-30 {
    margin-bottom: 20px;
  }
}

.mb-24 {
  margin-bottom: 24px;
}
@media (max-width: 1199px) {
  .mb-24 {
    margin-bottom: 22px;
  }
}
@media (max-width: 991px) {
  .mb-24 {
    margin-bottom: 22px;
  }
}
@media (max-width: 767px) {
  .mb-24 {
    margin-bottom: 18px;
  }
}

.mt-24 {
  margin-top: 24px;
}
@media (max-width: 1199px) {
  .mt-24 {
    margin-top: 22px;
  }
}
@media (max-width: 991px) {
  .mt-24 {
    margin-top: 22px;
  }
}
@media (max-width: 767px) {
  .mt-24 {
    margin-top: 18px;
  }
}

.mb-20 {
  margin-bottom: 20px;
}
@media (max-width: 1199px) {
  .mb-20 {
    margin-bottom: 18px;
  }
}
@media (max-width: 991px) {
  .mb-20 {
    margin-bottom: 16px;
  }
}
@media (max-width: 767px) {
  .mb-20 {
    margin-bottom: 14px;
  }
}

.mb-16 {
  margin-bottom: 16px;
}
@media (max-width: 1199px) {
  .mb-16 {
    margin-bottom: 15px;
  }
}
@media (max-width: 991px) {
  .mb-16 {
    margin-bottom: 14px;
  }
}
@media (max-width: 767px) {
  .mb-16 {
    margin-bottom: 12px;
  }
}

.mt-16 {
  margin-top: 16px;
}
@media (max-width: 1199px) {
  .mt-16 {
    margin-top: 15px;
  }
}
@media (max-width: 991px) {
  .mt-16 {
    margin-top: 14px;
  }
}
@media (max-width: 767px) {
  .mt-16 {
    margin-top: 12px;
  }
}

.mb-12 {
  margin-bottom: 12px;
}
@media (max-width: 1199px) {
  .mb-12 {
    margin-bottom: 11px;
  }
}
@media (max-width: 991px) {
  .mb-12 {
    margin-bottom: 10px;
  }
}
@media (max-width: 767px) {
  .mb-12 {
    margin-bottom: 8px;
  }
}

.mt-8 {
  margin-top: 8px;
}
@media (max-width: 767px) {
  .mt-8 {
    margin-top: 4px;
  }
}

.mb-8 {
  margin-bottom: 8px;
}

.mb-4p {
  margin-bottom: 4px;
}

@media (max-width: 1299px) {
  .mr-4 {
    margin-right: 4px;
  }
}

.p-108 {
  padding: clamp(64px, 5.625vw, 216px);
}
@media (max-width: 767px) {
  .p-108 {
    padding: 48px;
  }
}

.p-96 {
  padding: clamp(54px, 5vw, 192px) 0;
}
@media (max-width: 767px) {
  .p-96 {
    padding: 44px 0;
  }
}

.p-80 {
  padding: clamp(48px, 4.167vw, 160px) 0;
}
@media (max-width: 575px) {
  .p-80 {
    padding: 40px 0;
  }
}

.pt-80 {
  padding-top: clamp(48px, 4.167vw, 160px);
}
@media (max-width: 575px) {
  .pt-80 {
    padding-top: 40px;
  }
}

.pb-80 {
  padding-bottom: clamp(48px, 4.167vw, 160px);
}
@media (max-width: 575px) {
  .pb-80 {
    padding-bottom: 40px;
  }
}

.p-64 {
  padding: clamp(40px, 3.33vw, 128px) 0;
}
@media (max-width: 767px) {
  .p-64 {
    padding: 32px 0;
  }
}

.pt-64 {
  padding-top: clamp(40px, 3.33vw, 128px);
}
@media (max-width: 767px) {
  .pt-64 {
    padding-top: 32px;
  }
}

.pad-64 {
  padding-bottom: clamp(40px, 3.33vw, 128px);
}
@media (max-width: 767px) {
  .pad-64 {
    padding-bottom: 32px;
  }
}

.p-60 {
  padding: 60px 0;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .p-60 {
    padding: 40px 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .p-60 {
    padding: 32px 0;
  }
}
@media (max-width: 767px) {
  .p-60 {
    padding: 20px 0;
  }
}

.p-48 {
  padding: clamp(24px, 2.5vw, 96px) 0;
}
@media (max-width: 767px) {
  .p-48 {
    padding: 23px 0;
  }
}

.pb-48 {
  padding-bottom: clamp(24px, 2.5vw, 96px);
}
@media (max-width: 767px) {
  .pb-48 {
    padding-bottom: 23px;
  }
}

.pt-48 {
  padding-top: clamp(24px, 2.5vw, 96px);
}
@media (max-width: 767px) {
  .pt-48 {
    padding-top: 23px;
  }
}

.p-40 {
  padding: clamp(24px, 2.083vw, 80px) 0;
}
@media (max-width: 575px) {
  .p-40 {
    padding: 20px 0;
  }
}

.pt-40 {
  padding-top: clamp(24px, 2.083vw, 80px);
}
@media (max-width: 575px) {
  .pt-40 {
    padding-top: 20px;
  }
}

.pb-40 {
  padding-bottom: clamp(24px, 2.083vw, 80px);
}
@media (max-width: 575px) {
  .pb-40 {
    padding-bottom: 20px;
  }
}

.pl-36 {
  padding-left: 37px;
}
@media (max-width: 1199px) {
  .pl-36 {
    padding-left: 32px;
  }
}
@media (max-width: 991px) {
  .pl-36 {
    padding-left: 28px;
  }
}
@media (max-width: 767px) {
  .pl-36 {
    padding-left: 22px;
  }
}

.pad-32 {
  padding: 32px;
}
@media (max-width: 1199px) {
  .pad-32 {
    padding: 28px;
  }
}
@media (max-width: 991px) {
  .pad-32 {
    padding: 24px;
  }
}
@media (max-width: 767px) {
  .pad-32 {
    padding: 20px;
  }
}

.p-24 {
  padding: clamp(16px, 1.25vw, 48px);
}
@media (max-width: 575px) {
  .p-24 {
    padding: 14px;
  }
}

.pb-24 {
  padding-bottom: clamp(16px, 1.25vw, 48px);
}
@media (max-width: 575px) {
  .pb-24 {
    padding-bottom: 14px;
  }
}

.plr-24 {
  padding: 0 clamp(16px, 1.25vw, 48px);
}
@media (max-width: 575px) {
  .plr-24 {
    padding: 0 14px;
  }
}

.p-16 {
  padding: 16px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .p-16 {
    padding: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .p-16 {
    padding: 13px;
  }
}
@media (max-width: 767px) {
  .p-16 {
    padding: 10px;
  }
}
@media (max-width: 490px) {
  .p-16 {
    padding: 10px;
  }
}

.pl-28 {
  padding-left: 28px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .pl-28 {
    padding-left: 24px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .pl-28 {
    padding-left: 19px;
  }
}
@media (max-width: 767px) {
  .pl-28 {
    padding-left: 16px;
  }
}
@media (max-width: 490px) {
  .pl-28 {
    padding-left: 14px;
  }
}

/*-------------------------
Elements
-------------------------*/
#preloader {
  position: fixed;
  width: 100%;
  height: 100vh;
  display: grid;
  align-items: center;
  text-align: center;
  background: #F8F8FF;
  z-index: 9999999;
  align-content: center;
}
#preloader .loader {
  text-align: center;
  width: 100%;
  position: relative;
  overflow: hidden;
  max-width: 35rem;
  height: 18rem;
  margin: 0 auto;
}
#preloader .loader .wait {
  margin: 5rem 0;
  visibility: hidden;
}
#preloader .loader .iata_code {
  font-size: 4rem;
  font-weight: 600;
  top: 60%;
  position: absolute;
  color: #4D73FC;
}
#preloader .loader .iata_code.departure_city {
  left: 55px;
}
#preloader .loader .iata_code.arrival_city {
  right: 0.5rem;
}
#preloader .loader .plane {
  position: absolute;
  margin: 0 auto;
  width: 100%;
}
#preloader .loader .plane .plane-img {
  -webkit-animation: spin 2.5s linear infinite;
  -moz-animation: spin 2.5s linear infinite;
  animation: spin 2.5s linear infinite;
}
@media (max-width: 575px) {
  #preloader .loader .plane .plane-img {
    width: 210px;
  }
}
@-moz-keyframes spin {
  100% {
    -moz-transform: rotate(360deg);
  }
}
@-webkit-keyframes spin {
  100% {
    -webkit-transform: rotate(360deg);
  }
}
@keyframes spin {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
#preloader .loader .earth-wrapper {
  position: absolute;
  margin: 0 auto;
  width: 100%;
  padding-top: 2.7rem;
}
#preloader .loader .earth-wrapper .earth {
  width: 160px;
  height: 160px;
  background: url("../../assets/media/earth.gif");
  border-radius: 100%;
  background-size: 340px;
  animation: earthAnim 12s infinite linear;
  margin: 0 auto;
  border: 1px solid #CDD1D3;
}
@media (max-width: 575px) {
  #preloader .loader .earth-wrapper .earth {
    width: 120px;
    height: 120px;
  }
}
@keyframes earthAnim {
  0% {
    background-position-x: 0;
  }
  100% {
    background-position-x: -340px;
  }
}
@media screen and (max-width: 420px) {
  #preloader .loader .departure_city {
    left: 0;
    right: 0;
    top: 30%;
    position: absolute;
    margin: 0 auto;
  }
  #preloader .loader .arrival_city {
    left: 0;
    right: 0;
    top: 93%;
    position: absolute;
    margin: 0 auto;
  }
}

.back-to-top {
  position: fixed;
  bottom: 0px;
  right: 12px;
  display: block;
  width: 120px;
  height: 120px;
  font-size: 100px;
  padding: 11px 0;
  border-radius: 10px;
  background: #4D73FC;
  color: #F8F8FF;
  text-align: center;
  opacity: 0;
  text-decoration: none;
  -webkit-transform: scale(0.3);
  -ms-transform: scale(0.3);
  transform: scale(0.3);
  z-index: 999;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}
.back-to-top:hover {
  background: #FC9C4D;
  color: #F8F8FF;
}
.back-to-top.show {
  opacity: 1;
}
@media (max-width: 490px) {
  .back-to-top {
    right: -20px;
    bottom: -20px;
  }
}

.cus-btn {
  font-family: "Inter", sans-serif;
  font-size: clamp(13px, 0.833vw, 22px);
  font-weight: 600;
  line-height: 150%;
  letter-spacing: 0.32px;
  padding: clamp(10px, 0.833vw, 24px) clamp(20px, 1.66vw, 48px);
  display: flex;
  justify-content: center;
  width: fit-content;
  align-items: center;
  gap: clamp(5px, 0.42vw, 12px);
  transition: all 0.5s ease-in-out;
  border-radius: 10px;
  border: none;
  background: #4D73FC;
  box-shadow: 4px 4px 10px 0px rgba(77, 115, 252, 0.3);
  color: #F8F8FF;
}
.cus-btn i {
  font-size: 24px;
}
@media (max-width: 492px) {
  .cus-btn i {
    font-size: 20px;
  }
}
.cus-btn:hover {
  background-color: #ECECF2;
  color: #16191A;
}
.cus-btn.small-pad {
  padding: clamp(8px, 0.53vw, 16px) clamp(14px, 1.25vw, 32px);
}
.cus-btn.full-width {
  width: 100%;
}
.cus-btn.extra-width {
  width: 191px;
}
.cus-btn.width-237 {
  width: 237px;
}
@media (max-width: 576px) {
  .cus-btn.width-237 {
    width: fit-content;
  }
}
.cus-btn.primary {
  gap: clamp(10px, 0.833vw, 24px);
  padding: clamp(10px, 0.833vw, 24px) clamp(18px, 1.66vw, 48px);
  font-size: clamp(18px, 1.406vw, 54px);
  font-weight: 500;
  line-height: 120%;
  letter-spacing: -0.27px;
  background-color: #ECECF2;
  color: #16191A;
  box-shadow: none;
  border-radius: 5px;
}
.cus-btn.primary.primary-light {
  font-size: clamp(16px, 1.094vw, 24px);
  line-height: 130%;
  letter-spacing: inherit;
}
.cus-btn.primary.active, .cus-btn.primary:hover {
  background-color: #4D73FC;
  color: #F8F8FF;
  box-shadow: 4px 4px 10px 0px rgba(77, 115, 252, 0.3);
}
.cus-btn.sm-light {
  font-size: 14px;
  padding: 4px 9px;
  border-radius: 8px;
  letter-spacing: 0.28px;
}
.cus-btn.grey {
  background-color: #9EA2A8;
  color: #F8F8FF;
  transition: all 0.5s ease-in-out;
}
.cus-btn.grey:hover {
  background: #4D73FC;
}
.cus-btn.dark {
  background-color: #212627;
  color: #F8F8FF;
  transition: all 0.5s ease-in-out;
}
.cus-btn.dark:hover {
  background-color: #F8F8FF;
  color: #212627;
}
.cus-btn.btn-sec:hover {
  background-color: #FC9C4D;
  color: #F8F8FF;
}

.light-btn {
  display: flex;
  padding: clamp(8px, 0.73vw, 20px) clamp(16px, 1.5vw, 40px);
  justify-content: center;
  align-items: center;
  gap: clamp(6px, 0.53vw, 20px);
  font-family: "Open Sans", sans-serif;
  border-radius: 15px;
  font-size: clamp(16px, 1.094vw, 24px);
  font-weight: 600;
  line-height: 120%; /* 24px */
  transition: all 0.5s ease-in-out;
  color: #F8F8FF;
}
.light-btn.primary i {
  color: #4D73FC;
}
.light-btn.primary:hover {
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 94px 48px 42px 0px rgba(0, 0, 0, 0.01);
}

.cus-btn-2 {
  display: flex;
  padding: clamp(6px, 0.53vw, 20px) clamp(8px, 0.73vw, 20px);
  justify-content: center;
  align-items: center;
  gap: clamp(6px, 0.53vw, 20px);
  border-radius: 41px;
  border: 1px solid #FFF;
  box-shadow: 10px 10px 30px 0px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(25px);
  text-align: center;
  color: #F8F8FF;
  font-family: "Open Sans", sans-serif;
  font-size: clamp(10px, 0.95vw, 24px);
  font-weight: 600;
  line-height: 130%; /* 23.4px */
  transition: all 0.5s ease-in-out;
}
.cus-btn-2:hover {
  border-color: #4D73FC;
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 94px 48px 42px 0px rgba(0, 0, 0, 0.01);
}
.cus-btn-2.btn-sec {
  padding: clamp(10px, 0.83vw, 24px);
  border-radius: 10px;
  border: 1px solid #212627;
  background: rgba(77, 115, 252, 0.1);
  color: #212627;
  box-shadow: none;
}
.cus-btn-2.btn-sec:hover {
  background: #FC9C4D;
  border: 1px solid #FC9C4D;
  color: #F8F8FF;
}
.cus-btn-2.btn-sec.width-237 {
  width: 237px;
  padding: clamp(10px, 0.83vw, 24px) clamp(20px, 1.33vw, 42px);
}
@media (max-width: 576px) {
  .cus-btn-2.btn-sec.width-237 {
    width: fit-content;
  }
}
@media (max-width: 492px) {
  .cus-btn-2.btn-sec.width-237 {
    font-size: 14px;
    padding: 12px 24px;
    border-radius: 12px;
  }
}

.modal {
  width: 100%;
}
.modal .modal-dialog {
  max-width: 50%;
}
@media (max-width: 580px) {
  .modal .modal-dialog {
    max-width: 100%;
  }
}
.modal .modal-dialog .modal-body {
  width: 100%;
  text-align: end;
}
.modal .modal-dialog .modal-body .btn-close {
  margin-bottom: 12px;
}

/*-------------------------
  Form Styling
-------------------------*/
.form-group {
  position: relative;
}

.input-group {
  position: relative;
  background: #0B0C0D;
  padding: 12px 16px;
  border: none;
  border-radius: 12px;
  display: flex;
  align-items: center;
}
.input-group input {
  width: 88%;
  border: none;
  background: transparent;
  color: #9EA2A8;
  font-size: 16px;
  font-weight: 400;
  line-height: 150%;
  font-family: "Open Sans", sans-serif;
}
.input-group input:focus {
  outline: 0;
  box-shadow: none;
  border: none;
}
.input-group input::placeholder {
  color: #7A7F85;
  opacity: 1;
}
.input-group button {
  margin-right: 20px;
  border: none;
  background: transparent;
  color: #9EA2A8;
  font-size: 24px;
  padding: 0;
  text-align: end;
  box-shadow: none;
}
.input-group.search-bar {
  width: 100%;
}
.input-group.st-2 {
  border: none;
  background: #C6CBD2;
  border-radius: 20px;
}

.form-control {
  padding: 16px 16px;
  border-radius: 5px;
  background: rgba(77, 115, 252, 0.1);
  font-size: 16px;
  font-weight: 400;
  line-height: 150%;
  font-family: "Inter", sans-serif;
  position: relative;
  color: #16191A;
  border: 0;
  box-shadow: none;
}
.form-control:focus {
  box-shadow: none;
  outline: 0;
  color: #0B0C0D;
  border: none;
  background: rgba(77, 115, 252, 0.1);
}
.form-control::placeholder {
  color: #16191A;
  opacity: 1;
}

.form-select {
  background: rgba(77, 115, 252, 0.1);
  border: 0;
  position: relative;
}
.form-select::after {
  position: absolute;
  top: 4px;
  right: 4px;
  content: "";
  border-left: 1px solid #212627;
  border-bottom: 1px solid #212627;
}
.form-select.border {
  border: 1px solid #212627;
}

.form-group .form-control {
  background: #F8F8FF;
  padding: 12px 16px;
}

select {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  outline: 0;
  box-shadow: none;
  border: 0 !important;
  background: rgba(77, 115, 252, 0.1);
  background-image: none;
  flex: 1;
  padding: 16px 24px;
  color: #212627;
  cursor: pointer;
  font-size: 16px;
  font-family: "Inter", sans-serif;
}

select::-ms-expand {
  display: none;
}

.select {
  position: relative;
  display: flex;
  line-height: 150%;
  background: rgba(77, 115, 252, 0.1);
  overflow: hidden;
  border-radius: 0.25em;
}
.select::after {
  content: "\f078";
  position: absolute;
  top: 0;
  right: 0;
  padding: 16px 24px;
  font-family: "Font Awesome 5 Pro";
  cursor: pointer;
  pointer-events: none;
  transition: 0.25s all ease;
}
.select:hover::after {
  color: #4D73FC;
}

.inputGroup {
  width: 100%;
  position: relative;
}
.inputGroup textarea,
.inputGroup input {
  font-family: "Inter", sans-serif;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 150%;
  /* 24px */
  letter-spacing: 0.32px;
  padding: 16px;
  outline: none;
  border: none;
  background-color: #ECECF2;
  border-radius: 20px;
  width: 100%;
  color: #212627;
}
.inputGroup label {
  font-family: "Inter", sans-serif;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 150%;
  /* 24px */
  letter-spacing: 0.32px;
  position: absolute;
  left: 0;
  padding: 0.8em;
  margin-left: 0.5em;
  pointer-events: none;
  transition: all 0.3s ease;
  color: #7A7F85;
}

.inputGroup :is(textarea:focus, textarea:valid) ~ label,
.inputGroup :is(input:focus, input:valid) ~ label {
  transform: translateY(-50%) scale(0.9);
  margin-left: 1.3em;
  padding: 0.4em;
  background-color: transparent;
}

textarea {
  height: 149px;
}

label.error {
  color: #bc0f0f;
  margin-top: 10px;
}

input[type=checkbox] {
  height: auto;
  width: auto;
  background-color: #9EA2A8;
}

input[type=checkbox]:checked {
  accent-color: #4D73FC;
}

input[type=checkbox]:checked {
  accent-color: #4D73FC;
  background-color: #4D73FC;
  border-radius: 10px;
}

input[type=range]::-webkit-slider-thumb {
  background: #4D73FC;
}

input[type=radio] {
  height: auto;
  width: auto;
}

input[type=radio]:checked {
  accent-color: #4D73FC;
}

.alert-message {
  display: none;
}

.search {
  display: inline-block;
  position: relative;
  box-shadow: 4px 4px 10px 0px rgba(77, 115, 252, 0.3);
  overflow: hidden;
  border-radius: 15px;
}
@media (max-width: 768px) {
  .search {
    width: 100%;
  }
}

.search input[type=text] {
  width: 636px;
  padding: 16px;
  border: none;
  box-shadow: 4px 4px 10px 0px rgba(77, 115, 252, 0.3);
  font-family: "Inter", sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 150%;
  /* 24px */
  letter-spacing: 0.32px;
  color: #7A7F85;
}
@media (max-width: 1199px) {
  .search input[type=text] {
    width: 500px;
  }
}
@media (max-width: 992px) {
  .search input[type=text] {
    width: 430px;
  }
}
@media (max-width: 768px) {
  .search input[type=text] {
    width: 100%;
  }
}

.search input[type=text]:focus {
  outline: 0;
}

.search button[type=submit] {
  position: absolute;
  top: 0;
  right: 0;
}

.checkBox {
  display: block;
}

.checkBox input {
  padding: 0;
  height: initial;
  width: initial;
  margin-bottom: 0;
  display: none;
  cursor: pointer;
}

.checkBox label {
  position: relative;
  cursor: pointer;
  font-family: "Inter", sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 150%;
  /* 24px */
  letter-spacing: 0.32px;
  color: #9EA2A8;
  display: flex;
  align-items: center;
  gap: 16px;
}
@media (max-width: 490px) {
  .checkBox label {
    align-items: flex-start;
  }
}

.checkBox label:before {
  content: "";
  -webkit-appearance: none;
  background-color: transparent;
  border: 2px solid #9EA2A8;
  border-radius: 5px;
  box-shadow: none;
  padding: 8px;
  display: inline-block;
  position: relative;
  vertical-align: middle;
  cursor: pointer;
}
@media (max-width: 490px) {
  .checkBox label:before {
    margin-top: 5px;
  }
}

.checkBox input:checked + label:after {
  content: "";
  display: block;
  position: absolute;
  top: 6px;
  left: 7px;
  width: 6px;
  height: 10px;
  border: solid #F8F8FF;
  border-width: 0 1.5px 1.5px 0;
  transform: rotate(45deg);
}

.card {
  border: none;
  background: transparent;
}
.card .card-header {
  background: transparent;
  border: none;
  padding: 0;
  border-radius: 0;
}
.card .card-header .nav-tabs {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 1px;
  margin: 0;
  padding: 0px;
  border-radius: 0px;
  background: transparent;
}
.card .card-header .nav-tabs li.flight-sec {
  width: calc(25% - 1px);
}
.card .card-header .nav-tabs li.flight-sec a {
  width: 100%;
}
.card .card-header .nav-tabs li.flight-sec a.active, .card .card-header .nav-tabs li.flight-sec a:hover {
  background: #4D73FC;
}
@media (max-width: 1099px) {
  .card .card-header .nav-tabs li.flight-sec {
    width: calc(50% - 1px);
  }
}
@media (max-width: 420px) {
  .card .card-header .nav-tabs li.flight-sec {
    width: calc(100% - 1px);
  }
}
.card .card-body {
  padding: 0;
  padding-top: 32px;
}

textarea {
  height: 147px !important;
}

.quantity {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-shrink: 0;
}
.quantity .number,
.quantity .increment,
.quantity .decrement {
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: center;
  border-radius: 5px;
  background: rgba(77, 115, 252, 0.1);
  color: #212627;
  font-family: "Inter", sans-serif;
  font-size: 21px;
  font-weight: 500;
  flex-shrink: 0;
  line-height: 140%;
  /* 29.4px */
  border: 1px solid #212627;
  width: 35px;
  height: 45px;
}
@media (max-width: 420px) {
  .quantity .number,
  .quantity .increment,
  .quantity .decrement {
    width: 32px;
    height: 36px;
  }
}
.quantity .number {
  color: #0B0C0D;
  background: transparent;
  border: 0;
}

.radio-button input:checked,
.radio-button input:not(:checked) {
  position: absolute;
  left: -9999px;
}

.radio-button label {
  color: #4D73FC;
  font-family: "Inter", sans-serif;
  font-size: 21px;
  font-weight: 500;
  line-height: 130%;
  /* 29.4px */
}
@media (max-width: 674px) {
  .radio-button label {
    font-size: 16px;
  }
}

.radio-button input:not(:checked) + label {
  color: #7A7F85;
}

.radio-button input:checked + label,
.radio-button input:not(:checked) + label {
  position: relative;
  padding-left: 36px;
  cursor: pointer;
  display: inline-block;
}
@media (max-width: 674px) {
  .radio-button input:checked + label,
  .radio-button input:not(:checked) + label {
    padding-left: 22px;
  }
}

.radio-button input:checked + label:before,
.radio-button input:not(:checked) + label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0px;
  width: 28px;
  height: 28px;
  border: 2px solid #4D73FC;
  border-radius: 100%;
  background: #F8F8FF;
}
@media (max-width: 674px) {
  .radio-button input:checked + label:before,
  .radio-button input:not(:checked) + label:before {
    padding-left: 16px;
    width: 20px;
    height: 20px;
  }
}

.radio-button input:not(:checked) + label:before {
  border: 2px solid #4D73FC;
}
@media (max-width: 674px) {
  .radio-button input:not(:checked) + label:before {
    border: 1px solid #4D73FC;
  }
}

.radio-button input:checked + label:after,
.radio-button input:not(:checked) + label:after {
  content: "";
  width: 16px;
  height: 16px;
  background: #4D73FC;
  position: absolute;
  top: 6px;
  left: 6px;
  border-radius: 100%;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
}
@media (max-width: 674px) {
  .radio-button input:checked + label:after,
  .radio-button input:not(:checked) + label:after {
    width: 10px;
    height: 10px;
    top: 5px;
    left: 5px;
  }
}

.radio-button input:not(:checked) + label:after {
  opacity: 0;
  -webkit-transform: scale(0);
  transform: scale(0);
}

.radio-button input:checked + label:after {
  opacity: 1;
  -webkit-transform: scale(1);
  transform: scale(1);
}

.filter-checkbox input {
  padding: 0;
  display: none;
  border: 1px solid #212627;
  cursor: pointer;
}
.filter-checkbox input:checked + label:after {
  content: "";
  display: block;
  position: absolute;
  top: 3px;
  left: 5px;
  width: 5px;
  height: 12px;
  z-index: 10;
  border: solid #4D73FC;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
.filter-checkbox label {
  position: relative;
  cursor: pointer;
  color: #7A7F85;
  font-family: "Inter", sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 130%;
}
@media (max-width: 992px) {
  .filter-checkbox label {
    font-size: 15px;
  }
}
@media (max-width: 575px) {
  .filter-checkbox label {
    font-size: 14px;
  }
}
.filter-checkbox label:before {
  content: "";
  -webkit-appearance: none;
  background: transparent;
  border: 1px solid #7A7F85;
  border-radius: 5px;
  width: 16px;
  height: 16px;
  display: inline-block;
  position: relative;
  vertical-align: middle;
  cursor: pointer;
  margin-right: 8px;
  margin-top: -3px;
}
.filter-checkbox label.black-color {
  color: #0B0C0D;
}

/*--------------------------------------------------------------
# Search Popup
--------------------------------------------------------------*/
.search-popup {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -2;
  -webkit-transition: all 1s ease;
  -khtml-transition: all 1s ease;
  -moz-transition: all 1s ease;
  -ms-transition: all 1s ease;
  -o-transition: all 1s ease;
  transition: all 1s ease;
}
.search-popup .search-popup__overlay {
  position: fixed;
  width: 224vw;
  height: 224vw;
  top: calc(90px - 112vw);
  right: calc(50% - 112vw);
  z-index: 3;
  display: block;
  -webkit-border-radius: 50%;
  -khtml-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  border-radius: 50%;
  -webkit-transform: scale(0);
  -khtml-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
  -webkit-transform-origin: center;
  transform-origin: center;
  -webkit-transition: transform 0.8s ease-in-out;
  -khtml-transition: transform 0.8s ease-in-out;
  -moz-transition: transform 0.8s ease-in-out;
  -ms-transition: transform 0.8s ease-in-out;
  -o-transition: transform 0.8s ease-in-out;
  transition: transform 0.8s ease-in-out;
  transition-delay: 0s;
  transition-delay: 0.3s;
  -webkit-transition-delay: 0.3s;
  background-color: #9fb4ff;
  opacity: 0.8;
  cursor: url(../media/close.png), auto;
}
@media (max-width: 767px) {
  .search-popup .search-popup__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    transform: none;
    width: 100%;
    height: 100%;
    border-radius: 0;
    transform: translateY(-110%);
  }
}
.search-popup .search-popup__content {
  position: fixed;
  width: 0;
  max-width: 560px;
  padding: 30px 15px;
  left: 50%;
  top: 50%;
  opacity: 0;
  z-index: 3;
  -webkit-transform: translate(-50%, -50%);
  -khtml-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-transition: opacity 0.5s 0s, width 0.8s 0.8s cubic-bezier(0.225, 0.01, 0.475, 1.01), transform 0.2s 0s;
  -khtml-transition: opacity 0.5s 0s, width 0.8s 0.8s cubic-bezier(0.225, 0.01, 0.475, 1.01), transform 0.2s 0s;
  -moz-transition: opacity 0.5s 0s, width 0.8s 0.8s cubic-bezier(0.225, 0.01, 0.475, 1.01), transform 0.2s 0s;
  -ms-transition: opacity 0.5s 0s, width 0.8s 0.8s cubic-bezier(0.225, 0.01, 0.475, 1.01), transform 0.2s 0s;
  -o-transition: opacity 0.5s 0s, width 0.8s 0.8s cubic-bezier(0.225, 0.01, 0.475, 1.01), transform 0.2s 0s;
  transition: opacity 0.5s 0s, width 0.8s 0.8s cubic-bezier(0.225, 0.01, 0.475, 1.01), transform 0.2s 0s;
  transition-delay: 0s, 0.8s, 0s;
  transition-delay: 0s, 0.4s, 0s;
  transition-delay: 0.2s;
  -webkit-transition-delay: 0.2s;
}
.search-popup .search-popup__content .search-popup__form {
  position: relative;
}
.search-popup .search-popup__content .search-popup__form input[type=search],
.search-popup .search-popup__content .search-popup__form input[type=text] {
  width: 100%;
  background-color: #ffffff !important;
  font-size: 15px;
  color: #697585;
  border: none;
  outline: none;
  height: 66px;
  padding-left: 30px;
}
.search-popup .search-popup__content .search-popup__form button {
  padding: 0;
  width: 66px;
  height: 66px;
  border: 0;
  background: #4D73FC;
  color: #F8F8FF;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  position: absolute;
  top: 0;
  right: -1px;
  border-radius: 0;
}
.search-popup .search-popup__content .search-popup__form button i {
  margin: 0;
  font-size: 24px;
}
.search-popup .search-popup__content .search-popup__form button::after {
  background-color: #9b59b6;
}
.search-popup.active {
  z-index: 9999;
}
.search-popup.active .search-popup__overlay {
  top: auto;
  bottom: calc(90px - 112vw);
  -webkit-transform: scale(1);
  -khtml-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
  transition-delay: 0s;
  -webkit-transition-delay: 0s;
  opacity: 0.8;
  -webkit-transition: transform 1.6s cubic-bezier(0.4, 0, 0, 1);
  -khtml-transition: transform 1.6s cubic-bezier(0.4, 0, 0, 1);
  -moz-transition: transform 1.6s cubic-bezier(0.4, 0, 0, 1);
  -ms-transition: transform 1.6s cubic-bezier(0.4, 0, 0, 1);
  -o-transition: transform 1.6s cubic-bezier(0.4, 0, 0, 1);
  transition: transform 1.6s cubic-bezier(0.4, 0, 0, 1);
}
@media (max-width: 767px) {
  .search-popup.active .search-popup__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    transform: none;
    width: 100%;
    height: 100%;
    border-radius: 0;
    transform: translateY(0%);
  }
}
.search-popup.active .search-popup__content {
  width: 100%;
  opacity: 1;
  transition-delay: 0.7s;
  -webkit-transition-delay: 0.7s;
}

.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin: 0;
}
.pagination li a {
  width: 45px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #F8F8FF;
  font-size: 16px;
  font-family: "Inter", sans-serif;
  font-weight: 600;
  line-height: 150%; /* 24px */
  border-radius: 8px;
  letter-spacing: 0.32px;
  background: #0B0C0D;
}
@media (max-width: 490px) {
  .pagination li a {
    width: 38px;
    height: 37px;
    border-radius: 6px;
  }
}
.pagination li a i {
  font-size: 16px;
  font-weight: 400;
}
.pagination li a.active {
  background: #4D73FC;
  color: #0B0C0D;
}
.pagination li a:hover {
  color: #0B0C0D;
  background: #4D73FC;
}

.modal {
  padding: 0 !important;
  --bs-modal-padding: 0rem !important;
  --bs-modal-margin: 0rem !important;
}
.modal .modal-dialog {
  max-width: 100%;
  transform: unset;
}
@media (max-width: 768px) {
  .modal .modal-dialog {
    max-width: 80%;
  }
}
@media (max-width: 575px) {
  .modal .modal-dialog {
    max-width: 100%;
  }
}
.modal .modal-dialog .modal-content {
  background-color: #ECECF2;
  margin-top: 48px;
  border-radius: 24px;
}
.modal .modal-dialog .modal-content .top_bar {
  background-color: #ECECF2;
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-radius: 16px 16px 0 0;
}
.modal .modal-dialog .modal-content .top_bar .modal-title {
  padding-left: 24px;
  color: #212627;
}
.modal .modal-dialog .modal-content .close {
  border: 0;
  background: transparent;
  color: #4D73FC;
  padding: 10px 20px 0px 10px;
}
.modal .modal-dialog .modal-content .close span {
  display: flex;
  align-items: center;
  gap: 8px;
}
.modal .modal-dialog .modal-content .close span i {
  font-size: 24px;
}
.modal .modal-dialog .modal-content .close span b {
  font-size: 24px;
}
.modal .modal-dialog .modal-content .modal-body video {
  width: 100%;
  border-radius: 0 0 20px 20px;
}
.modal .modal-dialog .modal-content .modal-body iframe {
  border: 0;
  border: none;
  height: 700px;
  width: 100%;
}

/*-------------------------
  layouts
-------------------------*/
/*--------------------------------------------------------------
# Navigation
--------------------------------------------------------------*/
.main-menu {
  width: 100%;
  z-index: 9999;
  position: relative;
  padding: 18px 0;
}
.main-menu .main-menu__block {
  display: flex;
  align-items: center;
  position: relative;
  justify-content: space-between;
}
.main-menu .main-menu__left {
  display: flex;
  align-items: center;
  gap: 84px;
}
.main-menu .main-menu__left .main-menu__logo {
  align-items: center;
  position: relative;
  width: clamp(130px, 9.219vw, 200px);
}
.main-menu .main-menu__left .main-menu__nav {
  margin-left: auto;
  margin-right: auto;
}
.main-menu .main-menu__left .main-menu__nav .main-menu__list,
.main-menu .main-menu__left .main-menu__nav .main-menu__list ul,
.main-menu .main-menu__left .main-menu__nav .stricky-header .main-menu__list,
.main-menu .main-menu__left .main-menu__nav .stricky-header .main-menu__list ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
  align-items: center;
  display: none;
}
@media screen and (min-width: 1200px) {
  .main-menu .main-menu__left .main-menu__nav .main-menu__list,
  .main-menu .main-menu__left .main-menu__nav .main-menu__list ul,
  .main-menu .main-menu__left .main-menu__nav .stricky-header .main-menu__list,
  .main-menu .main-menu__left .main-menu__nav .stricky-header .main-menu__list ul {
    display: flex;
    gap: 32px;
  }
}
.main-menu .main-menu__left .main-menu__nav .main-menu__list > li,
.main-menu .main-menu__left .main-menu__nav .stricky-header .main-menu__list > li {
  padding-top: 8px;
  padding-bottom: 8px;
  position: relative;
  z-index: 999999;
}
.main-menu .main-menu__left .main-menu__nav .main-menu__list > li > a,
.main-menu .main-menu__left .main-menu__nav .stricky-header .main-menu__list > li > a {
  font-size: clamp(16px, 1.094vw, 24px);
  display: flex;
  align-items: center;
  font-family: "Inter", sans-serif;
  color: #212627;
  font-weight: 600;
  line-height: 130%;
  text-transform: capitalize;
  position: relative;
  transition: all 500ms ease;
}
.main-menu .main-menu__left .main-menu__nav .main-menu__list > li > a.active,
.main-menu .main-menu__left .main-menu__nav .stricky-header .main-menu__list > li > a.active {
  color: #4D73FC;
}
.main-menu .main-menu__left .main-menu__nav .main-menu__list > li > a:hover,
.main-menu .main-menu__left .main-menu__nav .stricky-header .main-menu__list > li > a:hover {
  color: #4D73FC;
}
.main-menu .main-menu__left .main-menu__nav .main-menu__list > li.current, .main-menu .main-menu__left .main-menu__nav .main-menu__list > li:hover,
.main-menu .main-menu__left .main-menu__nav .stricky-header .main-menu__list > li.current,
.main-menu .main-menu__left .main-menu__nav .stricky-header .main-menu__list > li:hover {
  color: #4D73FC;
}
.main-menu .main-menu__left .main-menu__nav .main-menu__list li ul,
.main-menu .main-menu__left .main-menu__nav .stricky-header .main-menu__list li ul {
  position: absolute;
  top: 100%;
  min-width: 270px;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  opacity: 0;
  visibility: hidden;
  transform-origin: top center;
  transform: scaleY(0) translateZ(100px);
  transition: 0.3s cubic-bezier(0.18, 0.55, 1, 1);
  z-index: 99;
  background-color: #F8F8FF;
  box-shadow: 0px 10px 60px 0px rgba(255, 255, 255, 0.07);
  padding: 15px 20px 10px;
  border-radius: 15px;
}
.main-menu .main-menu__left .main-menu__nav .main-menu__list li ul li,
.main-menu .main-menu__left .main-menu__nav .stricky-header .main-menu__list li ul li {
  flex: 1 1 100%;
  width: 100%;
  position: relative;
}
.main-menu .main-menu__left .main-menu__nav .main-menu__list li ul li > a,
.main-menu .main-menu__left .main-menu__nav .stricky-header .main-menu__list li ul li > a {
  font-size: 16px;
  line-height: 26px;
  color: black;
  font-weight: 500;
  font-family: "Inter", sans-serif;
  display: flex;
  padding: 7px 20px;
  transition: 400ms;
  margin-bottom: 4px;
  border-radius: 5px;
}
.main-menu .main-menu__left .main-menu__nav .main-menu__list li ul li > a:after,
.main-menu .main-menu__left .main-menu__nav .stricky-header .main-menu__list li ul li > a:after {
  position: absolute;
  right: 15px;
  top: 7px;
  border-radius: 0;
  font-size: 14px;
  font-weight: 400;
  font-family: "Font Awesome 5 Pro";
  content: "\f105";
  color: #F8F8FF;
  visibility: hidden;
  opacity: 0;
  transition: all 500ms ease;
  transform: scale(0);
}
.main-menu .main-menu__left .main-menu__nav .main-menu__list li ul li.current > a, .main-menu .main-menu__left .main-menu__nav .main-menu__list li ul li:hover > a,
.main-menu .main-menu__left .main-menu__nav .stricky-header .main-menu__list li ul li.current > a,
.main-menu .main-menu__left .main-menu__nav .stricky-header .main-menu__list li ul li:hover > a {
  background-color: #4D73FC;
  color: #F8F8FF;
}
.main-menu .main-menu__left .main-menu__nav .main-menu__list li ul li.current > a::after, .main-menu .main-menu__left .main-menu__nav .main-menu__list li ul li:hover > a::after,
.main-menu .main-menu__left .main-menu__nav .stricky-header .main-menu__list li ul li.current > a::after,
.main-menu .main-menu__left .main-menu__nav .stricky-header .main-menu__list li ul li:hover > a::after {
  visibility: visible;
  opacity: 1;
  transform: scale(1);
}
.main-menu .main-menu__left .main-menu__nav .main-menu__list li ul li > ul,
.main-menu .main-menu__left .main-menu__nav .stricky-header .main-menu__list li ul li > ul {
  top: 0;
  left: calc(100% + 20px);
}
.main-menu .main-menu__left .main-menu__nav .main-menu__list li ul li > ul.right-align,
.main-menu .main-menu__left .main-menu__nav .stricky-header .main-menu__list li ul li > ul.right-align {
  top: 0;
  left: auto;
  right: 100%;
}
.main-menu .main-menu__left .main-menu__nav .main-menu__list li ul li > ul ul,
.main-menu .main-menu__left .main-menu__nav .stricky-header .main-menu__list li ul li > ul ul {
  display: none;
}
.main-menu .main-menu__left .main-menu__nav .main-menu__list li:hover > ul,
.main-menu .main-menu__left .main-menu__nav .stricky-header .main-menu__list li:hover > ul {
  opacity: 1;
  visibility: visible;
  gap: 6px;
  transform: scaleY(1) translateZ(0px);
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .main-menu .main-menu__left .main-menu__nav .main-menu__list li:nth-last-child(1) ul li > ul, .main-menu .main-menu__left .main-menu__nav .main-menu__list li:nth-last-child(2) ul li > ul,
  .main-menu .main-menu__left .main-menu__nav .stricky-header .main-menu__list li:nth-last-child(1) ul li > ul,
  .main-menu .main-menu__left .main-menu__nav .stricky-header .main-menu__list li:nth-last-child(2) ul li > ul {
    left: auto;
    right: calc(100% + 20px);
  }
}

.main-menu__right {
  display: flex;
  align-items: center;
  gap: 16px;
}
@media (max-width: 575px) {
  .main-menu__right {
    gap: 14px;
  }
}
.main-menu__right .main-menu__search {
  color: #16191A;
  font-size: 24px;
}
.main-menu__right .main-menu__login {
  position: relative;
  font-size: 21px;
  font-weight: 500;
  line-height: 130%;
  color: #16191A;
  display: flex;
  align-items: center;
  transition: all 500ms ease;
}
.main-menu__right .main-menu__login:hover {
  color: #4D73FC;
}
.main-menu__right .center_slach {
  font-size: 21px;
  font-weight: 500;
  line-height: 130%;
  color: #16191A;
}
.main-menu__right .main-menu-signup__login {
  display: flex;
  align-items: flex-start;
  gap: 4px;
}

.stricky-header {
  position: fixed;
  z-index: 991;
  top: 0;
  left: 0;
  background-color: #F8F8FF;
  box-shadow: 0px 10px 60px 0px RGBA(0, 0, 0, 0.07);
  width: 100%;
  transform: translateY(-120%);
  transition: transform 500ms ease;
  padding: 24px 0;
}
@media (max-width: 1199px) {
  .stricky-header {
    display: none;
  }
}
.stricky-header.stricky-fixed {
  transform: translateY(0);
}

.mobile-nav__toggler {
  font-size: 24px;
  font-weight: 300;
  color: #0B0C0D;
  cursor: pointer;
  transition: 500ms;
}
@media screen and (min-width: 1200px) {
  .mobile-nav__toggler {
    display: none;
  }
}
.mobile-nav__toggler:hover {
  color: #4D73FC;
}

/*--------------------------------------------------------------
# Mobile Nav
--------------------------------------------------------------*/
.mobile-nav__wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 999;
  transform: translateX(-100%);
  transform-origin: left center;
  transition: transform 500ms ease 500ms, visibility 500ms ease 500ms;
  visibility: hidden;
  position: fixed;
}
.mobile-nav__wrapper .container {
  padding-left: 0;
  padding-right: 0;
}
.mobile-nav__wrapper.expanded {
  opacity: 1;
  transform: translateX(0%);
  visibility: visible;
  transition: transform 500ms ease 0ms, visibility 500ms ease 0ms;
}
.mobile-nav__wrapper.expanded .mobile-nav__content {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
  transition: opacity 500ms ease 500ms, visibility 500ms ease 500ms, transform 500ms ease 500ms;
}
.mobile-nav__wrapper .mobile-nav__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #0B0C0D;
  opacity: 0.3;
  cursor: url(../images/close.html), auto;
}
.mobile-nav__wrapper .mobile-nav__content {
  width: 300px;
  background-color: #0B0C0D;
  z-index: 10;
  position: relative;
  height: 100%;
  overflow-y: auto;
  padding-top: 30px;
  padding-bottom: 30px;
  padding-left: 15px;
  padding-right: 15px;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-100%);
  transition: opacity 500ms ease 0ms, visibility 500ms ease 0ms, transform 500ms ease 0ms;
}
.mobile-nav__wrapper .mobile-nav__content .main-menu__nav {
  display: block;
  padding: 0;
}
.mobile-nav__wrapper .mobile-nav__content .logo-box {
  margin-bottom: 40px;
  display: flex;
}
.mobile-nav__wrapper .mobile-nav__content .main-menu__list,
.mobile-nav__wrapper .mobile-nav__content .main-menu__list ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}
.mobile-nav__wrapper .mobile-nav__content .main-menu__list ul {
  display: none;
  border-top: 1px solid RGBA(255, 255, 255, 0.1);
}
.mobile-nav__wrapper .mobile-nav__content .main-menu__list ul li > a {
  padding-left: 1em;
}
.mobile-nav__wrapper .mobile-nav__content .main-menu__list ul li:not(:last-child) {
  border-bottom: 1px solid RGBA(255, 255, 255, 0.1);
}
.mobile-nav__wrapper .mobile-nav__content .main-menu__list li:not(:last-child) {
  border-bottom: 1px solid RGBA(255, 255, 255, 0.1);
}
.mobile-nav__wrapper .mobile-nav__content .main-menu__list li a {
  display: flex;
  justify-content: space-between;
  line-height: 30px;
  color: #F8F8FF;
  font-size: 16px;
  font-family: "Inter", sans-serif;
  text-transform: capitalize;
  font-weight: 600;
  height: 46px;
  letter-spacing: 0.48px;
  align-items: center;
  transition: 500ms;
}
.mobile-nav__wrapper .mobile-nav__content .main-menu__list li a.expanded {
  color: #4D73FC;
}
.mobile-nav__wrapper .mobile-nav__content .main-menu__list li a button {
  width: 30px;
  height: 30px;
  background-color: #4D73FC;
  border: none;
  outline: none;
  color: #F8F8FF;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  transform: rotate(-90deg);
  transition: transform 500ms ease;
}
.mobile-nav__wrapper .mobile-nav__content .main-menu__list li a button.expanded {
  transform: rotate(0deg);
  background-color: #F8F8FF;
  color: #0B0C0D;
}
.mobile-nav__wrapper .mobile-nav__close {
  position: absolute;
  top: 20px;
  right: 15px;
  font-size: 18px;
  color: #F8F8FF;
  cursor: pointer;
}

.mobile-nav__social {
  display: flex;
  align-items: center;
}
.mobile-nav__social a {
  font-size: 16px;
  color: #F8F8FF;
  transition: 500ms;
}
.mobile-nav__social a + a {
  margin-left: 20px;
}
.mobile-nav__social a:hover {
  color: #4D73FC;
}

.mobile-nav__contact {
  margin-bottom: 0;
  margin-top: 20px;
  margin-bottom: 20px;
}
.mobile-nav__contact li {
  color: #F8F8FF;
  font-size: 14px;
  font-weight: 500;
  position: relative;
  display: flex;
  align-items: center;
}
.mobile-nav__contact li + li {
  margin-top: 15px;
}
.mobile-nav__contact li a {
  color: inherit;
  transition: 500ms;
}
.mobile-nav__contact li a:hover {
  color: #4D73FC;
}
.mobile-nav__contact li i {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #4D73FC;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  font-size: 14px;
  margin-right: 10px;
  color: #F8F8FF;
}

.mobile-nav__container .main-menu__logo,
.mobile-nav__container .main-menu__right {
  display: none;
}

@media (max-width: 575px) {
  footer {
    padding: 26px 10px !important;
  }
}
footer form {
  position: relative;
}
footer form input {
  position: relative;
}
footer form button {
  background: transparent;
  border: 0;
  font-size: 16px;
  font-weight: 500;
  line-height: 150%;
  letter-spacing: 0.32px;
  position: absolute;
  top: 16px;
  right: 16px;
}
footer form button::before {
  content: "";
  height: 24px;
  width: 1px;
  background: #16191A;
  position: absolute;
  left: -8px;
}
footer .social-link ul {
  display: flex;
  gap: 16px;
}
footer .social-link ul li a {
  background-color: #4D73FC;
  padding: 12px;
  border-radius: 10px;
  transition: all 0.5s ease-in-out;
}
footer .social-link ul li a:hover {
  background-color: #FC9C4D;
}

.hero-banner-1 {
  width: 100%;
  background: linear-gradient(180deg, rgba(248, 248, 255, 0) 0%, #f8f8ff 100%);
}
.hero-banner-1 .content {
  position: relative;
  padding: 100px 0 35px 0;
}
@media (max-width: 1399px) {
  .hero-banner-1 .content {
    padding: 80px 0 35px 0;
  }
}
@media (max-width: 1199px) {
  .hero-banner-1 .content {
    padding: 50px 0 35px 0;
  }
}
@media (max-width: 1099px) {
  .hero-banner-1 .content {
    padding: 35px 0 35px 0;
  }
}
.hero-banner-1 .content .image,
.hero-banner-1 .content .content-block {
  position: relative;
  z-index: 100;
}
.hero-banner-1 .content .vector-image svg {
  position: absolute;
  bottom: 4px;
  right: 153px;
  width: 80%;
  z-index: 10;
}
@media (max-width: 1599px) {
  .hero-banner-1 .content .vector-image svg {
    right: 25px;
    width: 90%;
  }
}
@media (max-width: 1399px) {
  .hero-banner-1 .content .vector-image svg {
    right: 93px;
    width: 80%;
    bottom: -8px;
    transform: rotate(172deg);
  }
}
@media (max-width: 1199px) {
  .hero-banner-1 .content .vector-image svg {
    bottom: -30px;
  }
}
@media (max-width: 992px) {
  .hero-banner-1 .content .vector-image svg {
    bottom: 16px;
    transform: rotate(-12deg);
    right: 4%;
  }
}
@media (max-width: 871px) {
  .hero-banner-1 .content .vector-image svg {
    display: none;
  }
}
@media (max-width: 576px) {
  .hero-banner-1 .content .vector-image svg {
    display: block;
    bottom: 2px;
    transform: rotate(212deg);
    width: 91%;
    right: 15%;
  }
}
@media (max-width: 499px) {
  .hero-banner-1 .content .vector-image svg {
    bottom: -24px;
    transform: rotate(218deg);
  }
}
@media (max-width: 430px) {
  .hero-banner-1 .content .vector-image svg {
    bottom: -38px;
  }
}
@media (max-width: 378px) {
  .hero-banner-1 .content .vector-image svg {
    bottom: -52px;
    transform: rotate(219deg);
  }
}
.hero-banner-1 .content .vector-image svg .dashed {
  stroke-dasharray: 22;
  animation: dash 8s linear alternate infinite;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
}
.hero-banner-1 .content .vector-image svg .path {
  stroke-dasharray: 22;
  stroke-dashoffset: 0;
}
@keyframes dash {
  from {
    stroke-dashoffset: 0;
  }
  to {
    stroke-dashoffset: 500;
  }
}
.hero-banner-1 .content .vector-image .location-image {
  position: absolute;
  bottom: 23%;
  right: 6%;
}
@media (max-width: 1599px) {
  .hero-banner-1 .content .vector-image .location-image {
    right: 0%;
  }
}
@media (max-width: 1399px) {
  .hero-banner-1 .content .vector-image .location-image {
    bottom: 54%;
    right: 6%;
  }
}
@media (max-width: 1199px) {
  .hero-banner-1 .content .vector-image .location-image {
    bottom: 57%;
  }
}
@media (max-width: 992px) {
  .hero-banner-1 .content .vector-image .location-image {
    bottom: 63%;
    right: 2%;
  }
}
@media (max-width: 876px) {
  .hero-banner-1 .content .vector-image .location-image {
    display: none;
  }
}

.booking {
  margin-top: -244px;
}
.booking .content {
  padding: clamp(24px, 3.33vw, 128px) clamp(32px, 4.792vw, 184px);
  background: #F8F8FF;
  border-radius: clamp(16px, 1.563vw, 42px);
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 94px 48px 42px 0px rgba(0, 0, 0, 0.01);
}
@media (max-width: 575px) {
  .booking .content {
    padding: 32px 24px;
  }
}
@media (max-width: 400px) {
  .booking .content {
    padding: 24px 16px;
  }
}
.booking .content .card .card-header .nav-tabs li a svg {
  width: clamp(22px, 1.66vw, 48px);
  height: clamp(22px, 1.66vw, 48px);
  transition: all 0.5s ease-in-out;
}
.booking .content .card .card-header .nav-tabs li a svg path {
  transition: all 0.5s ease-in-out;
}
.booking .content .card .card-header .nav-tabs li a.active svg path, .booking .content .card .card-header .nav-tabs li a:hover svg path {
  fill: #F8F8FF;
}
.booking .content .card .card-header .nav-tabs li a.active svg.stroke_svg path, .booking .content .card .card-header .nav-tabs li a:hover svg.stroke_svg path {
  fill: #F8F8FF;
  stroke: #F8F8FF;
}
.booking .content .card .card-body .tab-pane .card .card-header {
  width: 100%;
}
.booking .content .card .card-body .tab-pane .card .card-header .nav-tabs {
  width: 100%;
}
.booking .content .card .card-body .tab-pane .card .card-header .nav-tabs li {
  background: #ECECF2;
  width: calc(33.3% - 1px);
}
@media (max-width: 1199px) {
  .booking .content .card .card-body .tab-pane .card .card-header .nav-tabs li {
    width: calc(50% - 1px);
  }
}
@media (max-width: 838px) {
  .booking .content .card .card-body .tab-pane .card .card-header .nav-tabs li {
    width: calc(100% - 1px);
  }
}
.booking .content .card .card-body .tab-pane .card .card-header .nav-tabs li a.primary-light {
  width: 100%;
  background: #ECECF2;
  border-radius: 5px;
}
.booking .content .card .card-body .tab-pane .card .card-header .nav-tabs li a.active, .booking .content .card .card-body .tab-pane .card .card-header .nav-tabs li a:hover {
  background: #FC9C4D;
}
.booking .content .card .card-body .tab-pane .card .card-header .nav-tabs li.v-2 {
  width: calc(50% - 1px);
}
.booking .content .card .card-body .booking-bar {
  display: flex;
  border: 0px;
}
.booking .content .card .card-body .booking-bar .nav-item .nav-link {
  color: #212627;
  font-family: "Inter", sans-serif;
  font-size: clamp(16px, 1.094vw, 24px);
  font-weight: 500;
  line-height: 130%;
  border: 0;
  padding: clamp(10px, 0.83vw, 24px) clamp(16px, 1.25vw, 32px);
}
.booking .content .card .card-body .booking-bar .nav-item .nav-link.active {
  border-bottom: 2px solid #FC9C4D;
  transition: all 0.5s ease-in-out;
  background: transparent;
}
.booking .content .custom-control {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: clamp(16px, 1.66vw, 42px);
}
.booking .content .booking-info {
  position: relative;
  display: flex;
  padding: 20px 24px;
  align-items: center;
  gap: clamp(20px, 1.66vw, 42px);
  border-radius: 10px;
  background: rgba(77, 115, 252, 0.1);
}
@media (max-width: 1399px) {
  .booking .content .booking-info {
    flex-wrap: wrap;
  }
}
@media (max-width: 1099px) {
  .booking .content .booking-info {
    gap: 16px;
  }
}
@media (max-width: 992px) {
  .booking .content .booking-info {
    display: grid;
    gap: 0;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1400px) {
  .booking .content .booking-info .gap-24 {
    gap: 12px;
  }
}
.booking .content .booking-info.v-2 {
  padding: 8px 24px;
}
.booking .content .booking-info.promo-code {
  padding: 12px 24px;
  border: 1px solid #7A7F85;
}
.booking .content .booking-info.promo-code .sel-input::placeholder {
  font-size: 16px;
}
.booking .content .booking-info .min-230 {
  min-width: 230px;
}
.booking .content .booking-info .input-date-picker,
.booking .content .booking-info .custom-sel-input-block {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  position: relative;
  font-size: clamp(16px, 1.094vw, 24px);
  font-weight: 500;
}
@media (max-width: 1199px) {
  .booking .content .booking-info .input-date-picker,
  .booking .content .booking-info .custom-sel-input-block {
    margin-bottom: 16px;
  }
}
.booking .content .booking-info .input-date-picker.v-2::after,
.booking .content .booking-info .custom-sel-input-block.v-2::after {
  content: "";
  position: absolute;
  top: -12px;
  right: 0;
  height: 56px;
  width: 1px;
  background: #9EA2A8;
}
@media (max-width: 576px) {
  .booking .content .booking-info .input-date-picker.v-2::after,
  .booking .content .booking-info .custom-sel-input-block.v-2::after {
    display: none;
  }
}
.booking .content .booking-info .input-date-picker.location-select,
.booking .content .booking-info .custom-sel-input-block.location-select {
  width: 100%;
}
.booking .content .booking-info .input-date-picker.location-select i,
.booking .content .booking-info .custom-sel-input-block.location-select i {
  position: absolute;
  bottom: 0;
  color: #4D73FC;
  font-size: 24px;
}
.booking .content .booking-info .input-date-picker .sel-input,
.booking .content .booking-info .custom-sel-input-block .sel-input {
  width: 252px;
  color: #16191A;
  font-family: "Inter", sans-serif;
  font-size: clamp(16px, 1.094vw, 24px);
  font-weight: 500;
  line-height: 130%;
  border: none;
  background: transparent;
  padding: 4px 0 0 0;
  box-shadow: none;
}
@media (max-width: 1799px) {
  .booking .content .booking-info .input-date-picker .sel-input,
  .booking .content .booking-info .custom-sel-input-block .sel-input {
    width: 100%;
  }
}
.booking .content .booking-info .input-date-picker .sel-input::placeholder,
.booking .content .booking-info .custom-sel-input-block .sel-input::placeholder {
  color: #7A7F85;
}
.booking .content .booking-info .input-date-picker .sel-input:focus,
.booking .content .booking-info .custom-sel-input-block .sel-input:focus {
  outline: 0;
}
.booking .content .booking-info .input-date-picker .sel-input.location-input,
.booking .content .booking-info .custom-sel-input-block .sel-input.location-input {
  width: 100%;
  padding: 0 0 0 32px;
}
@media (max-width: 1199px) {
  .booking .content .booking-info .input-date-picker .sel-input.calendar,
  .booking .content .booking-info .custom-sel-input-block .sel-input.calendar {
    width: 50%;
  }
}
@media (max-width: 399px) {
  .booking .content .booking-info .input-date-picker .sel-input.calendar,
  .booking .content .booking-info .custom-sel-input-block .sel-input.calendar {
    width: 70%;
  }
}
.booking .content .booking-info .input-date-picker .slector-wrapper,
.booking .content .booking-info .custom-sel-input-block .slector-wrapper {
  display: none;
  position: absolute;
  left: 0;
  top: 60px;
  z-index: 888;
  background: #ECECF2;
  padding-bottom: 16px;
  overflow: auto;
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 94px 48px 42px 0px rgba(0, 0, 0, 0.01);
  border-radius: 8px;
  height: 300px;
  /* Track */
  /* Handle */
}
.booking .content .booking-info .input-date-picker .slector-wrapper::-webkit-scrollbar,
.booking .content .booking-info .custom-sel-input-block .slector-wrapper::-webkit-scrollbar {
  width: 6px;
}
.booking .content .booking-info .input-date-picker .slector-wrapper::-webkit-scrollbar-track,
.booking .content .booking-info .custom-sel-input-block .slector-wrapper::-webkit-scrollbar-track {
  background: transparent;
  padding: 0 2px;
  width: 8px;
}
.booking .content .booking-info .input-date-picker .slector-wrapper::-webkit-scrollbar-thumb,
.booking .content .booking-info .custom-sel-input-block .slector-wrapper::-webkit-scrollbar-thumb {
  width: 8px;
  background: #7A7F85;
  border-radius: 6px;
}
@media screen and (min-width: 768px) {
  .booking .content .booking-info .input-date-picker .slector-wrapper,
  .booking .content .booking-info .custom-sel-input-block .slector-wrapper {
    max-width: 265px !important;
    min-width: 265px !important;
    width: auto;
  }
}
.booking .content .booking-info .input-date-picker .slector-wrapper li.top-set,
.booking .content .booking-info .custom-sel-input-block .slector-wrapper li.top-set {
  position: sticky;
  left: 0;
  top: 0;
  width: 100%;
}
.booking .content .booking-info .input-date-picker .slector-wrapper li span,
.booking .content .booking-info .custom-sel-input-block .slector-wrapper li span {
  border-radius: 8px 8px 0 0;
  display: block;
  padding: 10px 16px;
  background: #F8F8FF;
}
.booking .content .booking-info .input-date-picker .slector-wrapper li .sel-option,
.booking .content .booking-info .custom-sel-input-block .slector-wrapper li .sel-option {
  width: 100%;
  background: transparent;
  border: none;
  padding: 6px 10px;
  display: flex;
  align-items: center;
  gap: 6px;
  color: #16191A;
  cursor: pointer;
  transition: all 0.5s ease-in-out;
}
.booking .content .booking-info .input-date-picker .slector-wrapper li .sel-option:hover,
.booking .content .booking-info .custom-sel-input-block .slector-wrapper li .sel-option:hover {
  background: #7A7F85;
}
.booking .content .booking-info .input-date-picker i,
.booking .content .booking-info .custom-sel-input-block i {
  font-size: clamp(16px, 1.094vw, 24px);
  color: #4D73FC;
}
@media (max-width: 1199px) {
  .booking .content .booking-info .arrows {
    margin: 32px 0;
  }
}
@media (max-width: 992px) {
  .booking .content .booking-info .arrows svg {
    transform: rotate(0deg);
  }
}
.booking .content .booking-info .passenger-area {
  width: 378px;
  position: absolute;
  top: 72px;
  right: 0;
  display: none;
  z-index: 99999;
}
@media (max-width: 1799px) {
  .booking .content .booking-info .passenger-area {
    top: 89px;
  }
}
@media (max-width: 1299px) {
  .booking .content .booking-info .passenger-area {
    top: 110px;
  }
}
@media (max-width: 1199px) {
  .booking .content .booking-info .passenger-area {
    width: 362px;
    top: 60px;
    left: unset;
    right: 0;
  }
}
@media (max-width: 576px) {
  .booking .content .booking-info .passenger-area {
    right: -36px;
  }
}
@media (max-width: 420px) {
  .booking .content .booking-info .passenger-area {
    width: 294px;
  }
}
.booking .content .booking-info .vertical-line {
  width: 1px;
  height: 56px;
  background: #9EA2A8;
  margin: 0 16px;
}
@media (max-width: 992px) {
  .booking .content .booking-info .vertical-line {
    height: 36px;
  }
}
.booking .input-date-picker .picker__holder {
  transform: translateY(72px);
}
.booking .input-date-picker.date-time::before {
  content: "";
  position: absolute;
  top: -12px;
  left: -24px;
  height: 56px;
  width: 1px;
  background: #9EA2A8;
}
@media (max-width: 576px) {
  .booking .input-date-picker.date-time::before {
    display: none;
  }
}

.radio-tile-group {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  grid-column-gap: clamp(16px, 1.58vw, 40px);
  grid-row-gap: clamp(16px, 1.58vw, 40px);
}
.radio-tile-group .input-container {
  position: relative;
}
.radio-tile-group .input-container .radio-button {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  margin: 0;
  cursor: pointer;
}
.radio-tile-group .input-container .radio-button:checked + .radio-tile {
  background: #4D73FC;
  border: 1px solid #212627;
  color: white;
  transition: all 0.5s ease-in-out;
}
.radio-tile-group .input-container .radio-button:checked + .radio-tile .radio-tile-label {
  color: #F8F8FF;
}
.radio-tile-group .input-container .radio-tile {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 52px;
  border: 1px solid #212627;
  background: rgba(77, 115, 252, 0.1);
  border-radius: 5px;
  padding: 14px 8px;
  transition: transform 300ms ease;
}
@media (max-width: 420px) {
  .radio-tile-group .input-container .radio-tile {
    height: 50px;
  }
}
.radio-tile-group .input-container .radio-tile.sidebar-radio {
  padding: 16px 22px;
  height: 59px;
}
.radio-tile-group .input-container .radio-tile.sidebar-departure-radio {
  padding: 16px 6px;
  height: 100px;
}
.radio-tile-group .input-container .radio-tile.wizard-page {
  width: 100%;
  height: 128px;
  border-radius: 20px;
  border: 0;
}
@media (max-width: 576px) {
  .radio-tile-group .input-container .radio-tile.wizard-page {
    margin-bottom: 16px;
  }
}
.radio-tile-group .input-container .radio-tile i {
  font-size: 20px;
}
.radio-tile-group .input-container .radio-tile-label {
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.32px;
  letter-spacing: 150%;
  color: #16191A;
}
.radio-tile-group .input-container .radio-tile-label.sidebar-label {
  font-size: 20px;
  letter-spacing: 130%;
}
@media (max-width: 1074px) {
  .radio-tile-group .input-container .radio-tile-label.sidebar-label {
    font-size: 16px;
  }
}
.radio-tile-group .input-container .radio-tile-label.departure-radio {
  color: #4D73FC;
}
.radio-tile-group .input-container .radio-tile-label.wizard-label {
  font-size: 27px;
}

.benefit {
  position: relative;
  z-index: -1;
}
.benefit .benefit-block {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: clamp(16px, 1.33vw, 48px);
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 94px 48px 42px 0px rgba(0, 0, 0, 0.01);
  padding: clamp(16px, 1.25vw, 36px);
  border-radius: 10px;
}
@media (max-width: 575px) {
  .benefit .benefit-block {
    padding: 16px 12px;
  }
}
.benefit .benefit-block .image-box img {
  width: clamp(48px, 3.33vw, 96px);
}
@media (max-width: 767px) {
  .benefit .benefit-block .image-box img {
    width: 40px;
    height: 40px;
  }
}
@media (max-width: 575px) {
  .benefit .benefit-block .image-box img {
    width: 32px;
    height: 32px;
  }
}

@media (max-width: 767px) {
  .benefit-box img {
    width: 48px;
    height: 48px;
  }
}
@media (max-width: 575px) {
  .benefit-box img {
    width: 38px;
    height: 38px;
  }
}

.our-history .benefit-box {
  padding: clamp(16px, 1.66vw, 48px);
}
.our-history .benefit-box img {
  width: clamp(48px, 3.33vw, 96px);
  height: clamp(48px, 3.33vw, 96px);
}
@media (max-width: 575px) {
  .our-history .benefit-box img {
    width: 38px;
    height: 38px;
  }
}

.flight-card .flight-card-slider {
  margin: 0 -16px;
}
.flight-card .flight-deal-block {
  border-radius: 10px;
  margin: 0 16px;
  position: relative;
  overflow: hidden;
}
.flight-card .flight-deal-block .image-box {
  border-radius: 10px;
  overflow: hidden;
}
.flight-card .flight-deal-block .image-box img {
  border-radius: 10px;
  -webkit-transition: all 0.6s linear 0s;
  transition: all 0.6s linear 0s;
}
.flight-card .flight-deal-block:hover .image-box img {
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 94px 48px 42px 0px rgba(0, 0, 0, 0.01);
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
  -webkit-transition: all 0.6s linear 0s;
  transition: all 0.6s linear 0s;
}

.global-travel-hotel .hotel-block {
  position: relative;
  overflow: hidden;
  border-radius: 10px;
}
.global-travel-hotel .hotel-block .image-box {
  overflow: hidden;
  border-radius: 10px;
  position: relative;
}
.global-travel-hotel .hotel-block .image-box img {
  border-radius: 10px;
  -webkit-transition: all 0.6s linear 0s;
  transition: all 0.6s linear 0s;
}
.global-travel-hotel .hotel-block .image-box .price {
  position: absolute;
  top: 16px;
  right: -140px;
  transition: all 0.5s ease-in-out;
  background: #F8F8FF;
  padding: 12px 24px;
  border-radius: 10px 0 0 10px;
}
.global-travel-hotel .hotel-block:hover .image-box img {
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 94px 48px 42px 0px rgba(0, 0, 0, 0.01);
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
  -webkit-transition: all 0.6s linear 0s;
  transition: all 0.6s linear 0s;
}
.global-travel-hotel .hotel-block:hover .image-box .price {
  top: 16px;
  right: 0px;
  transition: all 0.5s ease-in-out;
}

.travel-sec {
  background: url("../media/backgrounds/inner-banner-1.png");
  background-repeat: no-repeat;
  background-position: center;
  padding: 5.938vw 0 3vw;
  position: relative;
}
@media (max-width: 992px) {
  .travel-sec {
    background-size: cover;
    padding: 40px 0 40px;
  }
}
@media (max-width: 767px) {
  .travel-sec {
    padding: 40px 0 40px;
  }
}
.travel-sec .cloud-vector-block {
  position: relative;
}
.travel-sec .cloud-vector-block .cloud-vector {
  position: absolute;
  top: -102px;
  right: 0;
}
@media (max-width: 992px) {
  .travel-sec .cloud-vector-block .cloud-vector {
    display: none;
  }
}
.travel-sec .line-vector {
  position: absolute;
  top: 11%;
  left: 14%;
  width: 52%;
  transform: rotate(-9deg);
}
@media (max-width: 1399px) {
  .travel-sec .line-vector {
    display: none;
  }
}

.left-content {
  position: relative;
}
@media (max-width: 992px) {
  .left-content {
    text-align: center;
  }
}
.left-content .text {
  display: flex;
  flex-wrap: wrap;
  gap: clamp(12px, 0.83vw, 24px);
  align-items: center;
}
.left-content .text span {
  padding: 0 16px;
}
@media (max-width: 992px) {
  .left-content .text {
    justify-content: center;
  }
}
@media (max-width: 992px) {
  .left-content a {
    margin: 0 auto;
  }
}

.right-images-block {
  display: grid;
  justify-content: end;
  position: relative;
  z-index: 1;
}
.right-images-block .border-image {
  position: absolute;
  z-index: -1;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
@media (max-width: 1699px) {
  .right-images-block .border-image {
    width: 70%;
  }
}
@media (max-width: 1199px) {
  .right-images-block .border-image {
    width: 71%;
  }
}
.right-images-block .side-image {
  border-radius: 38px;
  border: 5px solid #F8F8FF;
}
@media (max-width: 1399px) {
  .right-images-block .side-image {
    border-radius: 32px;
  }
}
@media (max-width: 1199px) {
  .right-images-block .side-image {
    border-radius: 25px;
  }
}
@media (max-width: 992px) {
  .right-images-block .side-image {
    border-radius: 20px;
    border: 3px solid #F8F8FF;
  }
}
@media (max-width: 767px) {
  .right-images-block .side-image {
    border-radius: 15px;
  }
}
@media (max-width: 399px) {
  .right-images-block .side-image {
    border-radius: 8px;
    border: 2px solid #F8F8FF;
  }
}
.right-images-block .center-image {
  border-radius: 60px;
  border: 5px solid #F8F8FF;
}
@media (max-width: 1399px) {
  .right-images-block .center-image {
    border-radius: 50px;
  }
}
@media (max-width: 1199px) {
  .right-images-block .center-image {
    border-radius: 40px;
  }
}
@media (max-width: 992px) {
  .right-images-block .center-image {
    border-radius: 30px;
  }
}
@media (max-width: 767px) {
  .right-images-block .center-image {
    border-radius: 20px;
    border: 3px solid #F8F8FF;
  }
}

.achievement .achievements-block {
  padding: clamp(32px, 3.333vw, 96px);
}
@media (max-width: 576px) {
  .achievement .achievements-block {
    padding: 24px 16px;
  }
}
.achievement .counter-section .counter-count {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: clamp(12px, 0.83vw, 24px);
  padding: clamp(16px, 1.66vw, 48px) clamp(20px, 2.2vw, 54px);
  border-radius: 10px;
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 94px 48px 42px 0px rgba(0, 0, 0, 0.01);
}
.achievement .counter-section .counter-count img {
  width: clamp(32px, 2.5vw, 64px);
  height: clamp(32px, 2.5vw, 64px);
}
.achievement .achievement-image {
  height: 100%;
  object-fit: cover;
}

.testimonials .testimonials-box {
  padding: clamp(48px, 5.625vw, 180px);
}
@media (max-width: 576px) {
  .testimonials .testimonials-box {
    padding: 32px 20px;
  }
}
.testimonials .testimonial-slider {
  margin: 0 16px 0 0;
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 94px 48px 42px 0px rgba(0, 0, 0, 0.01);
  background: #ECECF2;
  border-radius: 10px;
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03);
}
.testimonials .testimonial-slider .slick-prev {
  top: 64px;
  right: 16%;
}
.testimonials .testimonial-slider .slick-prev::before {
  content: "\f177";
  color: #0B0C0D;
  background-color: #ECECF2;
  box-shadow: none;
}
.testimonials .testimonial-slider .slick-next {
  top: 64px;
  right: 10%;
}
.testimonials .testimonial-slider .slick-next::before {
  content: "\f178";
  color: #0B0C0D;
  background-color: #ECECF2;
  box-shadow: none;
}
.testimonials .review-block .user-image {
  margin-right: 16px;
}
.testimonials .review-block .user-image img {
  width: 64px;
  height: 64px;
  margin-right: -28px;
  position: relative;
}
.testimonials .review-block .user-image img:nth-child(1) {
  z-index: 15;
}
.testimonials .review-block .user-image img:nth-child(2) {
  z-index: 10;
}
.testimonials .review-block .user-image img:nth-child(3) {
  z-index: 5;
}
@media (max-width: 576px) {
  .testimonials .review-block h6 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    line-clamp: 4;
    -webkit-box-orient: vertical;
  }
}

.news-blog .vr-line {
  height: 24px;
  width: 2px;
  background-color: #7A7F85;
}
.news-blog .nav {
  display: flex;
  justify-content: center;
  gap: 16px;
  border-radius: 5px;
  border: 0;
  background: #F8F8FF;
  text-align: center;
  width: fit-content;
  margin: 0 auto;
}
@media (max-width: 452px) {
  .news-blog .nav {
    gap: 8px;
  }
}
.news-blog .nav .nav-item .nav-link {
  color: #212627;
  font-family: "Inter", sans-serif;
  font-weight: 500;
  font-size: 21px;
  font-style: normal;
  line-height: 130%;
  letter-spacing: 0.95px;
  border-radius: 5px;
  border: 0px;
  padding: 16px 8px;
}
.news-blog .nav .nav-item .nav-link.active {
  color: #F8F8FF;
  background: #4D73FC;
  transition: all 0.5s ease-in-out;
  border: 0px;
  padding: 16px 24px;
}
.news-blog .nav .vr-line {
  height: 27.5px;
  width: 1px;
  margin-top: 16px;
  background: #7A7F85;
}

.blog-box .image-box {
  border-radius: 10px;
  overflow: hidden;
  height: 100%;
  object-fit: cover;
}
.blog-box .image-box a {
  height: 100%;
  object-fit: cover;
}
.blog-box .image-box img {
  height: 100%;
  object-fit: cover;
  -webkit-transition: all 0.6s linear 0s;
  transition: all 0.6s linear 0s;
}
.blog-box .content-box h5 a {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}
.blog-box .content-box p {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}
.blog-box:hover .image-box img {
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 94px 48px 42px 0px rgba(0, 0, 0, 0.01);
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
  -webkit-transition: all 0.6s linear 0s;
  transition: all 0.6s linear 0s;
}

.blog-video-sec .video-block {
  position: relative;
  padding: 24px 24px 0 24px;
}
.blog-video-sec .video-block .video-btn {
  position: absolute;
  top: 61%;
  left: 48%;
}
@media (max-width: 1099px) {
  .blog-video-sec .video-block .video-btn {
    top: 65%;
    left: 47%;
  }
}
@media (max-width: 836px) {
  .blog-video-sec .video-block .video-btn {
    top: 69%;
    left: 46%;
  }
}
@media (max-width: 609px) {
  .blog-video-sec .video-block .video-btn {
    top: 73%;
    left: 47%;
    width: 8%;
  }
}
@media (max-width: 480px) {
  .blog-video-sec .video-block .video-btn {
    top: 78%;
    left: 46%;
  }
}
@media (max-width: 430px) {
  .blog-video-sec .video-block .video-btn {
    top: 80%;
  }
}
@media (max-width: 373px) {
  .blog-video-sec .video-block .video-btn {
    top: 83%;
  }
}

.blog-detail .blog_info_bar {
  display: inline-flex;
  gap: clamp(20px, 1.33vw, 48px);
  flex-wrap: wrap;
}
.blog-detail .blog_info_bar .author {
  display: flex;
  align-items: center;
  gap: 8px;
}
.blog-detail .blog_info_bar .author img {
  border-radius: 50%;
}
.blog-detail .result-tags {
  display: flex;
  gap: clamp(20px, 1.33vw, 48px);
}
.blog-detail .tag-words {
  display: flex;
  gap: 16px;
}
.blog-detail .article_detail-img {
  height: 100%;
  object-fit: cover;
}
.blog-detail .architex-tilt {
  height: 100%;
  object-fit: cover;
}
.blog-detail .architex-tilt img {
  height: 100%;
  object-fit: cover;
}
.blog-detail .qoute_img {
  margin-top: 108px;
}
@media (max-width: 992px) {
  .blog-detail .qoute_img {
    margin-top: 24px;
    text-align: right;
  }
}

.title-banner {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  background: url("../media/backgrounds/title-banner.png");
  background-repeat: no-repeat;
  background-size: cover;
}
.title-banner .banner-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  padding: 32px 16px;
}
.title-banner .banner-area.v-2 {
  padding: 32px 16px 275px;
}
.title-banner .banner-area .left-image {
  width: 17%;
}
.title-banner .banner-area .content-box {
  text-align: center;
  padding-left: 6%;
}
@media (max-width: 575px) {
  .title-banner .banner-area .content-box h1 {
    font-size: 26px;
  }
}
.title-banner .banner-area .content-box .title-break {
  display: none;
}
@media (max-width: 899px) {
  .title-banner .banner-area .content-box .title-break {
    display: block;
  }
}
@media (max-width: 575px) {
  .title-banner .banner-area .content-box .title-break {
    display: none;
  }
}
.title-banner .banner-area .right-image {
  width: 29%;
}
.title-banner .banner-area .right-image.tour-image {
  mix-blend-mode: darken;
}

.flight-listing-page .flight-block .flight-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media (max-width: 1199px) {
  .flight-listing-page .flight-block .flight-area {
    display: grid;
    justify-content: center;
    gap: 32px;
  }
}
.flight-listing-page .flight-block .flight-area .airline-name {
  display: flex;
  gap: 16px;
  align-items: center;
}
.flight-listing-page .flight-block .flight-area .airline-name img {
  border-radius: 50%;
}
@media (max-width: 1499px) {
  .flight-listing-page .flight-block .flight-area .airline-name {
    display: block;
  }
}
@media (max-width: 1199px) {
  .flight-listing-page .flight-block .flight-area .airline-name {
    text-align: center;
  }
}
.flight-listing-page .flight-block .flight-area .flight-detail {
  display: flex;
  align-items: center;
  gap: 32px;
}
@media (max-width: 1699px) {
  .flight-listing-page .flight-block .flight-area .flight-detail {
    gap: 8px;
  }
}
@media (max-width: 1199px) {
  .flight-listing-page .flight-block .flight-area .flight-detail {
    gap: 32px;
  }
}
.flight-listing-page .flight-block .flight-area .flight-button {
  display: flex;
  align-items: center;
  gap: 32px;
}
@media (max-width: 1599px) {
  .flight-listing-page .flight-block .flight-area .flight-button {
    gap: 8px;
    display: block;
  }
}
@media (max-width: 1599px) {
  .flight-listing-page .flight-block .flight-area .flight-button .amount {
    display: flex;
    flex-direction: row-reverse;
    justify-content: center;
    gap: 8px;
  }
}
@media (max-width: 1199px) {
  .flight-listing-page .flight-block .flight-area .flight-button .amount {
    margin-bottom: 8px;
  }
}
@media (max-width: 1199px) {
  .flight-listing-page .flight-block .flight-area .flight-button {
    margin: 0 auto;
  }
}
.flight-listing-page .accordion-button:is(:not(.collapsed)) i.fa-chevron-down:before {
  content: "\f077" !important;
}

@media (max-width: 992px) {
  .time-detail {
    padding-top: 40px;
  }
}
@media (max-width: 767px) {
  .time-detail {
    padding-top: 10px;
    text-align: center;
  }
}
.time-detail .flight-date {
  padding: 12px;
  background: #FC9C4D;
  border-radius: 5px;
  display: inline-flex;
  color: #F8F8FF;
}

.detail-block {
  background: rgba(77, 115, 252, 0.1);
  padding: 64px 24px;
  border-radius: 10px;
}
.detail-block img {
  border-radius: 50%;
}
@media (max-width: 767px) {
  .detail-block {
    padding: 32px 24px;
  }
}
@media (max-width: 576px) {
  .detail-block {
    text-align: center;
  }
}

.sidebar .sidebar-title {
  background: rgba(77, 115, 252, 0.2);
  padding: 16px 0;
  text-align: center;
  border-radius: 10px 10px 0 0;
}
.sidebar .filter-block .title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.sidebar .filter-block .content-block .custom-control a {
  font-size: 21px;
  font-weight: 500;
}
.sidebar .filter-block .content-block .custom-control a .fas {
  font-size: 27px;
}
.sidebar .filter-block .filter-checkbox .allow_over {
  font-size: 21px;
  font-weight: 500;
  color: #16191A;
}
.sidebar .filter-block .list li {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.sidebar .filter-block .list li ul li {
  display: flex;
}
.sidebar .filter-block .slider-track .irs--big {
  height: 22px;
  margin-bottom: 0;
}
.sidebar .filter-block .slider-track .irs--big .irs-line {
  top: 12px;
  height: 3px;
  border: 0;
  background: #C6CBD2;
  cursor: pointer;
  border-radius: 0;
}
.sidebar .filter-block .slider-track .irs--big .irs-bar {
  top: 12px;
  height: 3px;
  border: 0;
  background: #4D73FC;
  box-shadow: none;
  cursor: pointer;
}
.sidebar .filter-block .slider-track .irs--big .irs-handle {
  content: "";
  border-radius: 0;
  top: 1px;
  height: 24px;
  width: 24px;
  background-color: #4D73FC;
  background: #4D73FC;
  border: 0;
  border-radius: 24px;
  box-shadow: none;
}
.sidebar .filter-block .slider-track .irs--big .irs-handle:hover {
  cursor: pointer;
}
.sidebar .filter-block .colors label {
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  font-family: "Inter", sans-serif;
  font-weight: 600;
  font-size: 21px;
  line-height: 130%;
}
@media (max-width: 992px) {
  .sidebar .filter-block .colors label {
    font-size: 20px;
  }
}
@media (max-width: 767px) {
  .sidebar .filter-block .colors label {
    font-size: 18px;
  }
}
@media (max-width: 575px) {
  .sidebar .filter-block .colors label {
    font-size: 16px;
  }
}
.sidebar .filter-block .colors label input {
  display: none;
}
.sidebar .filter-block .colors label .color {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}
.sidebar .filter-block .colors label .color.non-selected {
  border: 0;
}
.sidebar .filter-block .colors label .color.selected {
  border: 2px solid #F8F8FF;
  width: 22px;
  height: 22px;
}
.sidebar .content-block .custom-control-label {
  color: #16191A;
}

.hotel-sidebar {
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 94px 48px 42px 0px rgba(0, 0, 0, 0.01);
  border-radius: 10px;
}
.hotel-sidebar .hotel-sidebar_title {
  padding: 16px 32px;
  background: rgba(77, 115, 252, 0.2);
  border-radius: 10px 10px 0 0;
}
.hotel-sidebar .filter-block {
  border-radius: 0 0 10px 10px;
}
.hotel-sidebar .contact-form {
  border-radius: 0 0 10px 10px;
  padding: clamp(16px, 1.25vw, 32px);
}
.hotel-sidebar .contact-form .hotel-info {
  display: flex;
  padding: 8px 24px;
  align-items: center;
  gap: 32px;
  border-radius: 10px;
  background: rgba(77, 115, 252, 0.1);
  border: 1px solid #212627;
}
.hotel-sidebar .contact-form .hotel-info.car-listing {
  gap: 0;
}
@media (max-width: 1099px) {
  .hotel-sidebar .contact-form .hotel-info {
    gap: 16px;
  }
}
@media (max-width: 992px) {
  .hotel-sidebar .contact-form .hotel-info {
    display: grid;
    gap: 0;
  }
}
.hotel-sidebar .contact-form .hotel-info .input-date-picker,
.hotel-sidebar .contact-form .hotel-info .custom-sel-input-block {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  position: relative;
}
@media (max-width: 1199px) {
  .hotel-sidebar .contact-form .hotel-info .input-date-picker,
  .hotel-sidebar .contact-form .hotel-info .custom-sel-input-block {
    margin-bottom: 16px;
  }
}
.hotel-sidebar .contact-form .hotel-info .input-date-picker.location-select,
.hotel-sidebar .contact-form .hotel-info .custom-sel-input-block.location-select {
  width: 100%;
  position: relative;
}
.hotel-sidebar .contact-form .hotel-info .input-date-picker.location-select i,
.hotel-sidebar .contact-form .hotel-info .custom-sel-input-block.location-select i {
  position: absolute;
  bottom: 0;
  color: #4D73FC;
  font-size: 24px;
}
.hotel-sidebar .contact-form .hotel-info .input-date-picker.location-select::after,
.hotel-sidebar .contact-form .hotel-info .custom-sel-input-block.location-select::after {
  content: "\f078";
  position: absolute;
  top: 16px;
  right: 0;
  font-family: "Font Awesome 5 Pro";
  display: none;
}
.hotel-sidebar .contact-form .hotel-info .input-date-picker .sel-input,
.hotel-sidebar .contact-form .hotel-info .custom-sel-input-block .sel-input {
  width: 252px;
  color: #16191A;
  font-family: "Inter", sans-serif;
  font-size: 16px;
  font-weight: 500;
  line-height: 130%;
  border: none;
  background: transparent;
  padding: 4px 0 0 0;
  box-shadow: none;
}
@media (max-width: 1799px) {
  .hotel-sidebar .contact-form .hotel-info .input-date-picker .sel-input,
  .hotel-sidebar .contact-form .hotel-info .custom-sel-input-block .sel-input {
    width: 100%;
  }
}
.hotel-sidebar .contact-form .hotel-info .input-date-picker .sel-input::placeholder,
.hotel-sidebar .contact-form .hotel-info .custom-sel-input-block .sel-input::placeholder {
  color: #212627;
}
.hotel-sidebar .contact-form .hotel-info .input-date-picker .sel-input:focus,
.hotel-sidebar .contact-form .hotel-info .custom-sel-input-block .sel-input:focus {
  outline: 0;
}
.hotel-sidebar .contact-form .hotel-info .input-date-picker .sel-input.location-input,
.hotel-sidebar .contact-form .hotel-info .custom-sel-input-block .sel-input.location-input {
  width: 100%;
  padding: 0 0 0 32px;
}
@media (max-width: 1199px) {
  .hotel-sidebar .contact-form .hotel-info .input-date-picker .sel-input.calendar,
  .hotel-sidebar .contact-form .hotel-info .custom-sel-input-block .sel-input.calendar {
    width: 50%;
  }
}
@media (max-width: 399px) {
  .hotel-sidebar .contact-form .hotel-info .input-date-picker .sel-input.calendar,
  .hotel-sidebar .contact-form .hotel-info .custom-sel-input-block .sel-input.calendar {
    width: 70%;
  }
}
.hotel-sidebar .contact-form .hotel-info .input-date-picker .slector-wrapper,
.hotel-sidebar .contact-form .hotel-info .custom-sel-input-block .slector-wrapper {
  display: none;
  position: absolute;
  left: 0;
  top: 60px;
  z-index: 888;
  background: #ECECF2;
  padding-bottom: 16px;
  overflow: auto;
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 94px 48px 42px 0px rgba(0, 0, 0, 0.01);
  border-radius: 8px;
  height: 300px;
  /* Track */
  /* Handle */
}
.hotel-sidebar .contact-form .hotel-info .input-date-picker .slector-wrapper::-webkit-scrollbar,
.hotel-sidebar .contact-form .hotel-info .custom-sel-input-block .slector-wrapper::-webkit-scrollbar {
  width: 6px;
}
.hotel-sidebar .contact-form .hotel-info .input-date-picker .slector-wrapper::-webkit-scrollbar-track,
.hotel-sidebar .contact-form .hotel-info .custom-sel-input-block .slector-wrapper::-webkit-scrollbar-track {
  background: transparent;
  padding: 0 2px;
  width: 8px;
}
.hotel-sidebar .contact-form .hotel-info .input-date-picker .slector-wrapper::-webkit-scrollbar-thumb,
.hotel-sidebar .contact-form .hotel-info .custom-sel-input-block .slector-wrapper::-webkit-scrollbar-thumb {
  width: 8px;
  background: #7A7F85;
  border-radius: 6px;
}
@media screen and (min-width: 768px) {
  .hotel-sidebar .contact-form .hotel-info .input-date-picker .slector-wrapper,
  .hotel-sidebar .contact-form .hotel-info .custom-sel-input-block .slector-wrapper {
    max-width: 265px !important;
    min-width: 265px !important;
    width: auto;
  }
}
.hotel-sidebar .contact-form .hotel-info .input-date-picker .slector-wrapper li.top-set,
.hotel-sidebar .contact-form .hotel-info .custom-sel-input-block .slector-wrapper li.top-set {
  position: sticky;
  left: 0;
  top: 0;
  width: 100%;
}
.hotel-sidebar .contact-form .hotel-info .input-date-picker .slector-wrapper li span,
.hotel-sidebar .contact-form .hotel-info .custom-sel-input-block .slector-wrapper li span {
  border-radius: 8px 8px 0 0;
  display: block;
  padding: 10px 16px;
  background: #F8F8FF;
}
.hotel-sidebar .contact-form .hotel-info .input-date-picker .slector-wrapper li .sel-option,
.hotel-sidebar .contact-form .hotel-info .custom-sel-input-block .slector-wrapper li .sel-option {
  width: 100%;
  background: transparent;
  border: none;
  padding: 6px 10px;
  display: flex;
  align-items: center;
  gap: 6px;
  color: #16191A;
  cursor: pointer;
  transition: all 0.5s ease-in-out;
}
.hotel-sidebar .contact-form .hotel-info .input-date-picker .slector-wrapper li .sel-option:hover,
.hotel-sidebar .contact-form .hotel-info .custom-sel-input-block .slector-wrapper li .sel-option:hover {
  background: #7A7F85;
}
.hotel-sidebar .contact-form .hotel-info .input-date-picker i,
.hotel-sidebar .contact-form .hotel-info .custom-sel-input-block i {
  font-size: 21px;
  color: #4D73FC;
}
.hotel-sidebar .contact-form .hotel-info .seat-booking {
  font-weight: 500;
}
.hotel-sidebar .contact-form .hotel-info .passenger-area {
  width: 400px;
  position: absolute;
  top: 65px;
  left: 0;
  display: none;
  z-index: 999;
}
@media (max-width: 1799px) {
  .hotel-sidebar .contact-form .hotel-info .passenger-area {
    top: 89px;
  }
}
@media (max-width: 1299px) {
  .hotel-sidebar .contact-form .hotel-info .passenger-area {
    top: 110px;
  }
}
@media (max-width: 1199px) {
  .hotel-sidebar .contact-form .hotel-info .passenger-area {
    width: 362px;
    top: 60px;
    left: unset;
    right: 0;
  }
}
@media (max-width: 576px) {
  .hotel-sidebar .contact-form .hotel-info .passenger-area {
    right: -36px;
  }
}
@media (max-width: 420px) {
  .hotel-sidebar .contact-form .hotel-info .passenger-area {
    width: 294px;
  }
}
.hotel-sidebar .slider-track .irs--big {
  height: 22px;
  margin-bottom: 0;
}
.hotel-sidebar .slider-track .irs--big .irs-line {
  top: 12px;
  height: 1px;
  border: 0;
  background: #7A7F85;
  cursor: pointer;
  border-radius: 0;
}
.hotel-sidebar .slider-track .irs--big .irs-bar {
  top: 12px;
  height: 3px;
  border: 0;
  background: #4D73FC;
  box-shadow: none;
  cursor: pointer;
}
.hotel-sidebar .slider-track .irs--big .irs-handle {
  border-radius: 0;
  top: 1px;
  height: 24px;
  width: 24px;
  background-color: #4D73FC;
  background: #4D73FC;
  border: 0;
  border-radius: 24px;
  box-shadow: none;
}
.hotel-sidebar .slider-track .irs--big .irs-handle:hover {
  cursor: pointer;
}

.blog-detail .sidebar form {
  position: relative;
}
.blog-detail .sidebar form input {
  background: rgba(77, 115, 252, 0.1);
  padding: 16px 12px;
  border-radius: 10px;
}
.blog-detail .sidebar form button {
  position: absolute;
  top: 17px;
  right: 12px;
  background: transparent;
  border: 0;
}
.blog-detail .sidebar form button::before {
  position: absolute;
  content: "";
  top: 0px;
  left: -8px;
  height: 27px;
  width: 1px;
  background: #7A7F85;
}
.blog-detail .sidebar .filter-block .tags {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}
.blog-detail .sidebar .filter-block .tags .medics-tag {
  background: rgba(77, 115, 252, 0.1);
  padding: 8px 24px;
  border-radius: 10px;
}
.blog-detail .sidebar .filter-block .article-block {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 16px;
  border-radius: 20px;
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 94px 48px 42px 0px rgba(0, 0, 0, 0.01);
  background: #F8F8FF;
}
@media (max-width: 1699px) {
  .blog-detail .sidebar .filter-block .article-block {
    padding: 12px;
  }
}
.blog-detail .sidebar .filter-block .article-block .article-img-box {
  overflow: hidden;
  display: flex;
  flex-shrink: 0;
}
.blog-detail .sidebar .filter-block .article-block .article-img-box .article-img {
  border-radius: 10px;
  -webkit-transition: all 0.6s linear 0s;
  transition: all 0.6s linear 0s;
}
.blog-detail .sidebar .filter-block .article-block:hover .article-img-box .article-img {
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 94px 48px 42px 0px rgba(0, 0, 0, 0.01);
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
  -webkit-transition: all 0.6s linear 0s;
  transition: all 0.6s linear 0s;
}

.paginations {
  display: flex;
  justify-content: center;
}
.paginations ul {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}
.paginations ul li {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 47px;
  height: 40px;
  padding: 8px 0;
  border-radius: 10px;
  background: rgba(77, 115, 252, 0.1);
  transition: all 0.5s ease-in-out;
}
.paginations ul li a {
  color: #0B0C0D;
}
.paginations ul li:hover {
  transition: all 0.5s ease-in-out;
  background: #4D73FC;
  color: #F8F8FF;
  cursor: pointer;
}
.paginations ul li:hover a {
  transition: all 0.5s ease-in-out;
  color: #F8F8FF;
}
.paginations ul li.active {
  background: #4D73FC;
  color: #F8F8FF;
}
.paginations ul li.active a {
  color: #F8F8FF;
}
.paginations ul li.arrow a i {
  color: #4D73FC;
}
.paginations ul li.arrow:hover a i {
  color: #F8F8FF;
}

.flight-booking .booking-form .sw-theme-basic {
  border: 0;
}
.flight-booking .booking-form .sw-theme-basic .nav .nav-link::after {
  left: 50%;
  top: 50%;
  width: 50%;
}
.flight-booking .booking-form .sw-toolbar .sw-btn {
  display: none;
}
.flight-booking .booking-form .sw-toolbar-elm {
  display: flex;
  justify-content: end;
}
.flight-booking .booking-form .sw-toolbar-elm button {
  font-family: "Inter", sans-serif;
  font-size: 16px;
  font-weight: 500;
  line-height: 150%;
  letter-spacing: 0.32px;
  width: fit-content;
  transition: all 0.5s ease-in-out;
  padding: 16px 32px;
  background-color: #4D73FC;
  color: #F8F8FF;
  border-radius: 10px;
  border: 0px;
}
.flight-booking .booking-form .sw-toolbar-elm button:hover {
  background-color: #F8F8FF;
  color: #0B0C0D;
  transition: all 0.5s ease-in-out;
  box-shadow: 4px 4px 10px 0px rgba(77, 115, 252, 0.3);
}
.flight-booking .booking-form .form-wizard-header .nav {
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 94px 48px 42px 0px rgba(0, 0, 0, 0.01);
  background: #F8F8FF;
  border-radius: 10px;
  margin-bottom: 40px;
  padding: 0 24px;
  height: fit-content;
  display: flex;
  justify-content: space-between;
  position: relative;
}
.flight-booking .booking-form .form-wizard-header .nav .nav-item {
  padding: 16px 0;
  background-color: transparent;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 7;
}
.flight-booking .booking-form .form-wizard-header .nav .nav-item::after {
  content: "";
  background: #C6CBD2;
  position: absolute;
  top: 34px;
  left: 106%;
  width: 161px;
  height: 2px;
  z-index: 99;
}
@media (max-width: 1599px) {
  .flight-booking .booking-form .form-wizard-header .nav .nav-item::after {
    width: 114px;
    top: 33px;
  }
}
@media (max-width: 1399px) {
  .flight-booking .booking-form .form-wizard-header .nav .nav-item::after {
    width: 100px;
    top: 33px;
  }
}
@media (max-width: 1299px) {
  .flight-booking .booking-form .form-wizard-header .nav .nav-item::after {
    width: 90px;
    top: 33px;
  }
}
@media (max-width: 1199px) {
  .flight-booking .booking-form .form-wizard-header .nav .nav-item::after {
    width: 170px;
    top: 33px;
  }
}
@media (max-width: 1052px) {
  .flight-booking .booking-form .form-wizard-header .nav .nav-item::after {
    width: 130px;
    top: 33px;
  }
}
@media (max-width: 910px) {
  .flight-booking .booking-form .form-wizard-header .nav .nav-item::after {
    width: 100px;
    top: 33px;
  }
}
@media (max-width: 826px) {
  .flight-booking .booking-form .form-wizard-header .nav .nav-item::after {
    width: 80px;
    top: 33px;
  }
}
@media (max-width: 726px) {
  .flight-booking .booking-form .form-wizard-header .nav .nav-item::after {
    width: 50px;
    top: 33px;
  }
}
@media (max-width: 626px) {
  .flight-booking .booking-form .form-wizard-header .nav .nav-item::after {
    display: none;
  }
}
.flight-booking .booking-form .form-wizard-header .nav .nav-item.activated .nav-link {
  background-color: #4D73FC;
  color: #F8F8FF;
  border: 0;
  padding: 5px;
}
.flight-booking .booking-form .form-wizard-header .nav .nav-item.activated .nav-link span {
  display: none;
}
.flight-booking .booking-form .form-wizard-header .nav .nav-item.activated i {
  display: none;
  height: 24px;
  width: 24px;
  justify-content: center;
  align-content: center;
  border-radius: 24px;
  font-size: 16px;
  font-weight: 500;
  line-height: 150%;
  background-color: #4D73FC;
  color: #F8F8FF;
}
.flight-booking .booking-form .form-wizard-header .nav .nav-item.activated:is(.activated) i {
  display: grid;
}
.flight-booking .booking-form .form-wizard-header .nav .nav-item.activated:is(.activated) .num {
  display: none;
}
.flight-booking .booking-form .form-wizard-header .nav .nav-item.active .nav-link {
  background: #4D73FC;
  border: 1px solid #4D73FC;
}
.flight-booking .booking-form .form-wizard-header .nav .nav-item.active .nav-link span {
  color: #F8F8FF;
}
.flight-booking .booking-form .form-wizard-header .nav .nav-item .nav-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  padding: 0;
  gap: 8px;
  margin: 0;
  height: auto;
  min-height: auto;
  border: 1px solid #212627;
  border-radius: 40px;
  position: relative;
  overflow: hidden;
  margin-right: 8px;
}
.flight-booking .booking-form .form-wizard-header .nav .nav-item .nav-link span {
  font-size: 21px;
  font-weight: 500;
  line-height: 150%;
  letter-spacing: 0.32px;
  color: #0B0C0D;
  background: #F8F8FF;
  color: #212627;
  background: transparent;
}
.flight-booking .booking-form .form-wizard-header .nav .nav-item .nav-link.active span {
  color: #F8F8FF;
  background: #4D73FC;
  z-index: 2;
  display: none;
}
.flight-booking .booking-form .form-wizard-header .nav .nav-item .nav-link.active i {
  display: block;
}
.flight-booking .booking-form .form-wizard-header .nav .nav-item .nav-link .num {
  height: 24px;
  width: 24px;
  display: grid;
  justify-content: center;
  align-content: center;
  text-align: center;
  border-radius: 24px;
  font-size: 16px;
  font-weight: 500;
  line-height: 150%;
  background-color: #F8F8FF;
  color: #0B0C0D;
  border: 1px solid #0B0C0D;
  padding-right: 0;
}
.flight-booking .booking-form .form-wizard-header .nav .nav-item .nav-link.active .num {
  background-color: #4D73FC;
  color: #F8F8FF;
  border: 0;
}
.flight-booking .booking-form .form-wizard-header .nav .nav-item .nav-link i {
  display: none;
  height: 24px;
  width: 24px;
  justify-content: center;
  align-content: center;
  border-radius: 24px;
  font-size: 16px;
  font-weight: 500;
  line-height: 150%;
  background-color: #4D73FC;
  color: #F8F8FF;
}
.flight-booking .booking-form .form-wizard-header .nav .nav-item .nav-link:is(.done) i {
  display: grid;
}
.flight-booking .booking-form .form-wizard-header .nav .nav-item .nav-link:is(.done) .num {
  display: none;
}
.flight-booking .booking-form .detail-form {
  background: #F8F8FF;
  border-radius: 15px;
  padding: clamp(25px, 2.19vw, 56px) clamp(16px, 1.25vw, 32px);
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 94px 48px 42px 0px rgba(0, 0, 0, 0.01);
}
.flight-booking .booking-form .detail-form .gender-select select {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  outline: 0;
  box-shadow: none;
  border: 0 !important;
  background: rgba(77, 115, 252, 0);
  background-image: none;
  flex: 1;
  padding: 16px 24px;
  color: #212627;
  cursor: pointer;
  font-size: 16px;
  font-family: "Inter", sans-serif;
}
.flight-booking .booking-form .detail-form .gender-select select::-ms-expand {
  display: none;
}
.flight-booking .booking-form .detail-form .gender-select .select-1 {
  position: relative;
  display: flex;
  line-height: 150%;
  background: rgba(77, 115, 252, 0.1);
  overflow: hidden;
  border-radius: 0.25em;
}
.flight-booking .booking-form .detail-form .gender-select .select-1::before {
  content: "\f2e7";
  color: #4D73FC;
  position: absolute;
  top: 0;
  left: 0;
  font-size: 20px;
  padding: 16px 12px;
  font-family: "Font Awesome 5 Pro";
  cursor: pointer;
  pointer-events: none;
  transition: 0.25s all ease;
}
.flight-booking .booking-form .detail-form .gender-select .select-1::after {
  content: "\f078";
  position: absolute;
  top: 0;
  right: 0;
  padding: 16px 24px;
  font-family: "Font Awesome 5 Pro";
  cursor: pointer;
  pointer-events: none;
  transition: 0.25s all ease;
}
.flight-booking .booking-form .detail-form .gender-select .select-1:hover::after {
  color: #4D73FC;
}
.flight-booking .booking-form .detail-form select {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  outline: 0;
  box-shadow: none;
  border: 0 !important;
  background: rgba(77, 115, 252, 0.1);
  background-image: none;
  flex: 1;
  padding: 16px 24px 16px 48px;
  color: #212627;
  cursor: pointer;
  font-size: 16px;
  font-family: "Inter", sans-serif;
}
.flight-booking .booking-form .detail-form select::-ms-expand {
  display: none;
}
.flight-booking .booking-form .detail-form .select-1 {
  position: relative;
  display: flex;
  line-height: 150%;
  background: rgba(77, 115, 252, 0.1);
  overflow: hidden;
  border-radius: 10px;
  border: 1px solid #0B0C0D;
}
.flight-booking .booking-form .detail-form .select-1::before {
  content: "\f2e7";
  color: #4D73FC;
  position: absolute;
  top: 0;
  left: 0;
  font-size: 20px;
  padding: 16px 12px;
  font-family: "Font Awesome 5 Pro";
  cursor: pointer;
  pointer-events: none;
  transition: 0.25s all ease;
}
.flight-booking .booking-form .detail-form .select-1::after {
  content: "\f078";
  position: absolute;
  top: 0;
  right: 0;
  padding: 16px 24px;
  font-family: "Font Awesome 5 Pro";
  cursor: pointer;
  pointer-events: none;
  transition: 0.25s all ease;
}
.flight-booking .booking-form .detail-form .select-1:hover::after {
  color: #4D73FC;
}
.flight-booking .booking-form .detail-form select {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  outline: 0;
  box-shadow: none;
  border: 0 !important;
  background: rgba(77, 115, 252, 0.1);
  background-image: none;
  flex: 1;
  padding: 16px 24px 16px 48px;
  color: #212627;
  cursor: pointer;
  font-size: 1em;
  font-family: "Inter", sans-serif;
}
.flight-booking .booking-form .detail-form select::-ms-expand {
  display: none;
}
.flight-booking .booking-form .detail-form .select-2 {
  position: relative;
  display: flex;
  line-height: 150%;
  background: rgba(77, 115, 252, 0.1);
  overflow: hidden;
  border-radius: 10px;
  border: 1px solid #0B0C0D;
}
.flight-booking .booking-form .detail-form .select-2::before {
  content: "\f193";
  color: #4D73FC;
  position: absolute;
  top: 0;
  left: 0;
  font-size: 20px;
  padding: 16px 12px;
  font-family: "Font Awesome 5 Pro";
  cursor: pointer;
  pointer-events: none;
  transition: 0.25s all ease;
}
.flight-booking .booking-form .detail-form .select-2::after {
  content: "\f078";
  position: absolute;
  top: 0;
  right: 0;
  padding: 16px 24px;
  font-family: "Font Awesome 5 Pro";
  cursor: pointer;
  pointer-events: none;
  transition: 0.25s all ease;
}
.flight-booking .booking-form .detail-form .select-2:hover::after {
  color: #4D73FC;
}
.flight-booking .card-select {
  position: relative;
}
.flight-booking .card-select img {
  position: absolute;
  top: 16px;
  left: 16px;
}
.flight-booking .flight-booking-detail {
  border-radius: 15px;
}
.flight-booking .flight-booking-detail .flight-title {
  padding: 16px 32px;
  background: rgba(77, 115, 252, 0.2);
  border-radius: 15px 15px 0 0;
}
.flight-booking .flight-booking-detail .box {
  border-radius: 0 0 15px 15px;
}
.flight-booking .flight-booking-detail .content-block {
  border-radius: 0 0 15px 15px;
}
.flight-booking .flight-booking-detail .flight-detail {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 32px;
}
.flight-booking .flight-booking-detail .flight-detail .flight-departure {
  display: inline-block;
}
.flight-booking .flight-booking-detail .vr-line {
  background: #7A7F85;
  height: 62px;
  width: 1px;
}
.flight-booking .icon-block {
  position: relative;
}
.flight-booking .icon-block i {
  position: absolute;
  top: 16px;
  right: 16px;
  font-size: 24px;
  color: #7A7F85;
}

.wizard-fieldset {
  position: relative;
  display: none;
}
.wizard-fieldset.show {
  display: block !important;
}

.wizard-form-error {
  display: none;
  background-color: red;
  left: 0;
  right: 0;
  bottom: 0;
  height: 2px;
  width: 90%;
  margin: 0 auto;
  border-radius: 2px;
}

.hotel-listing .hotel-block {
  padding: 16px;
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 94px 48px 42px 0px rgba(0, 0, 0, 0.01);
  border-radius: 30px;
}
.hotel-listing .hotel-block .image-box {
  overflow: hidden;
  border-radius: 20px;
  height: 100%;
  object-fit: cover;
}
.hotel-listing .hotel-block .image-box a {
  height: 100%;
  object-fit: cover;
}
.hotel-listing .hotel-block .image-box a img {
  height: 100%;
  object-fit: cover;
  border-radius: 20px;
  -webkit-transition: all 0.5s linear 0s;
  transition: all 0.5s linear 0s;
}
.hotel-listing .hotel-block .image-box.tour-package {
  position: relative;
}
.hotel-listing .hotel-block .image-box.tour-package .tour-days {
  position: absolute;
  top: 24px;
  left: 0;
  background: #F8F8FF;
  border-radius: 0 10px 10px 0;
  padding: 12px 24px;
}
.hotel-listing .hotel-block .apartment-detail {
  background: rgba(77, 115, 252, 0.1);
  border-radius: 10px;
  padding: 16px;
}
.hotel-listing .hotel-block .rating-number {
  padding: 12px;
  border-radius: 5px;
  background: #4D73FC;
  color: #F8F8FF;
}
.hotel-listing .hotel-block:hover .image-box img {
  -webkit-transform: scale(1.07);
  transform: scale(1.07);
  -webkit-transition: all 0.5s linear 0s;
  transition: all 0.5s linear 0s;
}
.hotel-listing .hotel-block .hotel-detail {
  padding-top: 32px;
}
.hotel-listing .hotel-block .pricing {
  padding-top: 24px;
}
@media (max-width: 692px) {
  .hotel-listing .hotel-block .pricing {
    padding-top: 48px;
  }
}
@media (max-width: 576px) {
  .hotel-listing .hotel-block .pricing {
    padding-top: 8px;
  }
}
.hotel-listing .hotel-block .pricing .review {
  display: flex;
  align-items: center;
  justify-content: end;
  gap: 16px;
}
@media (max-width: 576px) {
  .hotel-listing .hotel-block .pricing .review {
    justify-content: center;
    gap: 8px;
  }
}
.hotel-listing iframe {
  width: 100%;
  height: 240px;
  border-radius: 10px;
}

.hotel-detail-sec iframe {
  width: 100%;
  height: 337px;
  border-radius: 10px;
}
.hotel-detail-sec .rating-star i {
  font-size: 24px;
  color: #FC9C4D;
}
.hotel-detail-sec .facilities {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
}
@media (max-width: 767px) {
  .hotel-detail-sec .facilities {
    gap: 22px;
  }
}
@media (max-width: 575px) {
  .hotel-detail-sec .facilities {
    gap: 16px;
  }
}
@media (max-width: 767px) {
  .hotel-detail-sec .facilities img {
    width: 28px;
  }
}
@media (max-width: 575px) {
  .hotel-detail-sec .facilities img {
    width: 20px;
  }
}

.hotel-availability .availability-info {
  display: flex;
  padding: 20px 24px;
  align-items: center;
  gap: 32px;
  border-radius: 10px;
  background: rgba(77, 115, 252, 0.1);
}
@media (max-width: 1099px) {
  .hotel-availability .availability-info {
    gap: 16px;
  }
}
@media (max-width: 992px) {
  .hotel-availability .availability-info {
    display: grid;
    gap: 0;
  }
}
.hotel-availability .availability-info.v-2 {
  padding: 8px 24px;
}
.hotel-availability .availability-info .input-date-picker,
.hotel-availability .availability-info .custom-sel-input-block {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  position: relative;
}
@media (max-width: 1199px) {
  .hotel-availability .availability-info .input-date-picker,
  .hotel-availability .availability-info .custom-sel-input-block {
    margin-bottom: 16px;
  }
}
.hotel-availability .availability-info .input-date-picker.v-2::after,
.hotel-availability .availability-info .custom-sel-input-block.v-2::after {
  content: "";
  position: absolute;
  top: -12px;
  right: 0;
  height: 56px;
  width: 1px;
  background: #9EA2A8;
}
@media (max-width: 576px) {
  .hotel-availability .availability-info .input-date-picker.v-2::after,
  .hotel-availability .availability-info .custom-sel-input-block.v-2::after {
    display: none;
  }
}
.hotel-availability .availability-info .input-date-picker.location-select,
.hotel-availability .availability-info .custom-sel-input-block.location-select {
  width: 100%;
}
.hotel-availability .availability-info .input-date-picker.location-select i,
.hotel-availability .availability-info .custom-sel-input-block.location-select i {
  position: absolute;
  bottom: 0;
  color: #4D73FC;
  font-size: 24px;
}
.hotel-availability .availability-info .input-date-picker .sel-input,
.hotel-availability .availability-info .custom-sel-input-block .sel-input {
  width: 252px;
  color: #16191A;
  font-family: "Inter", sans-serif;
  font-size: 21px;
  font-weight: 500;
  line-height: 130%;
  border: none;
  background: transparent;
  padding: 4px 0 0 0;
  box-shadow: none;
}
@media (max-width: 1799px) {
  .hotel-availability .availability-info .input-date-picker .sel-input,
  .hotel-availability .availability-info .custom-sel-input-block .sel-input {
    width: 100%;
  }
}
.hotel-availability .availability-info .input-date-picker .sel-input::placeholder,
.hotel-availability .availability-info .custom-sel-input-block .sel-input::placeholder {
  color: #212627;
}
.hotel-availability .availability-info .input-date-picker .sel-input:focus,
.hotel-availability .availability-info .custom-sel-input-block .sel-input:focus {
  outline: 0;
}
.hotel-availability .availability-info .input-date-picker .sel-input.location-input,
.hotel-availability .availability-info .custom-sel-input-block .sel-input.location-input {
  width: 100%;
  padding: 0 0 0 32px;
}
@media (max-width: 1199px) {
  .hotel-availability .availability-info .input-date-picker .sel-input.calendar,
  .hotel-availability .availability-info .custom-sel-input-block .sel-input.calendar {
    width: 50%;
  }
}
@media (max-width: 399px) {
  .hotel-availability .availability-info .input-date-picker .sel-input.calendar,
  .hotel-availability .availability-info .custom-sel-input-block .sel-input.calendar {
    width: 70%;
  }
}
.hotel-availability .availability-info .input-date-picker .slector-wrapper,
.hotel-availability .availability-info .custom-sel-input-block .slector-wrapper {
  display: none;
  position: absolute;
  left: 0;
  top: 60px;
  z-index: 888;
  background: #ECECF2;
  padding-bottom: 16px;
  overflow: auto;
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 94px 48px 42px 0px rgba(0, 0, 0, 0.01);
  border-radius: 8px;
  height: 300px;
  /* Track */
  /* Handle */
}
.hotel-availability .availability-info .input-date-picker .slector-wrapper::-webkit-scrollbar,
.hotel-availability .availability-info .custom-sel-input-block .slector-wrapper::-webkit-scrollbar {
  width: 6px;
}
.hotel-availability .availability-info .input-date-picker .slector-wrapper::-webkit-scrollbar-track,
.hotel-availability .availability-info .custom-sel-input-block .slector-wrapper::-webkit-scrollbar-track {
  background: transparent;
  padding: 0 2px;
  width: 8px;
}
.hotel-availability .availability-info .input-date-picker .slector-wrapper::-webkit-scrollbar-thumb,
.hotel-availability .availability-info .custom-sel-input-block .slector-wrapper::-webkit-scrollbar-thumb {
  width: 8px;
  background: #7A7F85;
  border-radius: 6px;
}
@media screen and (min-width: 768px) {
  .hotel-availability .availability-info .input-date-picker .slector-wrapper,
  .hotel-availability .availability-info .custom-sel-input-block .slector-wrapper {
    max-width: 265px !important;
    min-width: 265px !important;
    width: auto;
  }
}
.hotel-availability .availability-info .input-date-picker .slector-wrapper li.top-set,
.hotel-availability .availability-info .custom-sel-input-block .slector-wrapper li.top-set {
  position: sticky;
  left: 0;
  top: 0;
  width: 100%;
}
.hotel-availability .availability-info .input-date-picker .slector-wrapper li span,
.hotel-availability .availability-info .custom-sel-input-block .slector-wrapper li span {
  border-radius: 8px 8px 0 0;
  display: block;
  padding: 10px 16px;
  background: #F8F8FF;
}
.hotel-availability .availability-info .input-date-picker .slector-wrapper li .sel-option,
.hotel-availability .availability-info .custom-sel-input-block .slector-wrapper li .sel-option {
  width: 100%;
  background: transparent;
  border: none;
  padding: 6px 10px;
  display: flex;
  align-items: center;
  gap: 6px;
  color: #16191A;
  cursor: pointer;
  transition: all 0.5s ease-in-out;
}
.hotel-availability .availability-info .input-date-picker .slector-wrapper li .sel-option:hover,
.hotel-availability .availability-info .custom-sel-input-block .slector-wrapper li .sel-option:hover {
  background: #7A7F85;
}
.hotel-availability .availability-info .input-date-picker i,
.hotel-availability .availability-info .custom-sel-input-block i {
  font-size: 21px;
  color: #4D73FC;
}
.hotel-availability .availability-info .seat-booking {
  font-size: 21px;
  font-weight: 500;
}
.hotel-availability .availability-info .passenger-area {
  width: 378px;
  position: absolute;
  top: 65px;
  right: -50%;
  display: none;
  z-index: 999;
}
@media (max-width: 1799px) {
  .hotel-availability .availability-info .passenger-area {
    top: 89px;
  }
}
@media (max-width: 1299px) {
  .hotel-availability .availability-info .passenger-area {
    top: 110px;
  }
}
@media (max-width: 1199px) {
  .hotel-availability .availability-info .passenger-area {
    width: 362px;
    top: 60px;
    left: unset;
    right: 0;
  }
}
@media (max-width: 576px) {
  .hotel-availability .availability-info .passenger-area {
    right: -36px;
  }
}
@media (max-width: 420px) {
  .hotel-availability .availability-info .passenger-area {
    width: 294px;
  }
}
.hotel-availability .availability-info .vertical-line {
  width: 1px;
  height: 56px;
  background: #9EA2A8;
  margin: 0 16px;
}
@media (max-width: 992px) {
  .hotel-availability .availability-info .vertical-line {
    height: 36px;
  }
}

@media (max-width: 1299px) {
  .available-rooms-table {
    display: none;
  }
}
.available-rooms-table table {
  background: #F8F8FF;
  border-radius: 20px;
}
.available-rooms-table table thead tr {
  background: rgba(77, 115, 252, 0.1);
  border-radius: 10px 10px 0 0;
}
.available-rooms-table table thead tr th {
  padding: 22.5px 24px;
  font-size: 21px;
  font-weight: 500;
  color: #16191A;
}
.available-rooms-table table tbody:last-child {
  border-radius: 20px;
}
.available-rooms-table table tbody tr td {
  padding: 24px;
  font-size: 21px;
  font-weight: 500;
  line-height: 130%;
}
.available-rooms-table table tbody tr td .blue-text {
  padding: 8px;
  border-radius: 5px;
  background: rgba(77, 115, 252, 0.1);
}

.available-rooms-mobile-view {
  display: none;
}
.available-rooms-mobile-view .room-block {
  background: #F8F8FF;
  padding: 24px;
  border-radius: 20px;
}
@media (max-width: 1299px) {
  .available-rooms-mobile-view {
    display: block;
  }
}

.hotel-detail-sidebar .detail-title {
  border-radius: 10px 10px 0 0;
  background: rgba(77, 115, 252, 0.2);
  padding: 16px 32px;
}
.hotel-detail-sidebar .hotel-content {
  border-radius: 10px;
  background: #F8F8FF;
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 94px 48px 42px 0px rgba(0, 0, 0, 0.01);
  padding: 32px;
}

.hotel-booking-time {
  display: flex;
  padding: 20px 24px;
  align-items: center;
  gap: 32px;
  border-radius: 10px;
  background: rgba(77, 115, 252, 0.1);
  border: 1px solid #212627;
}
@media (max-width: 1099px) {
  .hotel-booking-time {
    gap: 16px;
  }
}
@media (max-width: 992px) {
  .hotel-booking-time {
    display: grid;
    gap: 0;
  }
}
.hotel-booking-time .input-date-picker,
.hotel-booking-time .custom-sel-input-block {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  position: relative;
}
@media (max-width: 1199px) {
  .hotel-booking-time .input-date-picker,
  .hotel-booking-time .custom-sel-input-block {
    margin-bottom: 16px;
  }
}
.hotel-booking-time .input-date-picker.location-select,
.hotel-booking-time .custom-sel-input-block.location-select {
  width: 100%;
}
.hotel-booking-time .input-date-picker.location-select i,
.hotel-booking-time .custom-sel-input-block.location-select i {
  position: absolute;
  bottom: 0;
  color: #4D73FC;
  font-size: 24px;
}
.hotel-booking-time .input-date-picker .sel-input,
.hotel-booking-time .custom-sel-input-block .sel-input {
  width: 252px;
  color: #16191A;
  font-family: "Inter", sans-serif;
  font-size: 21px;
  font-weight: 500;
  line-height: 130%;
  border: none;
  background: transparent;
  padding: 4px 0 0 0;
  box-shadow: none;
}
@media (max-width: 1799px) {
  .hotel-booking-time .input-date-picker .sel-input,
  .hotel-booking-time .custom-sel-input-block .sel-input {
    width: 100%;
  }
}
.hotel-booking-time .input-date-picker .sel-input::placeholder,
.hotel-booking-time .custom-sel-input-block .sel-input::placeholder {
  color: #212627;
}
.hotel-booking-time .input-date-picker .sel-input:focus,
.hotel-booking-time .custom-sel-input-block .sel-input:focus {
  outline: 0;
}
.hotel-booking-time .input-date-picker .sel-input.location-input,
.hotel-booking-time .custom-sel-input-block .sel-input.location-input {
  width: 100%;
  padding: 0 0 0 32px;
}
.hotel-booking-time .input-date-picker .slector-wrapper,
.hotel-booking-time .custom-sel-input-block .slector-wrapper {
  display: none;
  position: absolute;
  left: 0;
  top: 60px;
  z-index: 888;
  background: #ECECF2;
  padding-bottom: 16px;
  overflow: auto;
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 94px 48px 42px 0px rgba(0, 0, 0, 0.01);
  border-radius: 8px;
  height: 300px;
  /* Track */
  /* Handle */
}
.hotel-booking-time .input-date-picker .slector-wrapper::-webkit-scrollbar,
.hotel-booking-time .custom-sel-input-block .slector-wrapper::-webkit-scrollbar {
  width: 6px;
}
.hotel-booking-time .input-date-picker .slector-wrapper::-webkit-scrollbar-track,
.hotel-booking-time .custom-sel-input-block .slector-wrapper::-webkit-scrollbar-track {
  background: transparent;
  padding: 0 2px;
  width: 8px;
}
.hotel-booking-time .input-date-picker .slector-wrapper::-webkit-scrollbar-thumb,
.hotel-booking-time .custom-sel-input-block .slector-wrapper::-webkit-scrollbar-thumb {
  width: 8px;
  background: #7A7F85;
  border-radius: 6px;
}
@media screen and (min-width: 768px) {
  .hotel-booking-time .input-date-picker .slector-wrapper,
  .hotel-booking-time .custom-sel-input-block .slector-wrapper {
    max-width: 265px !important;
    min-width: 265px !important;
    width: auto;
  }
}
.hotel-booking-time .input-date-picker .slector-wrapper li.top-set,
.hotel-booking-time .custom-sel-input-block .slector-wrapper li.top-set {
  position: sticky;
  left: 0;
  top: 0;
  width: 100%;
}
.hotel-booking-time .input-date-picker .slector-wrapper li span,
.hotel-booking-time .custom-sel-input-block .slector-wrapper li span {
  border-radius: 8px 8px 0 0;
  display: block;
  padding: 10px 16px;
  background: #F8F8FF;
}
.hotel-booking-time .input-date-picker .slector-wrapper li .sel-option,
.hotel-booking-time .custom-sel-input-block .slector-wrapper li .sel-option {
  width: 100%;
  background: transparent;
  border: none;
  padding: 6px 10px;
  display: flex;
  align-items: center;
  gap: 6px;
  color: #16191A;
  cursor: pointer;
  transition: all 0.5s ease-in-out;
}
.hotel-booking-time .input-date-picker .slector-wrapper li .sel-option:hover,
.hotel-booking-time .custom-sel-input-block .slector-wrapper li .sel-option:hover {
  background: #7A7F85;
}
.hotel-booking-time .input-date-picker i,
.hotel-booking-time .custom-sel-input-block i {
  font-size: 21px;
  color: #4D73FC;
}

.detail-form textarea {
  border: 1px solid #212627;
  border-radius: 10px;
}

.brand-logo {
  display: inline-flex;
  align-items: center;
  gap: 16px;
  border-radius: 5px;
  border: 1px solid #212627;
  background: rgba(77, 115, 252, 0.1);
  padding: 4px;
}
.brand-logo img {
  width: clamp(64px, 6.458vw, 200px);
}
.brand-logo .rating {
  padding: 8px clamp(12px, 1.25vw, 36px);
  border-radius: 5px;
  background: #4D73FC;
}

.pricing-area .pre-reg {
  padding: 8px 16px;
  border-radius: 5px;
  background: rgba(77, 115, 252, 0.1);
}

.car-booking-detail {
  border-radius: 15px;
}
.car-booking-detail .car-title {
  padding: 16px 32px;
  background: rgba(77, 115, 252, 0.2);
  border-radius: 15px 15px 0 0;
}
.car-booking-detail .content-block {
  border-radius: 0 0 15px 15px;
}
.car-booking-detail .flight-detail {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 32px;
}
.car-booking-detail .flight-detail .flight-departure {
  display: inline-block;
}
.car-booking-detail .vr-line {
  background: #7A7F85;
  height: 62px;
  width: 1px;
}

.contact-us .contact-form {
  background: #F8F8FF;
  padding: 24px;
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 94px 48px 42px 0px rgba(0, 0, 0, 0.01);
  border-radius: 20px;
  margin-bottom: 80px;
}
@media (max-width: 992px) {
  .contact-us .contact-form {
    margin-bottom: 60px;
  }
}
@media (max-width: 767px) {
  .contact-us .contact-form {
    margin-bottom: 40px;
  }
}
@media (max-width: 576x) {
  .contact-us .contact-form {
    margin-bottom: 24px;
  }
}
.contact-us .contact-form button {
  padding: 12px 8px;
}
.contact-us iframe {
  width: 100%;
  height: 568px;
  border-radius: 15px;
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 94px 48px 42px 0px rgba(0, 0, 0, 0.01);
}
@media (max-width: 992px) {
  .contact-us iframe {
    height: 500px;
  }
}
@media (max-width: 767px) {
  .contact-us iframe {
    height: 440px;
  }
}
@media (max-width: 576x) {
  .contact-us iframe {
    height: 380px;
  }
}

@media (max-width: 992px) {
  .signup {
    background: url("../media/images/login.jpg");
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    min-height: 100vh;
    display: grid;
    align-items: center;
  }
}
@media (max-width: 492px) {
  .signup.v2 {
    display: block !important;
  }
}
@media (max-width: 492px) {
  .signup.v2 .form-block {
    margin: 48px 0;
  }
}
.signup .form-block {
  border-radius: 20px;
  background: #F8F8FF;
}
@media (max-width: 992px) {
  .signup .form-block {
    padding: 24px;
    margin: 0 16px;
  }
}
.signup .form-block .google {
  background: #FC4D4D;
  border: 0;
  border-radius: 10px;
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 94px 48px 42px 0px rgba(0, 0, 0, 0.01);
}
.signup .form-block .google a:hover {
  color: #F8F8FF;
}
.signup .form-block .facebook {
  background: #4D73FC;
  border: 0;
  border-radius: 10px;
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.04), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 94px 48px 42px 0px rgba(0, 0, 0, 0.01);
}
.signup .form-block .facebook a:hover {
  color: #F8F8FF;
}
.signup .form-block .link-btn {
  padding: 16px 32px;
  display: flex;
  gap: 12px;
  text-align: center;
  align-items: center;
  justify-content: center;
}
.signup .form-block .or {
  position: relative;
  text-align: center;
}
.signup .form-block .or::after, .signup .form-block .or::before {
  content: "";
  height: 1px;
  width: 46%;
  position: absolute;
  top: 14px;
  background: #7A7F85;
}
@media (max-width: 575px) {
  .signup .form-block .or::after, .signup .form-block .or::before {
    top: 12px;
  }
}
.signup .form-block .or::after {
  right: 0;
}
.signup .form-block .or::before {
  left: 0;
}
.signup .form-block .contact-form .form-control {
  border-radius: 5px;
  background: rgba(77, 115, 252, 0.1);
}
@media (max-width: 992px) {
  .signup .img-block {
    display: none;
  }
}
.signup .img-block img {
  min-height: 100vh;
  object-fit: cover;
  width: 100%;
}
@media (max-width: 1199px) {
  .signup .sign-up-image {
    width: 100%;
  }
}
@media (max-width: 992px) {
  .signup .sign-up-image {
    margin-top: 24px;
  }
}
.signup .architex-tilt {
  height: 100%;
  object-fit: cover;
}
.signup .architex-tilt img {
  height: 100%;
  object-fit: cover;
}
.signup .country-selector select {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  outline: 0;
  box-shadow: none;
  border: 0 !important;
  background: rgba(77, 115, 252, 0);
  background-image: none;
  flex: 1;
  padding: 11.5px 16px;
  color: #212627;
  cursor: pointer;
  font-size: 16px;
  font-family: "Inter", sans-serif;
}
.signup .country-selector select::-ms-expand {
  display: none;
}
.signup .country-selector .select {
  position: relative;
  display: flex;
  line-height: 150%;
  background: rgba(77, 115, 252, 0.1);
  overflow: hidden;
  border-radius: 0.25em;
}
.signup .country-selector .select::after {
  content: "\f078";
  position: absolute;
  top: 0;
  right: 0;
  padding: 16px 24px;
  font-family: "Font Awesome 5 Pro";
  cursor: pointer;
  pointer-events: none;
  transition: 0.25s all ease;
}
.signup .country-selector .select:hover::after {
  color: #4D73FC;
}

.hotel-schedule-block .hotel-detail .rating-review {
  display: flex;
  align-items: center;
  gap: clamp(12px, 1.25vw, 32px);
}
.hotel-schedule-block .hotel-detail .rating-review .number {
  padding: clamp(4px, 0.417vw, 16px) clamp(8px, 0.625vw, 32px);
  border-radius: 4px;
  background: #4D73FC;
  color: #F8F8FF;
}
.hotel-schedule-block .hotel-detail .room-detail-box {
  padding: clamp(10px, 0.833vw, 32px);
  border-radius: clamp(6px, 0.521vw, 24px);
  background: rgba(77, 115, 252, 0.1);
}

.tour-guide-wrapper {
  padding: clamp(24px, 2.083vw, 64px) clamp(12px, 0.833vw, 40px);
  border-radius: clamp(12px, 1.02vw, 32px);
  box-shadow: 94px 48px 42px 0px rgba(0, 0, 0, 0.01), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 0px 0px 0px 0px rgba(0, 0, 0, 0.04);
}

.feature-block {
  display: flex;
  align-items: flex-start;
  gap: clamp(8px, 0.625vw, 32px);
  padding: clamp(10px, 0.833vw, 32px);
  border-radius: clamp(6px, 0.521vw, 32px);
  background: #F8F8FF;
  box-shadow: 94px 48px 42px 0px rgba(0, 0, 0, 0.01), 53px 27px 35px 0px rgba(0, 0, 0, 0.02), 23px 12px 26px 0px rgba(0, 0, 0, 0.03), 6px 3px 14px 0px rgba(0, 0, 0, 0.04), 0px 0px 0px 0px rgba(0, 0, 0, 0.04);
}

/*# sourceMappingURL=app.css.map */
