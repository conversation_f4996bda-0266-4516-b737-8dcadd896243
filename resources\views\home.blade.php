@extends('layouts.frontend')

@push('styles')
    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <style>
        /* Sub Navigation Tabs */
        .sub-nav-tabs .nav-pills .nav-link {
            background-color: #f8f9fa;
            color: #6c757d;
            border-radius: 25px;
            padding: 12px 24px;
            margin: 0 5px;
            border: none;
            font-weight: 500;
        }

        .sub-nav-tabs .nav-pills .nav-link.active {
            background-color: #ff6b35;
            color: white;
        }

        .sub-nav-tabs .nav-pills .nav-link:hover {
            background-color: #e9ecef;
            color: #495057;
        }

        .sub-nav-tabs .nav-pills .nav-link.active:hover {
            background-color: #ff6b35;
            color: white;
        }

        /* Trip Type Selection */
        .trip-type-selection {
            padding: 20px 0;
        }

        .trip-type-selection .form-check-input:checked {
            background-color: #007bff;
            border-color: #007bff;
        }

        .trip-type-selection .form-check-label {
            font-weight: 500;
            color: #495057;
            margin-left: 8px;
        }

        /* Form Controls */
        .form-control-lg {
            padding: 12px 16px;
            font-size: 16px;
            border-radius: 8px;
            border: 2px solid #e9ecef;
        }

        .form-control-lg:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        /* Swap Button */
        .swap-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
        }

        .swap-btn:hover {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }

        .swap-btn.rotate {
            transform: rotate(180deg);
            transition: transform 0.3s ease;
        }

        /* Airport Suggestions */
        .airport-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 8px 8px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .airport-suggestion {
            padding: 12px 16px;
            cursor: pointer;
            border-bottom: 1px solid #f1f3f4;
        }

        .airport-suggestion:hover {
            background-color: #f8f9fa;
        }

        .airport-suggestion:last-child {
            border-bottom: none;
        }

        .form-group {
            position: relative;
        }

        /* Stopover Section */
        .stopover-section {
            background-color: #f8f9fa;
            padding: 24px;
            border-radius: 12px;
            border: 1px solid #e9ecef;
        }

        .stopover-section .form-check-input:checked {
            background-color: #007bff;
            border-color: #007bff;
        }

        .stopover-section .form-check-label {
            font-weight: 500;
            color: #495057;
        }

        /* Days Counter */
        .input-group .btn {
            border-color: #ced4da;
        }

        .input-group .form-control {
            border-left: none;
            border-right: none;
        }

        /* Show Flight Button */
        .btn-primary.btn-lg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 24px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary.btn-lg:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* Passengers Dropdown */
        .dropdown-menu {
            border-radius: 12px;
            border: 1px solid #e9ecef;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        /* Flight Results */
        .flight-result-card {
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 16px;
            background: white;
            transition: all 0.3s ease;
        }

        .flight-result-card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .price-highlight {
            font-size: 1.75rem;
            font-weight: 700;
            color: #007bff;
        }

        /* Flight Deals Section */
        .flight-deal-block {
            border-radius: 12px;
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .flight-deal-block:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
        }

        .flight-deal-block .image-box img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
        }

        .flight-deal-block .cus-btn.small-pad {
            padding: 8px 16px;
            font-size: 14px;
            border-radius: 6px;
        }

        .flight-deal-block h5 a {
            color: inherit;
            transition: color 0.3s ease;
        }

        .flight-deal-block h5 a:hover {
            color: #007bff;
        }

        /* Airport Autocomplete Styles */
        .airport-autocomplete {
            position: relative;
        }

        .airport-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e9ecef;
            border-top: none;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .airport-suggestion {
            padding: 12px 16px;
            cursor: pointer;
            border-bottom: 1px solid #f8f9fa;
            transition: background-color 0.2s ease;
        }

        .airport-suggestion:hover,
        .airport-suggestion.active {
            background-color: #f8f9fa;
        }

        .airport-suggestion:last-child {
            border-bottom: none;
        }

        .airport-suggestion .airport-code {
            font-weight: 600;
            color: #007bff;
            font-size: 14px;
        }

        .airport-suggestion .airport-name {
            color: #333;
            font-size: 14px;
            margin-bottom: 2px;
        }

        .airport-suggestion .airport-location {
            color: #6c757d;
            font-size: 12px;
        }

        .airport-suggestion .highlight {
            background-color: #fff3cd;
            font-weight: 600;
        }

        .autocomplete-loading {
            padding: 12px 16px;
            text-align: center;
            color: #6c757d;
            font-size: 14px;
        }

        .autocomplete-no-results {
            padding: 12px 16px;
            text-align: center;
            color: #6c757d;
            font-size: 14px;
            font-style: italic;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sub-nav-tabs .nav-pills .nav-link {
                padding: 8px 16px;
                font-size: 14px;
                margin: 2px;
            }

            .stopover-section {
                padding: 16px;
            }

            .form-control-lg {
                padding: 10px 14px;
                font-size: 14px;
            }

            .flight-deal-block .image-box img {
                height: 150px;
            }

            .airport-suggestions {
                max-height: 200px;
            }

            .airport-suggestion {
                padding: 10px 12px;
            }
        }

        /* Flight Datatable Styles */
        .flight-row:hover {
            background-color: #f8f9fa;
            transition: all 0.3s ease;
        }

        .airline-logo .badge {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
            border-radius: 5px;
        }

        .table th {
            border-top: none;
            font-weight: 600;
            background-color: #16191A;
            color: #F8F8FF;
            position: sticky;
            top: 0;
            z-index: 10;
            font-family: "Inter", sans-serif;
        }

        .table-responsive {
            max-height: 600px;
            overflow-y: auto;
            border-radius: 10px;
            box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
        }

        .sort-option:hover {
            background-color: #f8f9fa;
            color: #4D73FC;
        }

        .badge {
            font-size: 0.75rem;
        }



        .btn-group {
            gap: 0.5rem;
        }

        .btn-group .cus-btn {
            margin-right: 0;
        }

        .input-group .form-control {
            border-color: #ECECF2;
            border-radius: 5px;
            font-family: "Inter", sans-serif;
        }

        .input-group .form-control:focus {
            border-color: #4D73FC;
            box-shadow: 0 0 0 0.2rem rgba(77, 115, 252, 0.25);
        }

        .input-group-text {
            background-color: #ECECF2;
            border-color: #ECECF2;
            color: #4D73FC;
        }

        .available-flights .card {
            border-radius: 10px;
            box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
            border: none;
        }

        .available-flights .card-header {
            background-color: #F8F8FF;
            border-bottom: 1px solid #ECECF2;
            border-radius: 10px 10px 0 0;
        }

        .table td {
            vertical-align: middle;
            font-family: "Inter", sans-serif;
        }

        .cus-btn.small-pad {
            padding: 8px 16px;
            font-size: 14px;
        }

        /* Professional Filter Controls Styles */
        .flight-filter-section {
            margin-bottom: 2rem;
        }

        .filter-container {
            background: linear-gradient(135deg, #F8F8FF 0%, #FFFFFF 100%);
            border-radius: 15px;
            box-shadow: 0px 8px 25px rgba(77, 115, 252, 0.08);
            border: 1px solid rgba(77, 115, 252, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .filter-container:hover {
            box-shadow: 0px 12px 35px rgba(77, 115, 252, 0.12);
            transform: translateY(-2px);
        }

        .filter-header {
            background: linear-gradient(135deg, #4D73FC 0%, #6B8AFF 100%);
            padding: 1.25rem 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .filter-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .filter-icon {
            color: #F8F8FF;
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .filter-heading {
            color: #F8F8FF;
            margin: 0;
            font-family: "Inter", sans-serif;
            font-weight: 600;
            font-size: 1.1rem;
            letter-spacing: 0.5px;
        }

        .filter-actions .btn-clear {
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #F8F8FF;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .filter-actions .btn-clear:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .filter-body {
            padding: 1.75rem 1.5rem;
        }

        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .filter-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-family: "Inter", sans-serif;
            font-weight: 600;
            font-size: 0.875rem;
            color: #16191A;
            margin-bottom: 0.25rem;
        }

        .label-icon {
            color: #4D73FC;
            font-size: 0.875rem;
            width: 14px;
            text-align: center;
        }

        .custom-select-wrapper {
            position: relative;
            display: block;
        }

        .custom-filter-select {
            width: 100%;
            padding: 0.875rem 2.5rem 0.875rem 1rem;
            border: 2px solid #ECECF2;
            border-radius: 10px;
            background-color: #FFFFFF;
            font-family: "Inter", sans-serif;
            font-size: 0.875rem;
            color: #16191A;
            appearance: none;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .custom-filter-select:focus {
            outline: none;
            border-color: #4D73FC;
            box-shadow: 0 0 0 3px rgba(77, 115, 252, 0.1);
            background-color: #F8F8FF;
        }

        .custom-filter-select:hover {
            border-color: #4D73FC;
            background-color: #F8F8FF;
        }

        .select-arrow {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #4D73FC;
            font-size: 0.75rem;
            pointer-events: none;
            transition: all 0.3s ease;
        }

        .custom-select-wrapper:hover .select-arrow {
            transform: translateY(-50%) rotate(180deg);
        }

        .filter-action {
            display: flex;
            align-items: end;
        }

        .action-buttons {
            width: 100%;
        }

        .filter-apply-btn {
            width: 100%;
            padding: 0.875rem 1.5rem;
            font-size: 0.875rem;
            font-weight: 600;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            box-shadow: 0px 4px 15px rgba(77, 115, 252, 0.2);
        }

        .filter-apply-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0px 8px 25px rgba(77, 115, 252, 0.3);
        }

        /* Professional Search Box Styles */
        .professional-search-wrapper {
            min-width: 280px;
        }

        .search-input-container {
            position: relative;
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, #FFFFFF 0%, #F8F8FF 100%);
            border: 2px solid #ECECF2;
            border-radius: 12px;
            padding: 0;
            transition: all 0.3s ease;
            box-shadow: 0px 2px 8px rgba(77, 115, 252, 0.08);
        }

        .search-input-container:hover {
            border-color: #4D73FC;
            box-shadow: 0px 4px 15px rgba(77, 115, 252, 0.15);
        }

        .search-input-container:focus-within {
            border-color: #4D73FC;
            box-shadow: 0px 4px 20px rgba(77, 115, 252, 0.2);
            background: #F8F8FF;
        }

        .search-icon {
            position: absolute;
            left: 1rem;
            color: #4D73FC;
            font-size: 0.875rem;
            z-index: 2;
        }

        .professional-search-input {
            width: 100%;
            border: none;
            background: transparent;
            padding: 0.875rem 2.5rem 0.875rem 2.75rem;
            font-family: "Inter", sans-serif;
            font-size: 0.875rem;
            color: #16191A;
            outline: none;
            border-radius: 12px;
        }

        .professional-search-input::placeholder {
            color: #9CA3AF;
            font-style: italic;
        }

        .search-clear-btn {
            position: absolute;
            right: 0.75rem;
            width: 20px;
            height: 20px;
            background: #4D73FC;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 2;
        }

        .search-clear-btn:hover {
            background: #3A5FE8;
            transform: scale(1.1);
        }

        .search-clear-btn i {
            color: white;
            font-size: 0.75rem;
        }

        /* Professional Sort Button Styles */
        .professional-sort-wrapper {
            position: relative;
        }

        .sort-dropdown-container {
            position: relative;
        }

        .professional-sort-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: linear-gradient(135deg, #4D73FC 0%, #6B8AFF 100%);
            border: none;
            border-radius: 10px;
            padding: 0.875rem 1.25rem;
            color: #F8F8FF;
            font-family: "Inter", sans-serif;
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0px 4px 15px rgba(77, 115, 252, 0.2);
            min-width: 140px;
        }

        .professional-sort-btn:hover {
            background: linear-gradient(135deg, #3A5FE8 0%, #5A7AFF 100%);
            transform: translateY(-2px);
            box-shadow: 0px 8px 25px rgba(77, 115, 252, 0.3);
            color: #F8F8FF;
        }

        .professional-sort-btn:focus {
            outline: none;
            box-shadow: 0px 4px 20px rgba(77, 115, 252, 0.4);
        }

        .sort-icon {
            font-size: 0.875rem;
            opacity: 0.9;
        }

        .sort-text {
            flex: 1;
            text-align: left;
        }

        .dropdown-arrow {
            font-size: 0.75rem;
            transition: transform 0.3s ease;
        }

        .professional-sort-btn[aria-expanded="true"] .dropdown-arrow {
            transform: rotate(180deg);
        }

        .professional-dropdown-menu {
            background: #FFFFFF;
            border: 1px solid #ECECF2;
            border-radius: 10px;
            box-shadow: 0px 8px 25px rgba(0, 0, 0, 0.1);
            padding: 0.5rem 0;
            margin-top: 0.5rem;
            min-width: 200px;
        }

        .professional-dropdown-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.25rem;
            color: #16191A;
            text-decoration: none;
            font-family: "Inter", sans-serif;
            font-size: 0.875rem;
            transition: all 0.3s ease;
            border: none;
            background: none;
        }

        .professional-dropdown-item:hover {
            background: linear-gradient(135deg, #F8F8FF 0%, #ECECF2 100%);
            color: #4D73FC;
            transform: translateX(5px);
        }

        .professional-dropdown-item i {
            color: #4D73FC;
            width: 16px;
        }

        /* Professional Date Picker Styles */
        .custom-datepicker-wrapper {
            position: relative;
            display: block;
        }

        .custom-datepicker-input {
            width: 100%;
            padding: 0.875rem 4rem 0.875rem 1rem;
            border: 2px solid #ECECF2;
            border-radius: 10px;
            background-color: #FFFFFF;
            font-family: "Inter", sans-serif;
            font-size: 0.875rem;
            color: #16191A;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .custom-datepicker-input:focus {
            outline: none;
            border-color: #4D73FC;
            box-shadow: 0 0 0 3px rgba(77, 115, 252, 0.1);
            background-color: #F8F8FF;
        }

        .custom-datepicker-input:hover {
            border-color: #4D73FC;
            background-color: #F8F8FF;
        }

        .custom-datepicker-input::placeholder {
            color: #9CA3AF;
            font-style: italic;
        }

        .datepicker-icon {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #4D73FC;
            font-size: 0.875rem;
            pointer-events: none;
            transition: all 0.3s ease;
        }

        .datepicker-clear {
            position: absolute;
            right: 2.75rem;
            top: 50%;
            transform: translateY(-50%);
            width: 18px;
            height: 18px;
            background: #4D73FC;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 2;
        }

        .datepicker-clear:hover {
            background: #3A5FE8;
            transform: translateY(-50%) scale(1.1);
        }

        .datepicker-clear i {
            color: white;
            font-size: 0.7rem;
        }

        /* Flatpickr Custom Styling */
        .flatpickr-calendar {
            background: #FFFFFF;
            border: 1px solid #ECECF2;
            border-radius: 15px;
            box-shadow: 0px 15px 35px rgba(0, 0, 0, 0.1);
            font-family: "Inter", sans-serif;
            overflow: hidden;
        }

        .flatpickr-calendar.open {
            z-index: 9999;
        }

        .flatpickr-months {
            background: linear-gradient(135deg, #4D73FC 0%, #6B8AFF 100%);
            color: #F8F8FF;
            padding: 1rem;
        }

        .flatpickr-month {
            color: #F8F8FF;
            fill: #F8F8FF;
        }

        .flatpickr-current-month .flatpickr-monthDropdown-months {
            background: transparent;
            color: #F8F8FF;
            border: none;
            font-weight: 600;
        }

        .flatpickr-current-month input.cur-year {
            background: transparent;
            color: #F8F8FF;
            border: none;
            font-weight: 600;
        }

        .flatpickr-prev-month,
        .flatpickr-next-month {
            color: #F8F8FF;
            fill: #F8F8FF;
        }

        .flatpickr-prev-month:hover,
        .flatpickr-next-month:hover {
            color: #FFFFFF;
            background: rgba(255, 255, 255, 0.1);
        }

        .flatpickr-weekdays {
            background: #F8F8FF;
            color: #4D73FC;
            font-weight: 600;
        }

        .flatpickr-weekday {
            color: #4D73FC;
            font-weight: 600;
            font-size: 0.8rem;
        }

        .flatpickr-days {
            padding: 0.5rem;
        }

        .flatpickr-day {
            color: #16191A;
            border-radius: 8px;
            margin: 1px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .flatpickr-day:hover {
            background: linear-gradient(135deg, #4D73FC 0%, #6B8AFF 100%);
            color: #F8F8FF;
            transform: scale(1.05);
        }

        .flatpickr-day.selected {
            background: linear-gradient(135deg, #4D73FC 0%, #6B8AFF 100%);
            color: #F8F8FF;
            border-color: #4D73FC;
            font-weight: 600;
        }

        .flatpickr-day.today {
            border: 2px solid #4D73FC;
            color: #4D73FC;
            font-weight: 600;
        }

        .flatpickr-day.today:hover {
            background: linear-gradient(135deg, #4D73FC 0%, #6B8AFF 100%);
            color: #F8F8FF;
        }

        .flatpickr-day.prevMonthDay,
        .flatpickr-day.nextMonthDay {
            color: #9CA3AF;
        }

        .flatpickr-day.disabled {
            color: #E5E7EB;
            cursor: not-allowed;
        }

        /* Enhanced Date Picker Features */
        .custom-datepicker-input.has-value {
            background: linear-gradient(135deg, #F8F8FF 0%, #FFFFFF 100%);
            border-color: #4D73FC;
            color: #16191A;
            font-weight: 500;
        }

        .professional-datepicker.calendar-open {
            animation: fadeInScale 0.3s ease-out;
        }

        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: scale(0.95) translateY(-10px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        /* Date Picker Mobile Enhancements */
        @media (max-width: 768px) {
            .flatpickr-calendar {
                width: 90vw !important;
                max-width: 320px;
                left: 50% !important;
                transform: translateX(-50%) !important;
            }

            .flatpickr-months {
                padding: 0.75rem;
            }

            .flatpickr-day {
                height: 36px;
                line-height: 36px;
                font-size: 0.875rem;
            }
        }

        /* Custom Input Wrapper Styles */
        .custom-input-wrapper {
            position: relative;
            display: block;
        }

        .custom-filter-input {
            width: 100%;
            padding: 0.875rem 2.5rem 0.875rem 1rem;
            border: 2px solid #ECECF2;
            border-radius: 10px;
            background-color: #FFFFFF;
            font-family: "Inter", sans-serif;
            font-size: 0.875rem;
            color: #16191A;
            transition: all 0.3s ease;
        }

        .custom-filter-input:focus {
            outline: none;
            border-color: #4D73FC;
            box-shadow: 0 0 0 3px rgba(77, 115, 252, 0.1);
            background-color: #F8F8FF;
        }

        .custom-filter-input:hover {
            border-color: #4D73FC;
            background-color: #F8F8FF;
        }

        .custom-filter-input::placeholder {
            color: #9CA3AF;
            font-style: italic;
        }

        .input-icon {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #4D73FC;
            font-size: 0.875rem;
            pointer-events: none;
        }

        /* Professional Meal Badge Styles */
        .meal-badge {
            font-family: "Inter", sans-serif;
            font-weight: 600;
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
            border-radius: 8px;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .meal-badge.bg-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
            border-color: #28a745;
            color: #ffffff;
        }

        .meal-badge.bg-danger {
            background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%) !important;
            border-color: #dc3545;
            color: #ffffff;
        }

        .meal-badge i {
            font-size: 0.7rem;
            opacity: 0.9;
        }

        .meal-badge:hover {
            transform: translateY(-1px);
            box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* Modern Flight Cards Styling */
        .flight-cards-container {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
            padding: 1rem 0;
        }

        .flight-card {
            background: #FFFFFF;
            border: 1px solid #ECECF2;
            border-radius: 15px;
            box-shadow: 0px 4px 15px rgba(77, 115, 252, 0.08);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .flight-card:hover {
            transform: translateY(-2px);
            box-shadow: 0px 8px 25px rgba(77, 115, 252, 0.15);
            border-color: #4D73FC;
        }

        .flight-card-header {
            background: linear-gradient(135deg, #F8F8FF 0%, #FFFFFF 100%);
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #ECECF2;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .flight-route {
            display: flex;
            align-items: center;
            font-family: "Inter", sans-serif;
            font-weight: 600;
            color: #16191A;
            font-size: 1rem;
        }

        .flight-date {
            background: linear-gradient(135deg, #4D73FC 0%, #6B8AFF 100%);
            color: #F8F8FF;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-family: "Inter", sans-serif;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .flight-card-body {
            padding: 1.5rem;
        }

        .flight-timeline {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.5rem;
        }

        .departure-info, .arrival-info {
            text-align: center;
            flex: 1;
        }

        .departure-info .time, .arrival-info .time {
            font-size: 1.75rem;
            font-weight: 700;
            color: #16191A;
            font-family: "Inter", sans-serif;
            margin-bottom: 0.25rem;
        }

        .departure-info .airport-code, .arrival-info .airport-code {
            font-size: 1rem;
            font-weight: 600;
            color: #4D73FC;
            font-family: "Inter", sans-serif;
            margin-bottom: 0.25rem;
        }

        .departure-info .city-name, .arrival-info .city-name {
            font-size: 0.875rem;
            color: #6B7280;
            font-family: "Inter", sans-serif;
        }

        .flight-path {
            flex: 2;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0 2rem;
        }

        .flight-line {
            display: flex;
            align-items: center;
            width: 100%;
            margin-bottom: 0.5rem;
        }

        .line-start, .line-end {
            height: 2px;
            background: linear-gradient(135deg, #4D73FC 0%, #6B8AFF 100%);
            flex: 1;
        }

        .flight-duration {
            background: linear-gradient(135deg, #4D73FC 0%, #6B8AFF 100%);
            color: #F8F8FF;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-family: "Inter", sans-serif;
            font-weight: 600;
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin: 0 1rem;
        }

        .flight-duration i {
            font-size: 0.875rem;
        }

        .flight-type {
            margin-bottom: 0.25rem;
        }

        .stop-info {
            font-size: 0.75rem;
            color: #6B7280;
        }

        .flight-details {
            border-top: 1px solid #ECECF2;
            padding-top: 1rem;
        }

        .flight-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .airline-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .airline-badge {
            font-family: "Inter", sans-serif;
            font-weight: 600;
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
            border-radius: 8px;
        }

        .flight-number {
            font-family: "Inter", sans-serif;
            font-weight: 600;
            color: #16191A;
            font-size: 0.875rem;
        }

        .flight-services {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .service-item {
            display: flex;
            align-items: center;
            font-family: "Inter", sans-serif;
            font-size: 0.875rem;
            color: #6B7280;
        }

        .flight-card-footer {
            background: linear-gradient(135deg, #F8F8FF 0%, #FFFFFF 100%);
            padding: 1rem 1.5rem;
            border-top: 1px solid #ECECF2;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .price-section {
            display: flex;
            flex-direction: column;
        }

        .price {
            font-size: 1.5rem;
            font-weight: 700;
            color: #16191A;
            font-family: "Inter", sans-serif;
            margin-bottom: 0.25rem;
        }

        .price-label {
            font-size: 0.75rem;
            color: #6B7280;
            font-family: "Inter", sans-serif;
        }

        .price-locked {
            display: flex;
            align-items: center;
            color: #6B7280;
            font-family: "Inter", sans-serif;
            font-size: 0.875rem;
        }

        .action-section {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .flight-book-btn {
            background: linear-gradient(135deg, #10B981 0%, #059669 100%);
            color: #FFFFFF;
            border: none;
            border-radius: 10px;
            padding: 0.875rem 1.5rem;
            font-family: "Inter", sans-serif;
            font-weight: 600;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0px 4px 15px rgba(16, 185, 129, 0.2);
        }

        .flight-book-btn:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-2px);
            box-shadow: 0px 8px 25px rgba(16, 185, 129, 0.3);
        }

        .flight-login-btn {
            background: linear-gradient(135deg, #4D73FC 0%, #6B8AFF 100%);
            color: #FFFFFF;
            border: none;
            border-radius: 10px;
            padding: 0.875rem 1.5rem;
            font-family: "Inter", sans-serif;
            font-weight: 600;
            font-size: 0.875rem;
            text-decoration: none;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            box-shadow: 0px 4px 15px rgba(77, 115, 252, 0.2);
        }

        .flight-login-btn:hover {
            background: linear-gradient(135deg, #3A5FE8 0%, #5A7AFF 100%);
            color: #FFFFFF;
            transform: translateY(-2px);
            box-shadow: 0px 8px 25px rgba(77, 115, 252, 0.3);
        }

        .flight-details-btn {
            background: #FFFFFF;
            border: 2px solid #ECECF2;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #4D73FC;
        }

        .flight-details-btn:hover {
            background: #4D73FC;
            border-color: #4D73FC;
            color: #FFFFFF;
            transform: scale(1.1);
        }

        /* Professional Pagination Styles */
        .professional-pagination-wrapper {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 2rem;
            padding: 1.5rem;
            background: linear-gradient(135deg, #F8F8FF 0%, #FFFFFF 100%);
            border-radius: 15px;
            border: 1px solid #ECECF2;
            box-shadow: 0px 4px 15px rgba(77, 115, 252, 0.08);
        }

        .pagination-info {
            display: flex;
            align-items: center;
        }

        .flight-count-badge {
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, #4D73FC 0%, #6B8AFF 100%);
            color: #F8F8FF;
            padding: 0.75rem 1.25rem;
            border-radius: 25px;
            font-family: "Inter", sans-serif;
            font-size: 0.875rem;
            font-weight: 600;
            box-shadow: 0px 4px 15px rgba(77, 115, 252, 0.2);
        }

        .flight-count-badge i {
            color: #F8F8FF;
            opacity: 0.9;
        }

        .count-text {
            margin: 0;
        }

        .pagination-nav {
            display: flex;
            align-items: center;
        }

        .professional-pagination {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .pagination-item {
            display: flex;
        }

        .pagination-link {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 40px;
            height: 40px;
            padding: 0.5rem 0.75rem;
            background: #FFFFFF;
            border: 2px solid #ECECF2;
            border-radius: 8px;
            color: #16191A;
            text-decoration: none;
            font-family: "Inter", sans-serif;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .pagination-link:hover {
            background: linear-gradient(135deg, #4D73FC 0%, #6B8AFF 100%);
            border-color: #4D73FC;
            color: #F8F8FF;
            transform: translateY(-2px);
            box-shadow: 0px 4px 15px rgba(77, 115, 252, 0.3);
        }

        .pagination-item.active .pagination-link {
            background: linear-gradient(135deg, #4D73FC 0%, #6B8AFF 100%);
            border-color: #4D73FC;
            color: #F8F8FF;
            box-shadow: 0px 4px 15px rgba(77, 115, 252, 0.3);
        }

        .pagination-item.active .pagination-link:hover {
            background: linear-gradient(135deg, #3A5FE8 0%, #5A7AFF 100%);
            transform: translateY(-2px);
            box-shadow: 0px 6px 20px rgba(77, 115, 252, 0.4);
        }

        .pagination-item.disabled .pagination-link {
            background: #F8F9FA;
            border-color: #E9ECEF;
            color: #ADB5BD;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .pagination-item.disabled .pagination-link:hover {
            background: #F8F9FA;
            border-color: #E9ECEF;
            color: #ADB5BD;
            transform: none;
            box-shadow: none;
        }

        .prev-link, .next-link {
            padding: 0.5rem 1rem;
            min-width: auto;
        }

        .prev-link i, .next-link i {
            font-size: 0.75rem;
        }

        @media (max-width: 768px) {
            .btn-group {
                flex-wrap: wrap;
                gap: 0.25rem;
            }

            .input-group {
                width: 100% !important;
                margin-bottom: 0.5rem;
            }

            .table-responsive {
                font-size: 0.875rem;
            }

            .cus-btn,
            .cus-btn-2 {
                font-size: 12px;
                padding: 6px 12px;
            }

            /* Professional Filter Mobile Styles */
            .filter-header {
                padding: 1rem 1.25rem;
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .filter-title {
                justify-content: center;
            }

            .filter-body {
                padding: 1.25rem 1rem;
            }

            .filter-grid {
                grid-template-columns: 1fr;
                gap: 1.25rem;
            }

            .custom-filter-select {
                padding: 0.75rem 2.25rem 0.75rem 0.875rem;
                font-size: 0.8rem;
            }

            .custom-filter-input {
                padding: 0.75rem 2.25rem 0.75rem 0.875rem;
                font-size: 0.8rem;
            }

            .custom-datepicker-input {
                padding: 0.75rem 3.5rem 0.75rem 0.875rem;
                font-size: 0.8rem;
            }

            .datepicker-icon {
                right: 0.875rem;
                font-size: 0.8rem;
            }

            .datepicker-clear {
                right: 2.25rem;
                width: 16px;
                height: 16px;
            }

            .flatpickr-calendar {
                font-size: 0.8rem;
            }

            .flatpickr-day {
                height: 32px;
                line-height: 32px;
            }

            .filter-label {
                font-size: 0.8rem;
            }

            .filter-apply-btn {
                padding: 0.75rem 1.25rem;
                font-size: 0.8rem;
            }

            .filter-container:hover {
                transform: none;
            }

            /* Professional Search, Sort & Pagination Mobile Styles */
            .professional-search-wrapper {
                min-width: 100%;
                margin-bottom: 0.75rem;
            }

            .search-input-container {
                border-radius: 8px;
            }

            .professional-search-input {
                padding: 0.75rem 2.25rem 0.75rem 2.5rem;
                font-size: 0.8rem;
            }

            .professional-sort-wrapper {
                width: 100%;
            }

            .professional-sort-btn {
                width: 100%;
                justify-content: center;
                padding: 0.75rem 1rem;
                font-size: 0.8rem;
                border-radius: 8px;
            }

            .professional-dropdown-menu {
                width: 100%;
                border-radius: 8px;
            }

            .professional-dropdown-item {
                padding: 0.625rem 1rem;
                font-size: 0.8rem;
            }

            .professional-pagination-wrapper {
                flex-direction: column;
                gap: 1rem;
                padding: 1rem;
                margin-top: 1.5rem;
            }

            .flight-count-badge {
                padding: 0.625rem 1rem;
                font-size: 0.8rem;
                border-radius: 20px;
            }

            .professional-pagination {
                gap: 0.25rem;
                flex-wrap: wrap;
                justify-content: center;
            }

            .pagination-link {
                min-width: 35px;
                height: 35px;
                padding: 0.375rem 0.625rem;
                font-size: 0.8rem;
                border-radius: 6px;
            }

            .prev-link, .next-link {
                padding: 0.375rem 0.75rem;
                font-size: 0.75rem;
            }

            .prev-link i, .next-link i {
                font-size: 0.7rem;
            }

            /* Mobile meal badge styles */
            .meal-badge {
                font-size: 0.7rem;
                padding: 0.375rem 0.625rem;
                border-radius: 6px;
            }

            .meal-badge i {
                font-size: 0.65rem;
            }

            /* Mobile Flight Cards Responsive Styles */
            .flight-card-header {
                flex-direction: column;
                gap: 0.75rem;
                padding: 1rem;
                text-align: center;
            }

            .flight-route {
                font-size: 0.875rem;
            }

            .flight-date {
                padding: 0.375rem 0.75rem;
                font-size: 0.8rem;
            }

            .flight-card-body {
                padding: 1rem;
            }

            .flight-timeline {
                flex-direction: column;
                gap: 1rem;
                margin-bottom: 1rem;
            }

            .departure-info, .arrival-info {
                flex: none;
            }

            .departure-info .time, .arrival-info .time {
                font-size: 1.5rem;
            }

            .flight-path {
                margin: 0;
                order: -1;
                width: 100%;
            }

            .flight-line {
                margin-bottom: 0.25rem;
            }

            .flight-duration {
                padding: 0.375rem 0.75rem;
                font-size: 0.8rem;
                margin: 0 0.5rem;
            }

            .flight-meta {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }

            .flight-services {
                flex-wrap: wrap;
                gap: 0.5rem;
            }

            .flight-card-footer {
                flex-direction: column;
                gap: 1rem;
                padding: 1rem;
                text-align: center;
            }

            .action-section {
                width: 100%;
                justify-content: center;
            }

            .flight-book-btn, .flight-login-btn {
                padding: 0.75rem 1.25rem;
                font-size: 0.8rem;
            }
        }
    </style>
@endpush

@push('title')
    {{ config('app.name', 'Sky Avenue') }} - Home
@endpush

@push('description')
    {{ config('app.name', 'Sky Avenue') }} - Your trusted airline booking partner. Book flights online with ease.
@endpush

@push('content')
    <!-- Hero Banner start -->
    <section class="hero-banner-1">
        <div class="container-fluid">
            <div class="content">
                <div class="vector-image">
                    <svg xmlns="http://www.w3.org/2000/svg" width="1414" height="319" viewBox="0 0 1414 319" fill="none">
                        <path class="path"
                            d="M-0.5 215C62.4302 220.095 287 228 373 143.5C444.974 72.7818 368.5 -3.73136 320.5 1.99997C269.5 8.08952 231.721 43.5 253.5 119C275.279 194.5 367 248.212 541.5 207.325C675.76 175.867 795.5 82.7122 913 76.7122C967.429 73.9328 1072.05 88.6813 1085 207.325C1100 344.712 882 340.212 922.5 207.325C964.415 69.7967 1354 151.5 1479 183.5"
                            stroke="#ECECF2" stroke-width="6" stroke-linecap="round" stroke-dasharray="round" />

                        <path class="dashed"
                            d="M-0.5 215C62.4302 220.095 287 228 373 143.5C444.974 72.7818 368.5 -3.73136 320.5 1.99997C269.5 8.08952 231.721 43.5 253.5 119C275.279 194.5 367 248.212 541.5 207.325C675.76 175.867 795.5 82.7122 913 76.7122C967.429 73.9328 1072.05 88.6813 1085 207.325C1100 344.712 882 340.212 922.5 207.325C964.415 69.7967 1354 151.5 1479 183.5"
                            stroke="#212627" stroke-width="6" stroke-linecap="round" stroke-dasharray="22 22" />
                    </svg>
                    <div class="location-image">
                        <img src="{{ asset('assets/media/icons/location-blue.png') }}" alt="">
                    </div>
                </div>
                <div class="row align-items-center row-gap-5">
                    <div class="col-xxl-3 col-xl-4 col-lg-4 col-md-5 col-sm-5">
                        <div class="content-block">
                            <h1 class="lightest-black mb-16"><span class="color-primary">Book</span> Your Dream <span
                                    class="color-primary">Flights</span> Now!</h1>
                            <p class="dark-gray mb-24">Lorem ipsum dolor sit amet consectetur. Felis tristique pretium leo
                                nisi at risus ac enim.</p>
                            <a href="{{ route('flights.booking') }}" class="cus-btn">Book Now</a>
                        </div>
                    </div>
                    <div class="col-xxl-9 col-xl-8 col-lg-8 col-md-7 col-sm-7">
                        <div class="image flynow-tilt"
                            data-tilt-options='{ "glare": false, "maxGlare": 0, "maxTilt": 3, "speed": 700, "scale": 1.02 }'>
                            <img src="{{ asset('assets/media/banner/plane.png') }}" alt="">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Hero Banner End -->

    <!-- Available Flights Section Start -->
    <section class="available-flights mb-20 mt-5">
        <div class="container-fluid">
            <div class="content">
                <div class="card">
                    <div class="card-header py-3 px-3">
                        <div class="d-flex justify-content-between align-items-center flex-wrap">
                            <h4 class="lightest-black bold mb-0">Flight Search & Booking</h4>
                            <div class="d-flex gap-3 flex-wrap">
                                <!-- Professional Search Input -->
                                <div class="professional-search-wrapper">
                                    <div class="search-input-container">
                                        <i class="fas fa-search search-icon"></i>
                                        <input type="text" class="professional-search-input" id="flightSearch" placeholder="Search flights, airlines, routes...">
                                        <div class="search-clear-btn" id="clearSearch" style="display: none;">
                                            <i class="fas fa-times"></i>
                                        </div>
                                    </div>
                                </div>
                                <!-- Professional Sort Dropdown -->
                                <div class="professional-sort-wrapper">
                                    <div class="sort-dropdown-container">
                                        <button class="professional-sort-btn" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="fas fa-sort-amount-down sort-icon"></i>
                                            <span class="sort-text">Sort By</span>
                                            <i class="fas fa-chevron-down dropdown-arrow"></i>
                                        </button>
                                        <ul class="professional-dropdown-menu dropdown-menu" aria-labelledby="sortDropdown">
                                            <li><a class="professional-dropdown-item sort-option" href="#" data-sort="date">
                                                <i class="fas fa-calendar-alt me-2"></i>Date
                                            </a></li>
                                            <li><a class="professional-dropdown-item sort-option" href="#" data-sort="price">
                                                <i class="fas fa-money-bill-wave me-2"></i>Price
                                            </a></li>
                                            <li><a class="professional-dropdown-item sort-option" href="#" data-sort="airline">
                                                <i class="fas fa-plane me-2"></i>Airline
                                            </a></li>
                                            <li><a class="professional-dropdown-item sort-option" href="#" data-sort="seats">
                                                <i class="fas fa-chair me-2"></i>Available Seats
                                            </a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">

                        <!-- Professional Flight Filter Controls -->
                        <div class="flight-filter-section mb-4">
                            <div class="filter-container">
                                <div class="filter-header">
                                    <div class="filter-title">
                                        <i class="fas fa-filter filter-icon"></i>
                                        <h5 class="filter-heading">Flight Filters</h5>
                                    </div>
                                    <div class="filter-actions">
                                        <button type="button" class="cus-btn-2 btn-clear" id="clearAllFilters">
                                            <i class="fas fa-times me-1"></i>Clear All
                                        </button>
                                    </div>
                                </div>

                                <div class="filter-body">
                                    <div class="filter-grid">
                                        <div class="filter-group">
                                            <label for="departureDate" class="filter-label">
                                                <i class="fas fa-calendar-alt label-icon"></i>
                                                Departure Date
                                            </label>
                                            <div class="custom-datepicker-wrapper">
                                                <input type="text" class="custom-datepicker-input" id="departureDate" placeholder="Select departure date" readonly>
                                                <i class="fas fa-calendar-alt datepicker-icon"></i>
                                                <div class="datepicker-clear" id="clearDepartureDate" style="display: none;">
                                                    <i class="fas fa-times"></i>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="filter-group">
                                            <label for="airlineFilter" class="filter-label">
                                                <i class="fas fa-plane label-icon"></i>
                                                Airline
                                            </label>
                                            <div class="custom-select-wrapper">
                                                <select class="custom-filter-select" id="airlineFilter">
                                                    <option value="">All Airlines</option>
                                                    <option value="airblue">Airblue</option>
                                                    <option value="emirates">Emirates</option>
                                                    <option value="qatar">Qatar Airways</option>
                                                </select>
                                                <i class="fas fa-chevron-down select-arrow"></i>
                                            </div>
                                        </div>

                                        <div class="filter-group">
                                            <label for="sectorFilter" class="filter-label">
                                                <i class="fas fa-globe label-icon"></i>
                                                Sector
                                            </label>
                                            <div class="custom-select-wrapper">
                                                <select class="custom-filter-select" id="sectorFilter">
                                                    <option value="">All Sectors</option>
                                                    <option value="domestic">Domestic</option>
                                                    <option value="international">International</option>
                                                </select>
                                                <i class="fas fa-chevron-down select-arrow"></i>
                                            </div>
                                        </div>

                                        <div class="filter-group">
                                            <label for="filterMeal" class="filter-label">
                                                <i class="fas fa-utensils label-icon"></i>
                                                Meal Service
                                            </label>
                                            <div class="custom-select-wrapper">
                                                <select class="custom-filter-select" id="filterMeal">
                                                    <option value="">Any Meal Option</option>
                                                    <option value="yes">With Meal</option>
                                                    <option value="no">No Meal</option>
                                                </select>
                                                <i class="fas fa-chevron-down select-arrow"></i>
                                            </div>
                                        </div>

                                        <div class="filter-group">
                                            <label for="filterMinSeats" class="filter-label">
                                                <i class="fas fa-chair label-icon"></i>
                                                Minimum Seats
                                            </label>
                                            <div class="custom-input-wrapper">
                                                <input type="number" class="custom-filter-input" id="filterMinSeats"
                                                    placeholder="Min seats" min="1" max="20">
                                                <i class="fas fa-users input-icon"></i>
                                            </div>
                                        </div>

                                        <div class="filter-group">
                                            <label for="filterMaxPrice" class="filter-label">
                                                <i class="fas fa-money-bill-wave label-icon"></i>
                                                Maximum Price (PKR)
                                            </label>
                                            <div class="custom-input-wrapper">
                                                <input type="number" class="custom-filter-input" id="filterMaxPrice"
                                                    placeholder="Max price" min="0" step="1000">
                                                <i class="fas fa-rupee-sign input-icon"></i>
                                            </div>
                                        </div>

                                        <div class="filter-group filter-action">
                                            <div class="action-buttons">
                                                <button type="button" class="cus-btn filter-apply-btn"
                                                    id="applyTableFilter">
                                                    <i class="fas fa-search me-2"></i>
                                                    Apply Filters
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Modern Flight Cards Display -->
            <div class="flight-cards-container" id="flightCardsContainer">
                <!-- Flight Card 1 -->
                <div class="flight-card" data-group="uae">
                    <div class="flight-card-header">
                        <div class="flight-route">
                            <i class="fas fa-plane-departure text-primary me-2"></i>
                            <span class="route-text">Peshawar to Ras Al Khaimah</span>
                        </div>
                        <div class="flight-date">19 Jul 2025</div>
                    </div>

                    <div class="flight-card-body">
                        <div class="flight-timeline">
                            <div class="departure-info">
                                <div class="time">17:25</div>
                                <div class="airport-code">PEW</div>
                                <div class="city-name">Peshawar</div>
                            </div>

                            <div class="flight-path">
                                <div class="flight-line">
                                    <div class="line-start"></div>
                                    <div class="flight-duration">
                                        <i class="fas fa-plane"></i>
                                        <span>1h 55m</span>
                                    </div>
                                    <div class="line-end"></div>
                                </div>
                                <div class="flight-type">
                                    <span class="badge bg-info">Direct</span>
                                </div>
                            </div>

                            <div class="arrival-info">
                                <div class="time">19:20</div>
                                <div class="airport-code">RKT</div>
                                <div class="city-name">Ras Al Khaimah</div>
                            </div>
                        </div>

                        <div class="flight-details">
                            <div class="flight-meta">
                                <div class="airline-info">
                                    <span class="badge bg-primary airline-badge">Airblue</span>
                                    <span class="flight-number">G9865</span>
                                </div>
                                <div class="flight-services">
                                    <div class="service-item">
                                        <i class="fas fa-suitcase text-muted me-1"></i>
                                        <span>20+7 KG</span>
                                    </div>
                                    <div class="service-item">
                                        <span class="badge bg-danger meal-badge">
                                            <i class="fas fa-times me-1"></i>No Meal
                                        </span>
                                    </div>
                                    <div class="service-item">
                                        <i class="fas fa-chair text-muted me-1"></i>
                                        <span>3 seats</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flight-card-footer">
                        <div class="price-section">
                            @auth
                                <div class="price">PKR 45,000</div>
                                <div class="price-label">per person</div>
                            @else
                                <div class="price-locked" data-bs-toggle="tooltip" data-bs-placement="top"
                                    title="Please login to view flight prices">
                                    <i class="fas fa-lock me-2"></i>Login to see pricing
                                </div>
                            @endauth
                        </div>
                        <div class="action-section">
                            @auth
                                <button class="flight-book-btn" onclick="bookFlight('G9865')">
                                    <span>Book Flight</span>
                                </button>
                            @else
                                <a href="{{ route('login') }}" class="flight-login-btn" data-bs-toggle="tooltip"
                                    data-bs-placement="top" title="Login to book this flight">
                                    <i class="fas fa-sign-in-alt me-2"></i>Login
                                </a>
                            @endauth
                            <button class="flight-details-btn" onclick="showFlightDetails('G9865')">
                                <i class="fas fa-info-circle"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Flight Card 2 -->
                <div class="flight-card" data-group="bahrain">
                    <div class="flight-card-header">
                        <div class="flight-route">
                            <i class="fas fa-plane-departure text-primary me-2"></i>
                            <span class="route-text">Islamabad to Ras Al Khaimah</span>
                        </div>
                        <div class="flight-date">21 Jul 2025</div>
                    </div>

                    <div class="flight-card-body">
                        <div class="flight-timeline">
                            <div class="departure-info">
                                <div class="time">08:35</div>
                                <div class="airport-code">ISB</div>
                                <div class="city-name">Islamabad</div>
                            </div>

                            <div class="flight-path">
                                <div class="flight-line">
                                    <div class="line-start"></div>
                                    <div class="flight-duration">
                                        <i class="fas fa-plane"></i>
                                        <span>2h 05m</span>
                                    </div>
                                    <div class="line-end"></div>
                                </div>
                                <div class="flight-type">
                                    <span class="badge bg-info">Direct</span>
                                </div>
                            </div>

                            <div class="arrival-info">
                                <div class="time">10:40</div>
                                <div class="airport-code">RKT</div>
                                <div class="city-name">Ras Al Khaimah</div>
                            </div>
                        </div>

                        <div class="flight-details">
                            <div class="flight-meta">
                                <div class="airline-info">
                                    <span class="badge bg-primary airline-badge">Airblue</span>
                                    <span class="flight-number">G9869</span>
                                </div>
                                <div class="flight-services">
                                    <div class="service-item">
                                        <i class="fas fa-suitcase text-muted me-1"></i>
                                        <span>20+7 KG</span>
                                    </div>
                                    <div class="service-item">
                                        <span class="badge bg-danger meal-badge">
                                            <i class="fas fa-times me-1"></i>No Meal
                                        </span>
                                    </div>
                                    <div class="service-item">
                                        <i class="fas fa-chair text-muted me-1"></i>
                                        <span>2 seats</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flight-card-footer">
                        <div class="price-section">
                            @auth
                                <div class="price">PKR 52,000</div>
                                <div class="price-label">per person</div>
                            @else
                                <div class="price-locked" data-bs-toggle="tooltip" data-bs-placement="top"
                                    title="Please login to view flight prices">
                                    <i class="fas fa-lock me-2"></i>Login to see pricing
                                </div>
                            @endauth
                        </div>
                        <div class="action-section">
                            @auth
                                <button class="flight-book-btn" onclick="bookFlight('G9869')">
                                    <span>Book Flight</span>
                                </button>
                            @else
                                <a href="{{ route('login') }}" class="flight-login-btn" data-bs-toggle="tooltip"
                                    data-bs-placement="top" title="Login to book this flight">
                                    <i class="fas fa-sign-in-alt me-2"></i>Login
                                </a>
                            @endauth
                            <button class="flight-details-btn" onclick="showFlightDetails('G9869')">
                                <i class="fas fa-info-circle"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Flight Card 3 -->
                <div class="flight-card" data-group="ksa">
                    <div class="flight-card-header">
                        <div class="flight-route">
                            <i class="fas fa-plane-departure text-primary me-2"></i>
                            <span class="route-text">Islamabad to Jeddah</span>
                        </div>
                        <div class="flight-date">25 Jul 2025</div>
                    </div>

                    <div class="flight-card-body">
                        <div class="flight-timeline">
                            <div class="departure-info">
                                <div class="time">12:15</div>
                                <div class="airport-code">ISB</div>
                                <div class="city-name">Islamabad</div>
                            </div>

                            <div class="flight-path">
                                <div class="flight-line">
                                    <div class="line-start"></div>
                                    <div class="flight-duration">
                                        <i class="fas fa-plane"></i>
                                        <span>6h 15m</span>
                                    </div>
                                    <div class="line-end"></div>
                                </div>
                                <div class="flight-type">
                                    <span class="badge bg-warning">1 Stop</span>
                                </div>
                                <div class="stop-info">
                                    <small class="text-muted">via RAS AL KHAIMAH</small>
                                </div>
                            </div>

                            <div class="arrival-info">
                                <div class="time">18:30</div>
                                <div class="airport-code">JED</div>
                                <div class="city-name">Jeddah</div>
                            </div>
                        </div>

                        <div class="flight-details">
                            <div class="flight-meta">
                                <div class="airline-info">
                                    <span class="badge bg-primary airline-badge">Airblue</span>
                                    <span class="flight-number">G9871</span>
                                </div>
                                <div class="flight-services">
                                    <div class="service-item">
                                        <i class="fas fa-suitcase text-muted me-1"></i>
                                        <span>20+7 KG</span>
                                    </div>
                                    <div class="service-item">
                                        <span class="badge bg-success meal-badge">
                                            <i class="fas fa-utensils me-1"></i>With Meal
                                        </span>
                                    </div>
                                    <div class="service-item">
                                        <i class="fas fa-chair text-muted me-1"></i>
                                        <span>5 seats</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flight-card-footer">
                        <div class="price-section">
                            @auth
                                <div class="price">PKR 75,000</div>
                                <div class="price-label">per person</div>
                            @else
                                <div class="price-locked" data-bs-toggle="tooltip" data-bs-placement="top"
                                    title="Please login to view flight prices">
                                    <i class="fas fa-lock me-2"></i>Login to see pricing
                                </div>
                            @endauth
                        </div>
                        <div class="action-section">
                            @auth
                                <button class="flight-book-btn" onclick="bookFlight('G9871')">
                                    <span>Book Flight</span>
                                </button>
                            @else
                                <a href="{{ route('login') }}" class="flight-login-btn" data-bs-toggle="tooltip"
                                    data-bs-placement="top" title="Login to book this flight">
                                    <i class="fas fa-sign-in-alt me-2"></i>Login
                                </a>
                            @endauth
                            <button class="flight-details-btn" onclick="showFlightDetails('G9871')">
                                <i class="fas fa-info-circle"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Flight Card 4 -->
                <div class="flight-card" data-group="umrah">
                    <div class="flight-card-header">
                        <div class="flight-route">
                            <i class="fas fa-plane-departure text-primary me-2"></i>
                            <span class="route-text">Dubai to Jeddah</span>
                        </div>
                        <div class="flight-date">28 Jul 2025</div>
                    </div>

                    <div class="flight-card-body">
                        <div class="flight-timeline">
                            <div class="departure-info">
                                <div class="time">14:45</div>
                                <div class="airport-code">DXB</div>
                                <div class="city-name">Dubai</div>
                            </div>

                            <div class="flight-path">
                                <div class="flight-line">
                                    <div class="line-start"></div>
                                    <div class="flight-duration">
                                        <i class="fas fa-plane"></i>
                                        <span>1h 35m</span>
                                    </div>
                                    <div class="line-end"></div>
                                </div>
                                <div class="flight-type">
                                    <span class="badge bg-info">Direct</span>
                                </div>
                            </div>

                            <div class="arrival-info">
                                <div class="time">16:20</div>
                                <div class="airport-code">JED</div>
                                <div class="city-name">Jeddah</div>
                            </div>
                        </div>

                        <div class="flight-details">
                            <div class="flight-meta">
                                <div class="airline-info">
                                    <span class="badge bg-danger airline-badge">Emirates</span>
                                    <span class="flight-number">EK623</span>
                                </div>
                                <div class="flight-services">
                                    <div class="service-item">
                                        <i class="fas fa-suitcase text-muted me-1"></i>
                                        <span>30+7 KG</span>
                                    </div>
                                    <div class="service-item">
                                        <span class="badge bg-success meal-badge">
                                            <i class="fas fa-utensils me-1"></i>With Meal
                                        </span>
                                    </div>
                                    <div class="service-item">
                                        <i class="fas fa-chair text-muted me-1"></i>
                                        <span>8 seats</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flight-card-footer">
                        <div class="price-section">
                            @auth
                                <div class="price">PKR 95,000</div>
                                <div class="price-label">per person</div>
                            @else
                                <div class="price-locked" data-bs-toggle="tooltip" data-bs-placement="top"
                                    title="Please login to view flight prices">
                                    <i class="fas fa-lock me-2"></i>Login to see pricing
                                </div>
                            @endauth
                        </div>
                        <div class="action-section">
                            @auth
                                <button class="flight-book-btn" onclick="bookFlight('EK623')">
                                    <span>Book Flight</span>
                                </button>
                            @else
                                <a href="{{ route('login') }}" class="flight-login-btn" data-bs-toggle="tooltip"
                                    data-bs-placement="top" title="Login to book this flight">
                                    <i class="fas fa-sign-in-alt me-2"></i>Login
                                </a>
                            @endauth
                            <button class="flight-details-btn" onclick="showFlightDetails('EK623')">
                                <i class="fas fa-info-circle"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Flight Card 5 -->
                <div class="flight-card" data-group="hajj">
                    <div class="flight-card-header">
                        <div class="flight-route">
                            <i class="fas fa-plane-departure text-primary me-2"></i>
                            <span class="route-text">Doha to Medina</span>
                        </div>
                        <div class="flight-date">30 Jul 2025</div>
                    </div>

                    <div class="flight-card-body">
                        <div class="flight-timeline">
                            <div class="departure-info">
                                <div class="time">09:30</div>
                                <div class="airport-code">DOH</div>
                                <div class="city-name">Doha</div>
                            </div>

                            <div class="flight-path">
                                <div class="flight-line">
                                    <div class="line-start"></div>
                                    <div class="flight-duration">
                                        <i class="fas fa-plane"></i>
                                        <span>2h 15m</span>
                                    </div>
                                    <div class="line-end"></div>
                                </div>
                                <div class="flight-type">
                                    <span class="badge bg-info">Direct</span>
                                </div>
                            </div>

                            <div class="arrival-info">
                                <div class="time">11:45</div>
                                <div class="airport-code">MED</div>
                                <div class="city-name">Medina</div>
                            </div>
                        </div>

                        <div class="flight-details">
                            <div class="flight-meta">
                                <div class="airline-info">
                                    <span class="badge bg-success airline-badge">Qatar Airways</span>
                                    <span class="flight-number">QR615</span>
                                </div>
                                <div class="flight-services">
                                    <div class="service-item">
                                        <i class="fas fa-suitcase text-muted me-1"></i>
                                        <span>30+7 KG</span>
                                    </div>
                                    <div class="service-item">
                                        <span class="badge bg-success meal-badge">
                                            <i class="fas fa-utensils me-1"></i>With Meal
                                        </span>
                                    </div>
                                    <div class="service-item">
                                        <i class="fas fa-chair text-muted me-1"></i>
                                        <span>12 seats</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flight-card-footer">
                        <div class="price-section">
                            @auth
                                <div class="price">PKR 120,000</div>
                                <div class="price-label">per person</div>
                            @else
                                <div class="price-locked" data-bs-toggle="tooltip" data-bs-placement="top"
                                    title="Please login to view flight prices">
                                    <i class="fas fa-lock me-2"></i>Login to see pricing
                                </div>
                            @endauth
                        </div>
                        <div class="action-section">
                            @auth
                                <button class="flight-book-btn" onclick="bookFlight('QR615')">
                                    <span>Book Flight</span>
                                </button>
                            @else
                                <a href="{{ route('login') }}" class="flight-login-btn" data-bs-toggle="tooltip"
                                    data-bs-placement="top" title="Login to book this flight">
                                    <i class="fas fa-sign-in-alt me-2"></i>Login
                                </a>
                            @endauth
                            <button class="flight-details-btn" onclick="showFlightDetails('QR615')">
                                <i class="fas fa-info-circle"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Professional Pagination -->
            <div class="professional-pagination-wrapper">
                <div class="pagination-info">
                    <div class="flight-count-badge">
                        <i class="fas fa-plane-departure me-2"></i>
                        <span class="count-text">Showing 5 of 25 flights</span>
                    </div>
                </div>
                <nav aria-label="Flight pagination" class="pagination-nav">
                    <ul class="professional-pagination">
                        <li class="pagination-item disabled">
                            <a class="pagination-link prev-link" href="#" tabindex="-1" aria-disabled="true">
                                <i class="fas fa-chevron-left me-1"></i>Previous
                            </a>
                        </li>
                        <li class="pagination-item active">
                            <a class="pagination-link" href="#">1</a>
                        </li>
                        <li class="pagination-item">
                            <a class="pagination-link" href="#">2</a>
                        </li>
                        <li class="pagination-item">
                            <a class="pagination-link" href="#">3</a>
                        </li>
                        <li class="pagination-item">
                            <a class="pagination-link next-link" href="#">
                                Next<i class="fas fa-chevron-right ms-1"></i>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
        </div>
        </div>
        </div>
        </div>
    </section>
    <!-- Available Flights Section End -->

    <!-- Benefits Start -->
    <section class="benefit p-40" data-sal="slide-up" data-sal-duration="800" data-sal-delay="100"
        data-sal-easing="ease-in-out">
        <div class="container-fluid">
            <div class="row">
                <div class="col-xl-4 col-lg-6 col-md-6 mb-xl-0 mb-24">
                    <div class="benefit-block bg-white">
                        <div class="image-box">
                            <img src="{{ asset('assets/media/vector/benefit-1.png') }}" alt="">
                        </div>
                        <div class="text-box">
                            <h4 class="lightest-black mb-8">We are Now Available</h4>
                            <p class="color-medium-gray">Call +1 555 666 888 contact with us</p>
                        </div>
                    </div>
                </div>
                <div class="col-xl-4 col-lg-6 col-md-6 mb-lg-0 mb-24">
                    <div class="benefit-block bg-white">
                        <div class="image-box">
                            <img src="{{ asset('assets/media/vector/benefit-2.png') }}" alt="">
                        </div>
                        <div class="text-box">
                            <h4 class="lightest-black mb-8">International Flight</h4>
                            <p class="color-medium-gray">Call +1 555 666 888 contact with us</p>
                        </div>
                    </div>
                </div>
                <div class="col-xl-4 col-lg-6 col-md-6">
                    <div class="benefit-block bg-white">
                        <div class="image-box">
                            <img src="{{ asset('assets/media/vector/benefit-3.png') }}" alt="">
                        </div>
                        <div class="text-box">
                            <h4 class="lightest-black mb-8">Check Refund</h4>
                            <p class="color-medium-gray">Call +1 555 666 888 contact with us</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Benefits End -->

    <!-- Travel us sec Start -->
    <div class="travel-sec mb-40">
        <div class="cloud-vector-block">
            <img src="{{ asset('assets/media/vector/cloud-vector.png') }}" alt="" class="cloud-vector">
        </div>
        <img src="{{ asset('assets/media/vector/vector-line.png') }}" alt="" class="line-vector">
        <div class="container-fluid">
            <div class="row align-items-center justify-content-center row-gap-sm-5 row-gap-4">
                <div class="col-xxl-3 col-lg-4 col-md-8" data-sal="slide-right" data-sal-duration="800"
                    data-sal-delay="100" data-sal-easing="ease-in-out">
                    <div class="left-content">
                        <img src="{{ asset('assets/media/logo.png') }}" alt="" class="mb-40">
                        <div class="text mb-40">
                            <span class="h1 review-block bg-lightest-gray"> TRAVEL</span> <span
                                class="h1 review-block bg-lightest-gray">All</span>
                            <span class="h1 bg-lightest-gray"> OVER</span> <span
                                class="h1 bg-lightest-gray color-primary">The</span>
                            <span class="h1 bg-lightest-gray color-primary"> WORLD</span>
                        </div>
                        <a href="{{ route('flights.booking') }}" class="cus-btn">Booking Now</a>
                    </div>
                </div>
                <div class="offset-xxl-1 col-xxl-8 col-lg-8 col-md-10">
                    <div class="right-images-block" data-sal="slide-up" data-sal-duration="800" data-sal-delay="100"
                        data-sal-easing="ease-in-out">
                        <img src="{{ asset('assets/media/vector/border-line.png') }}" alt=""
                            class="border-image">
                        <div class="row justify-content-center align-items-center">
                            <div class="col-lg-3 col-md-3 col-3">
                                <img src="{{ asset('assets/media/images/paris.png') }}" alt=""
                                    class="side-image">
                            </div>
                            <div class="col-lg-5 col-md-5 col-5">
                                <img src="{{ asset('assets/media/images/dubai.png') }}" alt=""
                                    class="center-image">
                            </div>
                            <div class="col-lg-3 col-md-3 col-3">
                                <img src="{{ asset('assets/media/images/italy.png') }}" alt=""
                                    class="side-image">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Travel us sec End -->

    <!-- Global Travel Start -->
    <section class="global-travel-hotel p-40">
        <div class="container-fluid">
            <div class="d-flex justify-content-between mb-40 flex-md-nowrap flex-wrap">
                <h3 class="fw-700 lightest-black mb-md-0 mb-3">Popular Global Travel Destinations</h3>
                <a href="{{ route('hotels.index') }}" class="cus-btn">Show More</a>
            </div>
            <div class="row justify-content-center row-gap-4">
                <div class="col-xxl-6 col-lg-5 col-md-7 col-10" data-sal="slide-right" data-sal-duration="800"
                    data-sal-delay="100" data-sal-easing="ease-in-out">
                    <img src="{{ asset('assets/media/images/map.png') }}" alt="">
                </div>
                <div class="col-xxl-6 col-lg-7">
                    <div class="row">
                        <div class="col-lg-6 col-md-6 col-sm-6" data-sal="slide-left" data-sal-duration="800"
                            data-sal-delay="100" data-sal-easing="ease-in-out">
                            <div class="hotel-block bg-white p-24 light-shadow mb-24">
                                <div class="image-box mb-24">
                                    <a href="{{ route('hotels.detail') }}"><img
                                            src="{{ asset('assets/media/cities/image-1.png') }}" alt=""></a>
                                    <div class="price">
                                        <h6>form <span class="color-sec">$350</span></h6>
                                    </div>
                                </div>
                                <div class="content-box">
                                    <h5 class="black mb-8"><a href="{{ route('hotels.detail') }}">Rome</a></h5>
                                    <div class="d-flex gap-8 mb-24">
                                        <div class="icon d-flex align-items-center gap-8">
                                            <img src="{{ asset('assets/media/icons/location.png') }}" alt="">
                                            <h6 class="color-medium-gray">Italy - </h6>
                                        </div>
                                        <div class="icon d-flex align-items-center gap-8">
                                            <img src="{{ asset('assets/media/icons/hotel.png') }}" alt="">
                                            <h6 class="color-medium-gray">Hotels </h6>
                                        </div>
                                    </div>
                                    <a href="{{ route('hotels.detail') }}" class="cus-btn full-width">Discover</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-6">
                            <div class="hotel-block bg-white p-24 light-shadow mb-24">
                                <div class="image-box mb-24">
                                    <a href="{{ route('hotels.detail') }}"><img
                                            src="{{ asset('assets/media/cities/image-2.png') }}" alt=""></a>
                                    <div class="price">
                                        <h6>form <span class="color-sec">$260</span></h6>
                                    </div>
                                </div>
                                <div class="content-box">
                                    <h5 class="black mb-8"><a href="{{ route('hotels.detail') }}">Tokyo</a></h5>
                                    <div class="d-flex gap-8 mb-24">
                                        <div class="icon d-flex align-items-center gap-8">
                                            <img src="{{ asset('assets/media/icons/location.png') }}" alt="">
                                            <h6 class="color-medium-gray">Japan - </h6>
                                        </div>
                                        <div class="icon d-flex align-items-center gap-8">
                                            <img src="{{ asset('assets/media/icons/hotel.png') }}" alt="">
                                            <h6 class="color-medium-gray">Hotels </h6>
                                        </div>
                                    </div>
                                    <a href="{{ route('hotels.detail') }}" class="cus-btn full-width">Discover</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-6">
                            <div class="hotel-block bg-white p-24 light-shadow mb-24">
                                <div class="image-box mb-24">
                                    <a href="{{ route('hotels.detail') }}"><img
                                            src="{{ asset('assets/media/cities/image-3.png') }}" alt=""></a>
                                    <div class="price">
                                        <h6>form <span class="color-sec">$290</span></h6>
                                    </div>
                                </div>
                                <div class="content-box">
                                    <h5 class="black mb-8"><a href="{{ route('hotels.detail') }}">Sydney</a></h5>
                                    <div class="d-flex gap-8 mb-24">
                                        <div class="icon d-flex align-items-center gap-8">
                                            <img src="{{ asset('assets/media/icons/location.png') }}" alt="">
                                            <h6 class="color-medium-gray">Australia - </h6>
                                        </div>
                                        <div class="icon d-flex align-items-center gap-8">
                                            <img src="{{ asset('assets/media/icons/hotel.png') }}" alt="">
                                            <h6 class="color-medium-gray">Hotels </h6>
                                        </div>
                                    </div>
                                    <a href="{{ route('hotels.detail') }}" class="cus-btn full-width">Discover</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-6">
                            <div class="hotel-block bg-white p-24 light-shadow mb-24">
                                <div class="image-box mb-24">
                                    <a href="{{ route('hotels.detail') }}"><img
                                            src="{{ asset('assets/media/cities/image-4.png') }}" alt=""></a>
                                    <div class="price">
                                        <h6>form <span class="color-sec">$340</span></h6>
                                    </div>
                                </div>
                                <div class="content-box">
                                    <h5 class="black mb-8"><a href="{{ route('hotels.detail') }}">London</a></h5>
                                    <div class="d-flex gap-8 mb-24">
                                        <div class="icon d-flex align-items-center gap-8">
                                            <img src="{{ asset('assets/media/icons/location.png') }}" alt="">
                                            <h6 class="color-medium-gray">United Kingdom (UK) - </h6>
                                        </div>
                                        <div class="icon d-flex align-items-center gap-8">
                                            <img src="{{ asset('assets/media/icons/hotel.png') }}" alt="">
                                            <h6 class="color-medium-gray">Hotels </h6>
                                        </div>
                                    </div>
                                    <a href="{{ route('hotels.detail') }}" class="cus-btn full-width">Discover</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Global Travel End -->

    <!-- Achievement Section Start -->
    <section class="achievement p-40">
        <div class="container-fluid">
            <div class="bg-white light-shadow br-20 achievements-block">
                <div class="row align-items-center">
                    <div class="col-xl-6 col-lg-12 mb-xl-0 mb-24" data-sal="slide-up" data-sal-duration="800"
                        data-sal-delay="100" data-sal-easing="ease-in-out">
                        <h5 class="color-primary mb-16">Achievement</h5>
                        <h3 class="h3 bold mb-8">Your Destination Awaits, Book Now</h3>
                        <p class="dark-gray mb-24 w-90">Lorem ipsum dolor sit amet consectetur. Sed leo sit semper sed
                            facilisis ultrices urna eu. In tellus interdum vel ac massa interdum viverra elementum auctor.
                        </p>
                        <div class="counter-section mb-24">
                            <div class="row row-gap-4">
                                <div class="col-sm-6">
                                    <div class="counter-count bg-lightest-gray">
                                        <div>
                                            <span class="count h3 bold color-primary">12870</span>
                                            <span class="h3 bold color-primary">+</span>
                                            <h5 class="title white">Happy Customers</h5>
                                        </div>
                                        <img src="{{ asset('assets/media/icons/user.png') }}" alt="">
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="counter-count bg-lightest-gray">
                                        <div>
                                            <span class="count h3 bold color-primary">100</span>
                                            <span class="h3 bold color-primary">%</span>
                                            <h5 class="title white">Client Satisfied</h5>
                                        </div>
                                        <img src="{{ asset('assets/media/icons/user-2.png') }}" alt="">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="d-md-flex gap-32 align-items-center">
                            <h5 class="lightest-black mb-md-0 mb-16">Let's Connect Reach Out for More Information</h5>
                            <a href="{{ route('contact') }}" class="cus-btn">Contact us</a>
                        </div>
                    </div>
                    <div class="col-xl-6 col-lg-12" data-sal="slide-down" data-sal-duration="800" data-sal-delay="100"
                        data-sal-easing="ease-in-out">
                        <img src="{{ asset('assets/media/images/achievement-image.png') }}" alt=""
                            class="achievement-image light-shadow br-20">
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Achievement Section End -->

    <!-- Blog Area Start -->
    <section class="news-blog p-40" data-sal="slide-up" data-sal-duration="800" data-sal-delay="100"
        data-sal-easing="ease-in-out">
        <div class="container-fluid">
            <div class="d-flex justify-content-between mb-40">
                <h3 class="h3 bold lightest-black p-0">Our Latest News</h3>
                <a href="{{ route('blog.index') }}" class="cus-btn">Show More</a>
            </div>
            <div class="row row-gap-4">
                <div class="col-xxl-6 col-xl-4 col-lg-12 col-md-6 col-sm-6">
                    <div class="blog-box bg-white light-shadow p-24 br-20">
                        <div class="row align-items-center row-gap-3">
                            <div class="col-xxl-6 col-xl-12 col-lg-6">
                                <div class="image-box">
                                    <a href="{{ route('blog.detail') }}"><img
                                            src="{{ asset('assets/media/blog/blog-1.png') }}" alt=""></a>
                                </div>
                            </div>
                            <div class="col-xxl-6 col-xl-12 col-lg-6">
                                <div class="content-box">
                                    <div class="d-flex gap-16 mb-24">
                                        <div class="d-flex align-items-center gap-8">
                                            <img src="{{ asset('assets/media/icons/user-bk.png') }}" alt="">
                                            <p class="h6 dark-gray">Malisa John</p>
                                        </div>
                                        <div class="vr-line"></div>
                                        <div class="d-flex align-items-center gap-8">
                                            <img src="{{ asset('assets/media/icons/calender.png') }}" alt="">
                                            <p class="h6 dark-gray">08 Aug, 2023</p>
                                        </div>
                                    </div>
                                    <h5 class="lightest-black mb-8"><a href="{{ route('blog.detail') }}">Roaming with
                                            Purpose: Traveling Responsibly and Sustainably</a></h5>
                                    <p class="dark-gray mb-24">Lorem ipsum dolor sit amet consectetur. Feugiat sit
                                        eleifend
                                        tortor.</p>
                                    <a href="{{ route('blog.detail') }}" class="cus-btn small-pad">Read More</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xxl-6 col-xl-4 col-lg-12 col-md-6 col-sm-6">
                    <div class="blog-box bg-white light-shadow p-24 br-20">
                        <div class="row align-items-center row-gap-3">
                            <div class="col-xxl-6 col-xl-12 col-lg-6">
                                <div class="image-box">
                                    <a href="{{ route('blog.detail') }}"><img
                                            src="{{ asset('assets/media/blog/blog-3.png') }}" alt=""></a>
                                </div>
                            </div>
                            <div class="col-xxl-6 col-xl-12 col-lg-6">
                                <div class="content-box">
                                    <div class="d-flex gap-16 mb-24">
                                        <div class="d-flex align-items-center gap-8">
                                            <img src="{{ asset('assets/media/icons/user-bk.png') }}" alt="">
                                            <p class="h6 dark-gray">Malisa John</p>
                                        </div>
                                        <div class="vr-line"></div>
                                        <div class="d-flex align-items-center gap-8">
                                            <img src="{{ asset('assets/media/icons/calender.png') }}" alt="">
                                            <p class="h6 dark-gray">08 Aug, 2023</p>
                                        </div>
                                    </div>
                                    <h5 class="lightest-black mb-8"><a href="{{ route('blog.detail') }}">Navigating
                                            Cultures: Cross-Cultural Encounters and Insights</a></h5>
                                    <p class="dark-gray mb-24">Lorem ipsum dolor sit amet consectetur. Feugiat sit
                                        eleifend
                                        tortor.</p>
                                    <a href="{{ route('blog.detail') }}" class="cus-btn small-pad">Read More</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xxl-6 col-xl-4 col-lg-12 col-md-6 col-sm-6">
                    <div class="blog-box bg-white light-shadow p-24 br-20">
                        <div class="row align-items-center row-gap-3">
                            <div class="col-xxl-6 col-xl-12 col-lg-6">
                                <div class="image-box">
                                    <a href="{{ route('blog.detail') }}"><img
                                            src="{{ asset('assets/media/blog/blog-2.png') }}" alt=""></a>
                                </div>
                            </div>
                            <div class="col-xxl-6 col-xl-12 col-lg-6">
                                <div class="content-box">
                                    <div class="d-flex gap-16 mb-24">
                                        <div class="d-flex align-items-center gap-8">
                                            <img src="{{ asset('assets/media/icons/user-bk.png') }}" alt="">
                                            <p class="h6 dark-gray">Malisa John</p>
                                        </div>
                                        <div class="vr-line"></div>
                                        <div class="d-flex align-items-center gap-8">
                                            <img src="{{ asset('assets/media/icons/calender.png') }}" alt="">
                                            <p class="h6 dark-gray">08 Aug, 2023</p>
                                        </div>
                                    </div>
                                    <h5 class="lightest-black mb-8"><a href="{{ route('blog.detail') }}">Urban
                                            Explorations: Navigating Cities and Urban Landscapes</a></h5>
                                    <p class="dark-gray mb-24">Lorem ipsum dolor sit amet consectetur. Feugiat sit
                                        eleifend
                                        tortor.</p>
                                    <a href="{{ route('blog.detail') }}" class="cus-btn small-pad">Read More</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xxl-6 col-xl-4 col-lg-12 col-md-6 col-sm-6 d-xxl-block d-xl-none">
                    <div class="blog-box bg-white light-shadow p-24 br-20">
                        <div class="row align-items-center row-gap-3">
                            <div class="col-xxl-6 col-xl-12 col-lg-6">
                                <div class="image-box">
                                    <a href="{{ route('blog.detail') }}"><img
                                            src="{{ asset('assets/media/blog/blog-4.png') }}" alt=""></a>
                                </div>
                            </div>
                            <div class="col-xxl-6 col-xl-12 col-lg-6">
                                <div class="content-box">
                                    <div class="d-flex gap-16 mb-24">
                                        <div class="d-flex align-items-center gap-8">
                                            <img src="{{ asset('assets/media/icons/user-bk.png') }}" alt="">
                                            <p class="h6 dark-gray">Malisa John</p>
                                        </div>
                                        <div class="vr-line"></div>
                                        <div class="d-flex align-items-center gap-8">
                                            <img src="{{ asset('assets/media/icons/calender.png') }}" alt="">
                                            <p class="h6 dark-gray">08 Aug, 2023</p>
                                        </div>
                                    </div>
                                    <h5 class="lightest-black mb-8"><a href="{{ route('blog.detail') }}">Wings of
                                            Adventure: Exploring the World by Air</a></h5>
                                    <p class="dark-gray mb-24">Lorem ipsum dolor sit amet consectetur. Feugiat sit
                                        eleifend
                                        tortor.</p>
                                    <a href="{{ route('blog.detail') }}" class="cus-btn small-pad">Read More</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Blog Area End -->
@endpush

@push('scripts')
    <!-- Flatpickr JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script>
        // Set base URL for API calls
        window.baseUrl = '{{ url('/') }}';
        window.apiBaseUrl = '{{ url('/') }}/index.php/api';

        // Alternative: Use Laravel route helper for more reliable URLs
        window.airportAutocompleteUrl = '{{ route('api.airports.autocomplete') }}';
        window.popularAirportsUrl = '{{ route('api.airports.popular') }}';

        $(document).ready(function() {
            // Disable conflicting jQuery UI autocomplete from app.js
            if (typeof $.fn.autocomplete !== 'undefined') {
                $('.airport-search-input').off('autocomplete');
            }

            // Set minimum date to today
            const today = new Date().toISOString().split('T')[0];
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            const tomorrowStr = tomorrow.toISOString().split('T')[0];

            // Set dates for both visible and hidden fields
            $('#flightDep').attr('min', today).val(today);
            $('#flightTime').attr('min', today).val(tomorrowStr);
            $('#departure_date').val(today);
            $('#return_date').val(tomorrowStr);

            // Sync form fields when they change
            $('#from').on('change', function() {
                $('#departure_airport_code').val($(this).val());
            });

            $('#to').on('change', function() {
                $('#arrival_airport_code').val($(this).val());
            });

            $('#flightDep').on('change', function() {
                $('#departure_date').val($(this).val());
            });

            $('#flightTime').on('change', function() {
                $('#return_date').val($(this).val());
            });

            $('#adult').on('change', function() {
                $('#adults').val($(this).val());
            });

            $('#child').on('change', function() {
                $('#children').val($(this).val());
            });

            $('#infant').on('change', function() {
                $('#infants').val($(this).val());
            });

            // Update cabin class when radio buttons change
            $('input[name="class"]').on('change', function() {
                let cabinClass = 'economy';
                if ($('#business').is(':checked')) cabinClass = 'business';
                if ($('#firstClass').is(':checked')) cabinClass = 'first';
                if ($('#pre-eco').is(':checked')) cabinClass = 'premium_economy';
                $('#cabin_class').val(cabinClass);
            });

            // Debug: Log API URLs
            console.log('Base URL:', window.baseUrl);
            console.log('API Base URL:', window.apiBaseUrl);

            // Airport Autocomplete Functionality
            initializeAirportAutocomplete();

            // Update status indicator
            updateStatus('✅', 'Airport autocomplete ready! Type in the From/To fields above.');

            // Handle trip type change
            $('input[name="trip_type"]').change(function() {
                if ($(this).val() === 'oneway') {
                    $('#return_date_group').hide();
                    $('#return_date').removeAttr('required');
                } else {
                    $('#return_date_group').show();
                    $('#return_date').attr('required', true);
                }
            });

            // Swap airports functionality
            $('#swapAirports').click(function() {
                const departureValue = $('#departure_airport').val();
                const departureCode = $('#departure_airport_code').val();
                const arrivalValue = $('#arrival_airport').val();
                const arrivalCode = $('#arrival_airport_code').val();

                $('#departure_airport').val(arrivalValue);
                $('#departure_airport_code').val(arrivalCode);
                $('#arrival_airport').val(departureValue);
                $('#arrival_airport_code').val(departureCode);

                // Add animation effect
                $(this).addClass('rotate');
                setTimeout(() => {
                    $(this).removeClass('rotate');
                }, 300);
            });

            // Days counter functionality
            $('#increaseDays').click(function() {
                const currentVal = parseInt($('#stopover_days').val()) || 0;
                if (currentVal < 30) {
                    $('#stopover_days').val(currentVal + 1);
                }
            });

            $('#decreaseDays').click(function() {
                const currentVal = parseInt($('#stopover_days').val()) || 0;
                if (currentVal > 0) {
                    $('#stopover_days').val(currentVal - 1);
                }
            });

            // Update passenger summary
            function updatePassengerSummary() {
                const adults = parseInt($('#adults').val()) || 0;
                const children = parseInt($('#children').val()) || 0;
                const infants = parseInt($('#infants').val()) || 0;
                const cabinClass = $('#cabin_class option:selected').text();

                const totalPassengers = adults + children + infants;
                let summary = `${totalPassengers} Passenger${totalPassengers > 1 ? 's' : ''} / ${cabinClass}`;

                $('#passenger-summary').text(summary);
            }

            // Initialize passenger summary
            updatePassengerSummary();

            $('#adults, #children, #infants, #cabin_class').change(updatePassengerSummary);

            // Airport search functionality
            let searchTimeout;
            $('.airport-search').on('input', function() {
                const input = $(this);
                const query = input.val();
                const suggestionsDiv = input.closest('.form-group').find('.airport-suggestions');

                clearTimeout(searchTimeout);

                if (query.length < 2) {
                    suggestionsDiv.hide();
                    return;
                }

                searchTimeout = setTimeout(function() {
                    $.ajax({
                        url: '{{ route('flight-search.airports') }}',
                        method: 'GET',
                        data: {
                            query: query
                        },
                        success: function(response) {
                            if (response.success && response.data.length > 0) {
                                let html = '';
                                response.data.forEach(function(airport) {
                                    html += `<div class="airport-suggestion" data-code="${airport.code}" data-name="${airport.name}" data-city="${airport.city}">
                                        <strong>${airport.code}</strong> - ${airport.name}<br>
                                        <small class="text-muted">${airport.city}, ${airport.country}</small>
                                     </div>`;
                                });
                                suggestionsDiv.html(html).show();
                            } else {
                                suggestionsDiv.hide();
                            }
                        },
                        error: function() {
                            suggestionsDiv.hide();
                        }
                    });
                }, 300);
            });

            // Handle airport selection
            $(document).on('click', '.airport-suggestion', function() {
                const suggestion = $(this);
                const code = suggestion.data('code');
                const name = suggestion.data('name');
                const city = suggestion.data('city');

                const input = suggestion.closest('.form-group').find('.airport-search');
                const hiddenInput = suggestion.closest('.form-group').find('input[type="hidden"]');

                input.val(`${code} - ${city}`);
                hiddenInput.val(code);
                suggestion.closest('.airport-suggestions').hide();
            });

            // Hide suggestions when clicking outside
            $(document).click(function(e) {
                if (!$(e.target).closest('.form-group').length) {
                    $('.airport-suggestions').hide();
                }
            });

            // Test Airport Autocomplete function
            window.testAirportAutocomplete = function() {
                console.log('Testing Airport Autocomplete...');

                // Test airport autocomplete search
                $.ajax({
                    url: window.airportAutocompleteUrl,
                    method: 'GET',
                    data: {
                        query: 'new',
                        limit: 5
                    },
                    success: function(response) {
                        console.log('Airport autocomplete success:', response);
                        if (response.success && response.data.length > 0) {
                            let message =
                                `Airport Autocomplete is working! Found ${response.data.length} airports:\n\n`;
                            response.data.forEach(function(airport) {
                                message +=
                                    `${airport.code} - ${airport.name}\n${airport.city}, ${airport.country}\n\n`;
                            });
                            alert(message);
                        } else {
                            alert('Airport autocomplete returned no results');
                        }
                    },
                    error: function(xhr) {
                        console.error('Airport autocomplete error:', xhr);
                        const errorMsg = xhr.responseJSON ? xhr.responseJSON.message :
                            'Unknown error';
                        alert('Airport autocomplete failed: ' + errorMsg);
                    }
                });

                // Also test popular airports
                $.ajax({
                    url: window.popularAirportsUrl,
                    method: 'GET',
                    success: function(response) {
                        console.log('Popular airports success:', response);
                    },
                    error: function(xhr) {
                        console.error('Popular airports error:', xhr);
                    }
                });
            };

            // Test API function (for debugging)
            window.testFlightAPI = function() {
                console.log('Testing Flight API...');

                // Test flight search directly with mock data
                const testData = {
                    departure_airport: 'JFK',
                    arrival_airport: 'LAX',
                    departure_date: '2024-01-15',
                    adults: 1,
                    cabin_class: 'economy',
                    _token: $('input[name="_token"]').val()
                };

                $.ajax({
                    url: '/flynow/public/index.php/flight-search/search',
                    method: 'POST',
                    data: testData,
                    success: function(response) {
                        console.log('Flight search success:', response);
                        if (response.success) {
                            displayFlightResults(response.data);
                            $('#flightSearchResults').show();
                            alert(
                                `Flight API is working! Found ${response.data.length} flights. Check the results below.`
                                );
                        } else {
                            alert('Flight search returned no results: ' + response.message);
                        }
                    },
                    error: function(xhr) {
                        console.error('Flight search error:', xhr);
                        const errorMsg = xhr.responseJSON ? xhr.responseJSON.message :
                            'Unknown error';
                        alert('Flight search failed: ' + errorMsg);
                    }
                });

                // Also test airport search
                $.ajax({
                    url: '/flynow/public/index.php/flight-search/airports',
                    method: 'GET',
                    data: {
                        query: 'new'
                    },
                    success: function(response) {
                        console.log('Airport search success:', response);
                    },
                    error: function(xhr) {
                        console.error('Airport search error:', xhr);
                    }
                });
            };

            // Handle form submission
            $('#flightSearchForm').submit(function(e) {
                e.preventDefault();

                // Sync visible fields to hidden fields before submission
                $('#departure_airport_code').val($('#from').val() || 'JFK');
                $('#arrival_airport_code').val($('#to').val() || 'LAX');
                $('#departure_date').val($('#flightDep').val() || '2024-01-15');
                $('#return_date').val($('#flightTime').val() || '2024-01-22');
                $('#adults').val($('#adult').val() || '1');
                $('#children').val($('#child').val() || '0');
                $('#infants').val($('#infant').val() || '0');

                // Get selected class
                let cabinClass = 'economy';
                if ($('#business').is(':checked')) cabinClass = 'business';
                if ($('#firstClass').is(':checked')) cabinClass = 'first';
                if ($('#pre-eco').is(':checked')) cabinClass = 'premium_economy';
                $('#cabin_class').val(cabinClass);

                const formData = {
                    departure_airport: $('#departure_airport_code').val(),
                    arrival_airport: $('#arrival_airport_code').val(),
                    departure_date: $('#departure_date').val(),
                    return_date: $('#return_date').val(),
                    adults: $('#adults').val(),
                    children: $('#children').val(),
                    infants: $('#infants').val(),
                    cabin_class: $('#cabin_class').val(),
                    _token: $('input[name="_token"]').val()
                };

                // Validation
                if (!formData.departure_airport) {
                    alert('Please select a departure airport');
                    return;
                }

                if (!formData.arrival_airport) {
                    alert('Please select an arrival airport');
                    return;
                }

                if (!formData.departure_date) {
                    alert('Please select a departure date');
                    return;
                }

                if ($('input[name="trip_type"]:checked').val() === 'roundtrip' && !formData.return_date) {
                    alert('Please select a return date for round trip');
                    return;
                }

                // Show loading
                const searchBtn = $('#searchFlightsBtn');
                const originalText = searchBtn.html();
                searchBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>Searching...').prop('disabled',
                    true);

                // Make API call
                $.ajax({
                    url: '{{ route('flight-search.search') }}',
                    method: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response.success) {
                            displayFlightResults(response.data);
                            $('#flightSearchResults').show();
                            $('html, body').animate({
                                scrollTop: $('#flightSearchResults').offset().top - 100
                            }, 500);
                        } else {
                            alert('Search failed: ' + response.message);
                        }
                    },
                    error: function(xhr) {
                        const response = xhr.responseJSON;
                        alert('Search failed: ' + (response?.message || 'Please try again'));
                    },
                    complete: function() {
                        searchBtn.html(originalText).prop('disabled', false);
                    }
                });
            });

            // Display flight results
            function displayFlightResults(flights) {
                let html = '';

                if (flights.length === 0) {
                    html =
                        '<div class="alert alert-info">No flights found for your search criteria. Please try different dates or airports.</div>';
                } else {
                    flights.forEach(function(flight) {
                        html += `
                    <div class="flight-result-card">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <img src="${flight.airline_logo || '/assets/media/icons/airline-default.png'}"
                                     alt="${flight.airline}" class="img-fluid" style="max-height: 40px;">
                                <div class="small text-muted">${flight.airline}</div>
                            </div>
                            <div class="col-md-3">
                                <div class="fw-bold">${flight.departure_time}</div>
                                <div class="text-muted">${flight.departure_airport}</div>
                            </div>
                            <div class="col-md-2 text-center">
                                <div class="small text-muted">${flight.duration}</div>
                                <div class="text-muted">
                                    ${flight.stops === 0 ? 'Direct' : flight.stops + ' Stop' + (flight.stops > 1 ? 's' : '')}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="fw-bold">${flight.arrival_time}</div>
                                <div class="text-muted">${flight.arrival_airport}</div>
                            </div>
                            <div class="col-md-2 text-end">
                                <div class="price-highlight">$${flight.price}</div>
                                <button class="btn btn-primary btn-sm mt-2" onclick="selectFlight('${flight.id}')">
                                    Select
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                    });
                }

                $('#searchResultsContent').html(html);
            }

            // Handle flight selection
            window.selectFlight = function(flightId) {
                // Redirect to booking page or handle selection
                window.location.href = '{{ route('flights.booking') }}?flight=' + flightId;
            };

            // Airport Autocomplete Functionality
            function initializeAirportAutocomplete() {
                let searchTimeouts = {};
                let selectedIndex = -1;
                let currentSuggestions = [];

                // Initialize autocomplete for both fields
                $('.airport-search-input').each(function() {
                    const input = $(this);
                    const inputId = input.attr('id');
                    const suggestionsContainer = input.siblings('.airport-suggestions');

                    // Input event with debouncing
                    input.on('input', function() {
                        const query = $(this).val().trim();

                        // Clear previous timeout
                        if (searchTimeouts[inputId]) {
                            clearTimeout(searchTimeouts[inputId]);
                        }

                        if (query.length < 1) {
                            hideSuggestions(suggestionsContainer);
                            return;
                        }

                        // Show loading
                        showLoading(suggestionsContainer);

                        // Debounce search (300ms)
                        searchTimeouts[inputId] = setTimeout(function() {
                            searchAirports(query, suggestionsContainer, input);
                        }, 300);
                    });

                    // Keyboard navigation
                    input.on('keydown', function(e) {
                        const suggestions = suggestionsContainer.find('.airport-suggestion');

                        switch (e.keyCode) {
                            case 38: // Arrow Up
                                e.preventDefault();
                                selectedIndex = Math.max(-1, selectedIndex - 1);
                                updateSelection(suggestions);
                                break;

                            case 40: // Arrow Down
                                e.preventDefault();
                                selectedIndex = Math.min(suggestions.length - 1, selectedIndex + 1);
                                updateSelection(suggestions);
                                break;

                            case 13: // Enter
                                e.preventDefault();
                                if (selectedIndex >= 0 && suggestions.eq(selectedIndex).length) {
                                    selectAirport(suggestions.eq(selectedIndex), input,
                                        suggestionsContainer);
                                }
                                break;

                            case 27: // Escape
                                hideSuggestions(suggestionsContainer);
                                selectedIndex = -1;
                                break;
                        }
                    });

                    // Focus event - show popular airports if empty
                    input.on('focus', function() {
                        if ($(this).val().trim() === '') {
                            loadPopularAirports(suggestionsContainer, input);
                        }
                    });

                    // Click outside to hide
                    $(document).on('click', function(e) {
                        if (!$(e.target).closest('.airport-autocomplete').length) {
                            hideSuggestions(suggestionsContainer);
                            selectedIndex = -1;
                        }
                    });
                });

                // Search airports function
                function searchAirports(query, container, input) {
                    const apiUrl = window.airportAutocompleteUrl;
                    console.log('Searching airports with URL:', apiUrl, 'Query:', query);

                    $.ajax({
                        url: apiUrl,
                        method: 'GET',
                        data: {
                            query: query,
                            limit: 8
                        },
                        timeout: 3000,
                        success: function(response) {
                            if (response.success && response.data.length > 0) {
                                showSuggestions(response.data, container, input, query);
                            } else {
                                showNoResults(container);
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('Airport search failed:', error);
                            showError(container);
                        }
                    });
                }

                // Load popular airports
                function loadPopularAirports(container, input) {
                    $.ajax({
                        url: window.popularAirportsUrl,
                        method: 'GET',
                        timeout: 3000,
                        success: function(response) {
                            if (response.success && response.data.length > 0) {
                                showSuggestions(response.data, container, input, '',
                                    'Popular Airports');
                            }
                        },
                        error: function() {
                            // Silently fail for popular airports
                        }
                    });
                }

                // Show suggestions
                function showSuggestions(airports, container, input, query = '', title = '') {
                    let html = '';

                    if (title) {
                        html +=
                            `<div class="autocomplete-title" style="padding: 8px 16px; background: #f8f9fa; font-weight: 600; font-size: 12px; color: #6c757d; border-bottom: 1px solid #e9ecef;">${title}</div>`;
                    }

                    airports.forEach(function(airport) {
                        const displayText = highlightMatch(airport.display, query);
                        const locationText = highlightMatch(airport.subtitle, query);

                        html += `
                            <div class="airport-suggestion" data-code="${airport.code}" data-name="${airport.name}" data-city="${airport.city}" data-country="${airport.country}">
                                <div class="airport-code">${airport.code}</div>
                                <div class="airport-name">${displayText}</div>
                                <div class="airport-location">${locationText}</div>
                            </div>
                        `;
                    });

                    container.html(html).show();
                    selectedIndex = -1;

                    // Click handlers for suggestions
                    container.find('.airport-suggestion').on('click', function() {
                        selectAirport($(this), input, container);
                    });
                }

                // Highlight matching text
                function highlightMatch(text, query) {
                    if (!query) return text;

                    const regex = new RegExp(`(${escapeRegex(query)})`, 'gi');
                    return text.replace(regex, '<span class="highlight">$1</span>');
                }

                // Escape regex special characters
                function escapeRegex(string) {
                    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                }

                // Select airport
                function selectAirport(suggestion, input, container) {
                    const code = suggestion.data('code');
                    const name = suggestion.data('name');
                    const city = suggestion.data('city');
                    const country = suggestion.data('country');

                    // Set input value
                    input.val(`${code} - ${name}`);

                    // Update hidden fields
                    const target = input.data('target');
                    if (target === 'departure') {
                        $('#departure_airport_code').val(code);
                        $('#from').val(code);
                    } else if (target === 'arrival') {
                        $('#arrival_airport_code').val(code);
                        $('#to').val(code);
                    }

                    // Hide suggestions
                    hideSuggestions(container);
                    selectedIndex = -1;

                    // Trigger change event
                    input.trigger('change');
                }

                // Update selection highlighting
                function updateSelection(suggestions) {
                    suggestions.removeClass('active');
                    if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
                        suggestions.eq(selectedIndex).addClass('active');
                    }
                }

                // Show loading state
                function showLoading(container) {
                    container.html(
                        '<div class="autocomplete-loading"><i class="fas fa-spinner fa-spin me-2"></i>Searching airports...</div>'
                    ).show();
                }

                // Show no results
                function showNoResults(container) {
                    container.html('<div class="autocomplete-no-results">No airports found</div>').show();
                }

                // Show error
                function showError(container) {
                    container.html(
                        '<div class="autocomplete-no-results text-danger">Search failed. Please try again.</div>'
                    ).show();
                }

                // Hide suggestions
                function hideSuggestions(container) {
                    container.hide().empty();
                }
            }

            // Update status indicator
            function updateStatus(icon, text) {
                $('#statusIndicator').text(icon);
                $('#statusText').text(text);
            }

            // Load more flight deals
            window.loadMoreDeals = function() {
                const container = $('#flightDealsContainer');
                const moreDeals = [{
                        image: 'flight-1.png',
                        route: 'London to Paris',
                        dates: 'Jan 25, 2024 - Feb 01 2024',
                        class: 'Economy Class',
                        price: '$180'
                    },
                    {
                        image: 'flight-2.png',
                        route: 'New York to Tokyo',
                        dates: 'Feb 05, 2024 - Feb 12 2024',
                        class: 'Business Class',
                        price: '$2400'
                    },
                    {
                        image: 'flight-3.png',
                        route: 'Sydney to Singapore',
                        dates: 'Feb 15, 2024 - Feb 22 2024',
                        class: 'Premium Class',
                        price: '$650'
                    },
                    {
                        image: 'flight-4.png',
                        route: 'Mumbai to Dubai',
                        dates: 'Mar 01, 2024 - Mar 08 2024',
                        class: 'Economy Class',
                        price: '$320'
                    }
                ];

                moreDeals.forEach(deal => {
                    const dealHtml = `
                        <div class="col-xl-3 col-lg-4 col-md-6">
                            <div class="flight-deal-block bg-white p-24 h-100 shadow-sm">
                                <div class="image-box mb-24">
                                    <a href="{{ route('flights.booking') }}">
                                        <img src="{{ asset('assets/media/images/') }}${deal.image}" alt="${deal.route}" class="img-fluid rounded">
                                    </a>
                                </div>
                                <div class="content-box">
                                    <h5 class="color-black mb-8">
                                        <a href="{{ route('flights.booking') }}" class="text-decoration-none">${deal.route}</a>
                                    </h5>
                                    <p class="light-black mb-24">
                                        <i class="fas fa-calendar-alt me-2"></i>${deal.dates}
                                    </p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="price">
                                            <h6 class="light-black mb-8 fw-500">${deal.class}</h6>
                                            <h5 class="lightest-black fw-500">${deal.price}</h5>
                                        </div>
                                        <a href="{{ route('flights.booking') }}" class="cus-btn small-pad">Book Now</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    container.append(dealHtml);
                });

                // Hide the load more button after loading
                $(event.target).hide();
            };
        });

        // Flight Datatable Functionality
        $(document).ready(function() {
            let currentSort = null;
            let sortDirection = 'asc';
            let departureDatePicker = null;

            // Initialize Professional Date Picker
            function initializeDatePicker() {
                departureDatePicker = flatpickr("#departureDate", {
                    dateFormat: "Y-m-d",
                    altInput: true,
                    altFormat: "d M Y",
                    placeholder: "Select departure date",
                    minDate: "today",
                    maxDate: new Date().fp_incr(365), // 1 year from today
                    allowInput: false,
                    clickOpens: true,
                    theme: "light",
                    position: "auto",
                    defaultDate: null,
                    enableTime: false,
                    weekNumbers: false,
                    showMonths: 1,
                    static: false,
                    appendTo: document.body,
                    onChange: function(selectedDates, dateStr, instance) {
                        // Show/hide clear button
                        if (dateStr) {
                            $('#clearDepartureDate').show();
                            // Add visual feedback to input
                            $('#departureDate').addClass('has-value');
                        } else {
                            $('#clearDepartureDate').hide();
                            $('#departureDate').removeClass('has-value');
                        }

                        // Trigger filtering with slight delay
                        setTimeout(function() {
                            applyAllFilters();
                        }, 100);
                    },
                    onOpen: function(selectedDates, dateStr, instance) {
                        // Add custom class for styling
                        instance.calendarContainer.classList.add('professional-datepicker');

                        // Add animation class
                        setTimeout(() => {
                            instance.calendarContainer.classList.add('calendar-open');
                        }, 10);
                    },
                    onClose: function(selectedDates, dateStr, instance) {
                        // Remove animation class
                        instance.calendarContainer.classList.remove('calendar-open');
                    },
                    locale: {
                        firstDayOfWeek: 1, // Monday
                        weekdays: {
                            shorthand: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
                            longhand: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
                        },
                        months: {
                            shorthand: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                            longhand: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December']
                        }
                    }
                });
            }

            // Clear date picker functionality
            $('#clearDepartureDate').on('click', function(e) {
                e.stopPropagation();
                if (departureDatePicker) {
                    departureDatePicker.clear();
                }
                $(this).hide();
                applyAllFilters();
            });

            // Initialize date picker
            initializeDatePicker();

            // Enhanced search functionality for flight cards
            $('#flightSearch').on('keyup input', function() {
                const searchTerm = $(this).val().toLowerCase();

                // Show/hide clear button
                if (searchTerm.length > 0) {
                    $('#clearSearch').show();
                } else {
                    $('#clearSearch').hide();
                }

                $('.flight-card').each(function() {
                    const cardText = $(this).text().toLowerCase();
                    if (cardText.includes(searchTerm)) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });

                updateFlightCount();
            });

            // Clear search functionality
            $('#clearSearch').on('click', function() {
                $('#flightSearch').val('').trigger('input');
                $(this).hide();
            });



            // Sorting functionality
            $('.sort-option').on('click', function(e) {
                e.preventDefault();
                const sortBy = $(this).data('sort');

                if (currentSort === sortBy) {
                    sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
                } else {
                    sortDirection = 'asc';
                    currentSort = sortBy;
                }

                sortFlights(sortBy, sortDirection);
                $('#sortDropdown .sort-text').text(`${$(this).text()} (${sortDirection.toUpperCase()})`);
            });

            function sortFlights(sortBy, direction) {
                const container = $('#flightCardsContainer');
                const cards = container.find('.flight-card').toArray();

                cards.sort(function(a, b) {
                    let aVal, bVal;

                    switch (sortBy) {
                        case 'date':
                            aVal = new Date($(a).find('.flight-date').text().trim());
                            bVal = new Date($(b).find('.flight-date').text().trim());
                            break;
                        case 'price':
                            aVal = parseInt($(a).find('.price').text().replace(/[^\d]/g, '')) || 0;
                            bVal = parseInt($(b).find('.price').text().replace(/[^\d]/g, '')) || 0;
                            break;
                        case 'airline':
                            aVal = $(a).find('.airline-badge').text().toLowerCase();
                            bVal = $(b).find('.airline-badge').text().toLowerCase();
                            break;
                        case 'seats':
                            aVal = parseInt($(a).find('.service-item:contains("seats")').text().replace(/[^\d]/g, '')) || 0;
                            bVal = parseInt($(b).find('.service-item:contains("seats")').text().replace(/[^\d]/g, '')) || 0;
                            break;
                        default:
                            return 0;
                    }

                    if (direction === 'asc') {
                        return aVal > bVal ? 1 : -1;
                    } else {
                        return aVal < bVal ? 1 : -1;
                    }
                });

                container.empty().append(cards);
            }

            // Apply filter functionality
            $('#applyTableFilter').on('click', function() {
                applyAllFilters();
            });

            // Real-time filtering when filter values change (excluding date picker)
            $('#airlineFilter, #sectorFilter, #filterMeal, #filterMinSeats, #filterMaxPrice').on(
                'change input',
                function() {
                    // Add a small delay for input fields to avoid excessive filtering
                    clearTimeout(window.filterTimeout);
                    window.filterTimeout = setTimeout(function() {
                        applyAllFilters();
                    }, 300);
                });

            function applyAllFilters() {
                const departureDate = $('#departureDate').val();
                const airline = $('#airlineFilter').val();
                const sector = $('#sectorFilter').val();
                const meal = $('#filterMeal').val();
                const minSeats = parseInt($('#filterMinSeats').val()) || 0;
                const maxPrice = parseInt($('#filterMaxPrice').val()) || Infinity;

                $('.flight-card').each(function() {
                    let show = true;

                    // Filter by departure date
                    if (departureDate) {
                        const cardDate = $(this).find('.flight-date').text().trim();
                        const formattedDate = new Date(departureDate).toLocaleDateString('en-GB', {
                            day: '2-digit',
                            month: 'short',
                            year: 'numeric'
                        });
                        if (!cardDate.includes(formattedDate.replace(/,/g, ''))) {
                            show = false;
                        }
                    }

                    // Filter by airline
                    if (airline) {
                        const cardAirline = $(this).find('.airline-badge').text().toLowerCase();
                        if (!cardAirline.includes(airline.toLowerCase())) {
                            show = false;
                        }
                    }

                    // Filter by sector (domestic/international)
                    if (sector) {
                        const route = $(this).find('.route-text').text();
                        const isDomestic = route.includes('Islamabad') || route.includes('Peshawar');
                        if ((sector === 'domestic' && !isDomestic) || (sector === 'international' &&
                                isDomestic)) {
                            show = false;
                        }
                    }

                    // Filter by meal service
                    if (meal) {
                        const mealBadge = $(this).find('.meal-badge').text().toLowerCase();
                        if ((meal === 'yes' && mealBadge.includes('no meal')) ||
                            (meal === 'no' && mealBadge.includes('with meal'))) {
                            show = false;
                        }
                    }

                    // Filter by minimum seats
                    if (minSeats > 0) {
                        const availableSeats = parseInt($(this).find('.service-item:contains("seats")').text().replace(/[^\d]/g, '')) || 0;
                        if (availableSeats < minSeats) {
                            show = false;
                        }
                    }

                    // Filter by maximum price
                    if (maxPrice < Infinity) {
                        const priceText = $(this).find('.price').text();
                        const price = parseInt(priceText.replace(/[^\d]/g, '')) || 0;
                        if (price > maxPrice) {
                            show = false;
                        }
                    }

                    if (show) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });

                updateFlightCount();
            }



            // Clear all filters
            $('#clearAllFilters').on('click', function() {
                // Clear date picker
                if (departureDatePicker) {
                    departureDatePicker.clear();
                }
                $('#clearDepartureDate').hide();

                // Clear all other filter inputs
                $('#airlineFilter').val('');
                $('#sectorFilter').val('');
                $('#filterMeal').val('');
                $('#filterMinSeats').val('');
                $('#filterMaxPrice').val('');
                $('#flightSearch').val('');

                // Show all flight cards
                $('.flight-card').show();

                // Reset sort
                currentSort = null;
                sortDirection = 'asc';
                $('#sortDropdown .sort-text').text('Sort By');

                updateFlightCount();

                // Add visual feedback
                $(this).addClass('btn-success').find('i').removeClass('fa-times').addClass('fa-check');
                setTimeout(() => {
                    $(this).removeClass('btn-success').find('i').removeClass('fa-check').addClass(
                        'fa-times');
                }, 1000);
            });



            // Function to update flight count
            function updateFlightCount() {
                const visibleFlights = $('.flight-card:visible').length;
                const totalFlights = $('.flight-card').length;
                $('.count-text').text(`Showing ${visibleFlights} of ${totalFlights} flights`);
            }

            // Initialize flight count
            updateFlightCount();

            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Focus search input for better UX
            setTimeout(function() {
                $('#flightSearch').focus();
            }, 500);

            // Flight card action functions
            window.bookFlight = function(flightNumber) {
                // Redirect to booking page with flight details
                window.location.href = `{{ route('flights.booking') }}?flight_id=${flightNumber}`;
            };

            window.showFlightDetails = function(flightNumber) {
                // Show flight details modal or expand card
                alert(`Flight details for ${flightNumber} - Feature coming soon!`);
            };
        });
    </script>
@endpush
