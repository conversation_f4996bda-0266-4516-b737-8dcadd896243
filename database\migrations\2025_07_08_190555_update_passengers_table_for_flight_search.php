<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('passengers', function (Blueprint $table) {
            // Add new columns for flight search functionality
            $table->date('passport_expiry')->nullable()->after('passport_number');
            $table->string('type', 20)->nullable()->after('passenger_type');
            $table->string('special_assistance')->nullable()->after('special_needs');
            $table->string('meal_preference', 50)->nullable()->after('special_assistance');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('passengers', function (Blueprint $table) {
            $table->dropColumn([
                'passport_expiry',
                'type',
                'special_assistance',
                'meal_preference'
            ]);
        });
    }
};
