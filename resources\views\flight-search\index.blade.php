@extends('layouts.app')

@section('title', 'Search Flights - {{ config('app.name', 'FlyNow Airlines') }}')

@section('content')
<div class="flight-search-page">
    <!-- Hero Section with Search Form -->
    <section class="hero-search bg-gradient-primary py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="text-center text-white mb-4">
                        <h1 class="display-4 fw-bold mb-3">Find Your Perfect Flight</h1>
                        <p class="lead">Search and compare flights from multiple airlines worldwide</p>
                    </div>
                    
                    <!-- Search Form -->
                    <div class="card shadow-lg border-0">
                        <div class="card-body p-4">
                            <form id="flightSearchForm">
                                <!-- Trip Type -->
                                <div class="row mb-3">
                                    <div class="col-12">
                                        <div class="btn-group" role="group" aria-label="Trip type">
                                            <input type="radio" class="btn-check" name="trip_type" id="roundtrip" value="roundtrip" checked>
                                            <label class="btn btn-outline-primary" for="roundtrip">
                                                <i class="fas fa-exchange-alt me-1"></i>Round Trip
                                            </label>
                                            
                                            <input type="radio" class="btn-check" name="trip_type" id="oneway" value="oneway">
                                            <label class="btn btn-outline-primary" for="oneway">
                                                <i class="fas fa-arrow-right me-1"></i>One Way
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Airports and Dates -->
                                <div class="row g-3 mb-3">
                                    <div class="col-md-3">
                                        <label for="departure_airport" class="form-label">From</label>
                                        <div class="position-relative">
                                            <input type="text" class="form-control airport-input" id="departure_airport" 
                                                   name="departure_airport" placeholder="Departure city or airport" required>
                                            <div class="airport-suggestions" id="departure_suggestions"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <label for="arrival_airport" class="form-label">To</label>
                                        <div class="position-relative">
                                            <input type="text" class="form-control airport-input" id="arrival_airport" 
                                                   name="arrival_airport" placeholder="Destination city or airport" required>
                                            <div class="airport-suggestions" id="arrival_suggestions"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <label for="departure_date" class="form-label">Departure</label>
                                        <input type="date" class="form-control" id="departure_date" 
                                               name="departure_date" required>
                                    </div>
                                    
                                    <div class="col-md-3" id="return_date_container">
                                        <label for="return_date" class="form-label">Return</label>
                                        <input type="date" class="form-control" id="return_date" 
                                               name="return_date">
                                    </div>
                                </div>

                                <!-- Passengers and Class -->
                                <div class="row g-3 mb-4">
                                    <div class="col-md-3">
                                        <label for="adults" class="form-label">Adults</label>
                                        <select class="form-select" id="adults" name="adults" required>
                                            <option value="1" selected>1 Adult</option>
                                            <option value="2">2 Adults</option>
                                            <option value="3">3 Adults</option>
                                            <option value="4">4 Adults</option>
                                            <option value="5">5 Adults</option>
                                            <option value="6">6 Adults</option>
                                            <option value="7">7 Adults</option>
                                            <option value="8">8 Adults</option>
                                            <option value="9">9 Adults</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <label for="children" class="form-label">Children (2-11)</label>
                                        <select class="form-select" id="children" name="children">
                                            <option value="0" selected>0 Children</option>
                                            <option value="1">1 Child</option>
                                            <option value="2">2 Children</option>
                                            <option value="3">3 Children</option>
                                            <option value="4">4 Children</option>
                                            <option value="5">5 Children</option>
                                            <option value="6">6 Children</option>
                                            <option value="7">7 Children</option>
                                            <option value="8">8 Children</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <label for="infants" class="form-label">Infants (0-2)</label>
                                        <select class="form-select" id="infants" name="infants">
                                            <option value="0" selected>0 Infants</option>
                                            <option value="1">1 Infant</option>
                                            <option value="2">2 Infants</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <label for="cabin_class" class="form-label">Class</label>
                                        <select class="form-select" id="cabin_class" name="cabin_class" required>
                                            <option value="economy" selected>Economy</option>
                                            <option value="premium_economy">Premium Economy</option>
                                            <option value="business">Business</option>
                                            <option value="first">First Class</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Advanced Options (Collapsible) -->
                                <div class="mb-3">
                                    <button class="btn btn-link p-0 text-decoration-none" type="button" 
                                            data-bs-toggle="collapse" data-bs-target="#advancedOptions">
                                        <i class="fas fa-cog me-1"></i>Advanced Options
                                        <i class="fas fa-chevron-down ms-1"></i>
                                    </button>
                                </div>

                                <div class="collapse" id="advancedOptions">
                                    <div class="row g-3 mb-3">
                                        <div class="col-md-4">
                                            <label for="max_price" class="form-label">Max Price ($)</label>
                                            <input type="number" class="form-control" id="max_price" 
                                                   name="max_price" min="0" step="10" placeholder="Any price">
                                        </div>
                                        
                                        <div class="col-md-4">
                                            <label for="max_results" class="form-label">Max Results</label>
                                            <select class="form-select" id="max_results" name="max_results">
                                                <option value="20" selected>20 Results</option>
                                                <option value="30">30 Results</option>
                                                <option value="50">50 Results</option>
                                            </select>
                                        </div>
                                        
                                        <div class="col-md-4">
                                            <div class="form-check mt-4">
                                                <input class="form-check-input" type="checkbox" id="direct_flights_only" 
                                                       name="direct_flights_only">
                                                <label class="form-check-label" for="direct_flights_only">
                                                    Direct flights only
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Search Button -->
                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary btn-lg px-5" id="searchBtn">
                                        <i class="fas fa-search me-2"></i>Search Flights
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Search Results Section -->
    <section class="search-results py-5" id="searchResults" style="display: none;">
        <div class="container">
            <div class="row">
                <div class="col-lg-3">
                    <!-- Filters Sidebar -->
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="mb-0">Filter Results</h5>
                        </div>
                        <div class="card-body">
                            <!-- Price Range Filter -->
                            <div class="mb-4">
                                <h6>Price Range</h6>
                                <div class="range-slider">
                                    <input type="range" class="form-range" id="priceRange" min="0" max="2000" step="50">
                                    <div class="d-flex justify-content-between">
                                        <small>$0</small>
                                        <small id="priceValue">$2000+</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Airlines Filter -->
                            <div class="mb-4">
                                <h6>Airlines</h6>
                                <div id="airlinesFilter">
                                    <!-- Dynamic airline checkboxes will be added here -->
                                </div>
                            </div>

                            <!-- Stops Filter -->
                            <div class="mb-4">
                                <h6>Stops</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="nonstop" value="0">
                                    <label class="form-check-label" for="nonstop">Non-stop</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="onestop" value="1">
                                    <label class="form-check-label" for="onestop">1 Stop</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="twostops" value="2">
                                    <label class="form-check-label" for="twostops">2+ Stops</label>
                                </div>
                            </div>

                            <!-- Departure Time Filter -->
                            <div class="mb-4">
                                <h6>Departure Time</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="morning" value="morning">
                                    <label class="form-check-label" for="morning">Morning (6AM - 12PM)</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="afternoon" value="afternoon">
                                    <label class="form-check-label" for="afternoon">Afternoon (12PM - 6PM)</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="evening" value="evening">
                                    <label class="form-check-label" for="evening">Evening (6PM - 12AM)</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="night" value="night">
                                    <label class="form-check-label" for="night">Night (12AM - 6AM)</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-9">
                    <!-- Results Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h4 id="resultsCount">0 flights found</h4>
                            <p class="text-muted mb-0" id="searchSummary"></p>
                        </div>
                        <div>
                            <select class="form-select" id="sortBy">
                                <option value="price">Sort by Price</option>
                                <option value="duration">Sort by Duration</option>
                                <option value="departure">Sort by Departure Time</option>
                                <option value="arrival">Sort by Arrival Time</option>
                            </select>
                        </div>
                    </div>

                    <!-- Loading Spinner -->
                    <div class="text-center py-5" id="loadingSpinner" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Searching flights...</span>
                        </div>
                        <p class="mt-3">Searching for the best flights...</p>
                    </div>

                    <!-- Flight Results -->
                    <div id="flightResults">
                        <!-- Flight cards will be dynamically added here -->
                    </div>

                    <!-- No Results Message -->
                    <div class="text-center py-5" id="noResults" style="display: none;">
                        <i class="fas fa-plane-slash fa-3x text-muted mb-3"></i>
                        <h5>No flights found</h5>
                        <p class="text-muted">Try adjusting your search criteria or dates</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Popular Destinations Section -->
    <section class="popular-destinations py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="fw-bold">Popular Destinations</h2>
                <p class="text-muted">Discover amazing places around the world</p>
            </div>
            
            <div class="row" id="popularDestinations">
                <!-- Popular destinations will be loaded here -->
            </div>
        </div>
    </section>
</div>
@endsection

@push('styles')
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.airport-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 0.375rem 0.375rem;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.airport-suggestion {
    padding: 0.75rem;
    cursor: pointer;
    border-bottom: 1px solid #eee;
}

.airport-suggestion:hover {
    background-color: #f8f9fa;
}

.airport-suggestion:last-child {
    border-bottom: none;
}

.flight-card {
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.flight-card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.airline-logo {
    width: 40px;
    height: 40px;
    object-fit: contain;
}

.flight-route {
    position: relative;
}

.flight-route::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(to right, #007bff, #6c757d);
    z-index: 1;
}

.route-dot {
    position: relative;
    z-index: 2;
    background: white;
    border: 2px solid #007bff;
    border-radius: 50%;
    width: 12px;
    height: 12px;
}

.price-highlight {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
}

.destination-card {
    border: none;
    border-radius: 1rem;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.destination-card:hover {
    transform: translateY(-5px);
}

.destination-image {
    height: 200px;
    object-fit: cover;
}
</style>
@endpush

@push('scripts')
<script>
let searchResults = [];
let filteredResults = [];
let currentSearchParams = {};

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    loadPopularDestinations();
});

function initializePage() {
    // Set minimum date to today
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('departure_date').min = today;
    document.getElementById('return_date').min = today;

    // Handle trip type change
    document.querySelectorAll('input[name="trip_type"]').forEach(radio => {
        radio.addEventListener('change', function() {
            const returnContainer = document.getElementById('return_date_container');
            const returnInput = document.getElementById('return_date');

            if (this.value === 'oneway') {
                returnContainer.style.display = 'none';
                returnInput.required = false;
                returnInput.value = '';
            } else {
                returnContainer.style.display = 'block';
                returnInput.required = true;
            }
        });
    });

    // Handle departure date change
    document.getElementById('departure_date').addEventListener('change', function() {
        const returnDate = document.getElementById('return_date');
        returnDate.min = this.value;

        if (returnDate.value && returnDate.value <= this.value) {
            returnDate.value = '';
        }
    });

    // Initialize airport autocomplete
    initializeAirportAutocomplete();

    // Handle form submission
    document.getElementById('flightSearchForm').addEventListener('submit', handleSearch);

    // Initialize filters
    initializeFilters();
}

function initializeAirportAutocomplete() {
    const airportInputs = document.querySelectorAll('.airport-input');

    airportInputs.forEach(input => {
        let timeout;

        input.addEventListener('input', function() {
            clearTimeout(timeout);
            const query = this.value.trim();
            const suggestionsContainer = document.getElementById(this.id.replace('_airport', '_suggestions'));

            if (query.length < 2) {
                suggestionsContainer.style.display = 'none';
                return;
            }

            timeout = setTimeout(() => {
                searchAirports(query, suggestionsContainer, input);
            }, 300);
        });

        // Hide suggestions when clicking outside
        document.addEventListener('click', function(e) {
            if (!input.contains(e.target)) {
                const suggestionsContainer = document.getElementById(input.id.replace('_airport', '_suggestions'));
                suggestionsContainer.style.display = 'none';
            }
        });
    });
}

function searchAirports(query, container, input) {
    fetch(`/flight-search/airports?query=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data.length > 0) {
                displayAirportSuggestions(data.data, container, input);
            } else {
                container.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Airport search error:', error);
            container.style.display = 'none';
        });
}

function displayAirportSuggestions(airports, container, input) {
    container.innerHTML = '';

    airports.forEach(airport => {
        const suggestion = document.createElement('div');
        suggestion.className = 'airport-suggestion';
        suggestion.innerHTML = `
            <div class="fw-bold">${airport.name} (${airport.code})</div>
            <small class="text-muted">${airport.city}, ${airport.country}</small>
        `;

        suggestion.addEventListener('click', function() {
            input.value = `${airport.name} (${airport.code})`;
            input.dataset.code = airport.code;
            container.style.display = 'none';
        });

        container.appendChild(suggestion);
    });

    container.style.display = 'block';
}

function handleSearch(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const searchParams = {};

    // Extract airport codes
    const departureInput = document.getElementById('departure_airport');
    const arrivalInput = document.getElementById('arrival_airport');

    const departureCode = extractAirportCode(departureInput.value) || departureInput.dataset.code;
    const arrivalCode = extractAirportCode(arrivalInput.value) || arrivalInput.dataset.code;

    if (!departureCode || !arrivalCode) {
        showAlert('warning', 'Please select valid airports from the suggestions');
        return;
    }

    // Build search parameters
    searchParams.departure_airport = departureCode;
    searchParams.arrival_airport = arrivalCode;
    searchParams.departure_date = formData.get('departure_date');
    searchParams.return_date = formData.get('return_date');
    searchParams.adults = formData.get('adults');
    searchParams.children = formData.get('children') || 0;
    searchParams.infants = formData.get('infants') || 0;
    searchParams.cabin_class = formData.get('cabin_class');
    searchParams.max_price = formData.get('max_price') || null;
    searchParams.max_results = formData.get('max_results') || 20;
    searchParams.direct_flights_only = formData.get('direct_flights_only') === 'on';

    currentSearchParams = searchParams;
    performSearch(searchParams);
}

function extractAirportCode(value) {
    const match = value.match(/\(([A-Z]{3})\)/);
    return match ? match[1] : null;
}

function performSearch(params) {
    const searchBtn = document.getElementById('searchBtn');
    const loadingSpinner = document.getElementById('loadingSpinner');
    const resultsSection = document.getElementById('searchResults');

    // Show loading state
    searchBtn.disabled = true;
    searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Searching...';
    loadingSpinner.style.display = 'block';
    resultsSection.style.display = 'block';

    // Scroll to results
    resultsSection.scrollIntoView({ behavior: 'smooth' });

    fetch('/flight-search/search', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(params)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            searchResults = data.data;
            filteredResults = [...searchResults];
            displaySearchResults(data);
            updateFilters();
        } else {
            showAlert('danger', data.message);
            showNoResults();
        }
    })
    .catch(error => {
        console.error('Search error:', error);
        showAlert('danger', 'An error occurred while searching for flights. Please try again.');
        showNoResults();
    })
    .finally(() => {
        searchBtn.disabled = false;
        searchBtn.innerHTML = '<i class="fas fa-search me-2"></i>Search Flights';
        loadingSpinner.style.display = 'none';
    });
}

function displaySearchResults(data) {
    const resultsContainer = document.getElementById('flightResults');
    const resultsCount = document.getElementById('resultsCount');
    const searchSummary = document.getElementById('searchSummary');
    const noResults = document.getElementById('noResults');

    if (data.count === 0) {
        showNoResults();
        return;
    }

    // Update results info
    resultsCount.textContent = `${data.count} flight${data.count !== 1 ? 's' : ''} found`;
    searchSummary.textContent = `${currentSearchParams.departure_airport} → ${currentSearchParams.arrival_airport} • ${formatDate(currentSearchParams.departure_date)}`;

    // Clear previous results
    resultsContainer.innerHTML = '';
    noResults.style.display = 'none';

    // Display flights
    data.data.forEach(flight => {
        const flightCard = createFlightCard(flight);
        resultsContainer.appendChild(flightCard);
    });
}

function createFlightCard(flight) {
    const card = document.createElement('div');
    card.className = 'flight-card p-4';

    card.innerHTML = `
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <div class="text-center">
                            <div class="fw-bold">${flight.airline}</div>
                            <small class="text-muted">${flight.flight_number}</small>
                        </div>
                    </div>

                    <div class="col">
                        <div class="row align-items-center">
                            <div class="col-auto text-center">
                                <div class="fw-bold fs-5">${formatTime(flight.departure_time)}</div>
                                <small class="text-muted">${flight.departure_airport}</small>
                            </div>

                            <div class="col">
                                <div class="flight-route text-center position-relative py-2">
                                    <small class="text-muted d-block">${formatDuration(flight.duration)}</small>
                                    <small class="text-muted">${flight.stops === 0 ? 'Non-stop' : flight.stops + ' stop' + (flight.stops > 1 ? 's' : '')}</small>
                                    <div class="route-dot mx-auto"></div>
                                </div>
                            </div>

                            <div class="col-auto text-center">
                                <div class="fw-bold fs-5">${formatTime(flight.arrival_time)}</div>
                                <small class="text-muted">${flight.arrival_airport}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4 text-end">
                <div class="price-highlight text-center">
                    <div class="fw-bold fs-4">$${parseFloat(flight.price).toFixed(2)}</div>
                    <small>${flight.currency}</small>
                </div>
                <button class="btn btn-primary mt-2 w-100" onclick="selectFlight('${flight.id || flight.flight_number}', '${flight.provider}')">
                    Select Flight
                </button>
                <small class="text-muted d-block mt-1">via ${flight.provider}</small>
            </div>
        </div>
    `;

    return card;
}

function showNoResults() {
    document.getElementById('flightResults').innerHTML = '';
    document.getElementById('noResults').style.display = 'block';
    document.getElementById('resultsCount').textContent = '0 flights found';
}

function selectFlight(flightId, provider) {
    // This will be implemented when we create the booking flow
    showAlert('info', 'Booking functionality will be available soon!');
}

// Utility functions
function formatTime(timeString) {
    if (!timeString) return 'N/A';
    try {
        return new Date(timeString).toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        });
    } catch (e) {
        return timeString;
    }
}

function formatDuration(duration) {
    if (!duration) return 'N/A';
    // Parse ISO 8601 duration (PT2H30M) or return as-is
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?/);
    if (match) {
        const hours = match[1] || '0';
        const minutes = match[2] || '0';
        return `${hours}h ${minutes}m`;
    }
    return duration;
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        weekday: 'short',
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Filter functionality (placeholder)
function initializeFilters() {
    // This will be implemented for filtering results
    console.log('Filters initialized');
}

function updateFilters() {
    // This will be implemented to update filter options based on results
    console.log('Filters updated');
}

// Load popular destinations
function loadPopularDestinations() {
    fetch('/flight-search/popular-destinations')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayPopularDestinations(data.data);
            }
        })
        .catch(error => {
            console.error('Error loading popular destinations:', error);
        });
}

function displayPopularDestinations(destinations) {
    const container = document.getElementById('popularDestinations');
    container.innerHTML = '';

    destinations.forEach(destination => {
        const col = document.createElement('div');
        col.className = 'col-md-3 mb-4';

        col.innerHTML = `
            <div class="card destination-card h-100 shadow-sm">
                <div class="card-body text-center">
                    <h5 class="card-title">${destination.city}</h5>
                    <p class="card-text text-muted">${destination.country}</p>
                    <button class="btn btn-outline-primary" onclick="selectDestination('${destination.code}', '${destination.name}')">
                        Explore Flights
                    </button>
                </div>
            </div>
        `;

        container.appendChild(col);
    });
}

function selectDestination(code, name) {
    document.getElementById('arrival_airport').value = `${name} (${code})`;
    document.getElementById('arrival_airport').dataset.code = code;
    document.getElementById('departure_airport').focus();
}
</script>
@endpush
