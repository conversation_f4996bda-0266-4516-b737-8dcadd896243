@extends('layouts.admin')

@section('title', 'All Bookings')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h4><i class="fas fa-list me-2"></i>All Bookings</h4>
                <div>
                    <button class="btn btn-outline-primary" onclick="exportBookings()">
                        <i class="fas fa-download me-1"></i>Export
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.bookings.index') }}" class="row g-3">
                        <div class="col-md-3">
                            <label for="search" class="form-label">Keyword Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ request('search') }}" placeholder="Booking ref, email, flight...">
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">Date From</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" 
                                   value="{{ request('date_from') }}">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">Date To</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" 
                                   value="{{ request('date_to') }}">
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Status</option>
                                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="confirmed" {{ request('status') == 'confirmed' ? 'selected' : '' }}>Confirmed</option>
                                <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-1"></i>Search
                            </button>
                            <a href="{{ route('admin.bookings.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bookings Table -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Booking Details</th>
                                    <th>Flight</th>
                                    <th>Passengers</th>
                                    <th>Price (PKR)</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($bookings as $booking)
                                <tr>
                                    <td>
                                        <div class="fw-bold text-primary">{{ $booking->booking_reference }}</div>
                                        <div class="small text-muted">
                                            Agency: SKY AVENUE TRAVEL & TOURS<br>
                                            Agent: {{ $booking->contact_email }}<br>
                                            Created: {{ $booking->booking_date->format('M d, Y H:i') }}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="fw-bold">{{ $booking->airline_code ?? 'Fly Jinnah' }}</div>
                                        <div class="small text-muted">
                                            {{ $booking->departure_airport ?? 'ISB' }}-{{ $booking->arrival_airport ?? 'BAH' }}<br>
                                            {{ \Carbon\Carbon::parse($booking->departure_date ?? now())->format('M d, Y') }}<br>
                                            {{ $booking->flight_number ?? 'PK764' }}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="passenger-breakdown">
                                            <div class="row text-center">
                                                <div class="col-4">
                                                    <div class="small text-muted">Adults</div>
                                                    <div class="fw-bold">{{ $booking->adult_count }}</div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="small text-muted">Child</div>
                                                    <div class="fw-bold">{{ $booking->child_count }}</div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="small text-muted">Infants</div>
                                                    <div class="fw-bold">{{ $booking->infant_count }}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mt-2">
                                            <div class="small text-muted">Status</div>
                                            <div class="small">
                                                <span class="badge bg-success me-1">Requested</span>
                                                <span class="badge bg-warning">Pending</span>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="fw-bold text-primary">{{ number_format($booking->total_amount) }}</div>
                                        <div class="small text-muted">Total</div>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $booking->status_badge }} px-2 py-1">
                                            {{ $booking->status_display ?? ucfirst($booking->status ?? $booking->booking_status) }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button type="button" class="btn btn-outline-primary" 
                                                    onclick="viewBooking('{{ $booking->booking_reference }}')" 
                                                    title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <button type="button" class="btn btn-outline-secondary dropdown-toggle" 
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fas fa-cog"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" onclick="updateStatus({{ $booking->id }}, 'confirmed')">
                                                        <i class="fas fa-check text-success me-2"></i>Confirm
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="updateStatus({{ $booking->id }}, 'cancelled')">
                                                        <i class="fas fa-times text-danger me-2"></i>Cancel
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="updateStatus({{ $booking->id }}, 'completed')">
                                                        <i class="fas fa-flag-checkered text-info me-2"></i>Complete
                                                    </a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item" href="#" onclick="sendEmail({{ $booking->id }})">
                                                        <i class="fas fa-envelope text-primary me-2"></i>Send Email
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="6" class="text-center py-5">
                                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">No bookings found</h5>
                                        <p class="text-muted">Try adjusting your search criteria.</p>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($bookings->hasPages())
                    <div class="d-flex justify-content-center mt-4">
                        {{ $bookings->appends(request()->query())->links() }}
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function viewBooking(bookingReference) {
    window.open(`/booking/${bookingReference}`, '_blank');
}

function updateStatus(bookingId, status) {
    if (confirm(`Are you sure you want to change the booking status to ${status}?`)) {
        fetch(`/booking/${bookingId}/status`, {
            method: 'PATCH',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ status: status })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Failed to update status. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred. Please try again.');
        });
    }
}

function sendEmail(bookingId) {
    // Implementation for sending email
    alert('Email functionality will be implemented');
}

function exportBookings() {
    const params = new URLSearchParams(window.location.search);
    params.append('export', 'csv');
    window.open(`${window.location.pathname}?${params.toString()}`, '_blank');
}
</script>
@endpush
@endsection
