<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bookings', function (Blueprint $table) {
            $table->id();
            $table->string('booking_reference', 6)->unique(); // e.g., 'ABC123'
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('flight_id')->constrained()->onDelete('cascade');
            $table->integer('passenger_count');
            $table->decimal('total_amount', 10, 2);
            $table->enum('booking_status', ['Pending', 'Confirmed', 'Cancelled', 'Completed'])->default('Pending');
            $table->enum('payment_status', ['Pending', 'Paid', 'Failed', 'Refunded'])->default('Pending');
            $table->string('payment_method')->nullable(); // Credit Card, PayPal, etc.
            $table->string('payment_reference')->nullable();
            $table->dateTime('booking_date');
            $table->string('contact_email');
            $table->string('contact_phone')->nullable();
            $table->text('special_requests')->nullable();
            $table->boolean('is_checked_in')->default(false);
            $table->dateTime('check_in_time')->nullable();
            $table->timestamps();

            // Indexes
            $table->index('booking_reference');
            $table->index(['booking_status', 'payment_status']);
            $table->index('booking_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bookings');
    }
};
