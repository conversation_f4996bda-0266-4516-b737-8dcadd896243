@extends('layouts.admin')

@section('page-title', 'Roles & Permissions')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="page-title">Roles & Permissions</h1>
            <p class="page-subtitle">Manage user roles and permissions</p>
        </div>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createRoleModal">
            <i class="fas fa-plus me-2"></i>Create Role
        </button>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-users-cog me-2"></i>System Roles</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Role Name</th>
                                    <th>Description</th>
                                    <th>Users</th>
                                    <th>Permissions</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse(\Spatie\Permission\Models\Role::withCount(['users', 'permissions'])->get() as $role)
                                <tr>
                                    <td>
                                        <div class="fw-bold text-{{ $role->name === 'Admin' ? 'danger' : ($role->name === 'Manager' ? 'success' : ($role->name === 'Staff' ? 'info' : 'primary')) }}">{{ $role->name }}</div>
                                        <small class="text-muted">
                                            @if($role->name === 'Admin')
                                                Full system access
                                            @elseif($role->name === 'Manager')
                                                Flight and booking management
                                            @elseif($role->name === 'Staff')
                                                Booking management only
                                            @elseif($role->name === 'Viewer')
                                                Read-only access
                                            @else
                                                Custom role
                                            @endif
                                        </small>
                                    </td>
                                    <td>
                                        @if($role->name === 'Admin')
                                            Administrative access to most features
                                        @elseif($role->name === 'Manager')
                                            Manage flights and bookings
                                        @elseif($role->name === 'Staff')
                                            Basic access to view and assist customers
                                        @elseif($role->name === 'Viewer')
                                            Read-only access to reports
                                        @else
                                            Custom role permissions
                                        @endif
                                    </td>
                                    <td><span class="badge bg-primary">{{ $role->users_count }}</span></td>
                                    <td><span class="badge bg-info">{{ $role->permissions_count }} Permissions</span></td>
                                    <td>
                                        @if($role->name === 'Admin')
                                            <span class="text-muted small">System Role</span>
                                        @else
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#editRoleModal">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#viewPermissionsModal">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="confirmDelete('{{ $role->name }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                        @endif
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="5" class="text-center text-muted py-4">
                                        <i class="fas fa-user-shield fa-3x mb-3 opacity-50"></i>
                                        <div>No roles found</div>
                                        <small>Create your first role to get started</small>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Role Distribution</h5>
                </div>
                <div class="card-body">
                    @php
                        $roles = \Spatie\Permission\Models\Role::withCount('users')->get();
                        $totalUsers = $roles->sum('users_count');
                        $colors = ['danger', 'primary', 'success', 'info', 'warning', 'secondary'];
                    @endphp

                    @foreach($roles as $index => $role)
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>{{ $role->name }}</span>
                        <span class="badge bg-{{ $colors[$index % count($colors)] }}">{{ $role->users_count }}</span>
                    </div>
                    <div class="progress mb-3" style="height: 8px;">
                        <div class="progress-bar bg-{{ $colors[$index % count($colors)] }}" style="width: {{ $totalUsers > 0 ? ($role->users_count / $totalUsers) * 100 : 0 }}%"></div>
                    </div>
                    @endforeach

                    <div class="text-center mt-3">
                        <div class="h4 fw-bold">{{ $totalUsers }}</div>
                        <div class="text-muted">Total Users</div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-key me-2"></i>Available Permissions</h5>
                </div>
                <div class="card-body">
                    @php
                        $permissions = \Spatie\Permission\Models\Permission::all();
                        $permissionGroups = [
                            'Dashboard' => ['view-dashboard'],
                            'Flight Management' => ['manage-flights'],
                            'Booking Management' => ['manage-bookings'],
                            'User Management' => ['manage-users'],
                            'Role Management' => ['manage-roles'],
                            'Airlines & Airports' => ['manage-airlines', 'manage-airports'],
                            'Analytics & Reports' => ['view-analytics', 'view-reports', 'export-data'],
                            'Content & Settings' => ['manage-content', 'manage-settings']
                        ];
                    @endphp

                    <div class="row">
                        @foreach($permissionGroups as $group => $groupPermissions)
                        <div class="col-12 mb-2">
                            <strong>{{ $group }}</strong>
                            <div class="small text-muted">
                                @foreach($groupPermissions as $permissionName)
                                    @if($permissions->where('name', $permissionName)->first())
                                        • {{ ucwords(str_replace('-', ' ', $permissionName)) }}<br>
                                    @endif
                                @endforeach
                            </div>
                        </div>
                        @endforeach
                    </div>

                    <div class="text-center mt-3">
                        <div class="h5 fw-bold">{{ $permissions->count() }}</div>
                        <div class="text-muted">Total Permissions</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Role Modal -->
<div class="modal fade" id="createRoleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Create New Role</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="roleName" class="form-label">Role Name</label>
                            <input type="text" class="form-control" id="roleName" placeholder="Enter role name">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="roleColor" class="form-label">Role Color</label>
                            <select class="form-select" id="roleColor">
                                <option value="primary">Blue</option>
                                <option value="success">Green</option>
                                <option value="warning">Yellow</option>
                                <option value="info">Cyan</option>
                                <option value="secondary">Gray</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="roleDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="roleDescription" rows="3" placeholder="Enter role description"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Permissions</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="perm1">
                                    <label class="form-check-label" for="perm1">View Users</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="perm2">
                                    <label class="form-check-label" for="perm2">Create Users</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="perm3">
                                    <label class="form-check-label" for="perm3">Edit Users</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="perm4">
                                    <label class="form-check-label" for="perm4">Delete Users</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="perm5">
                                    <label class="form-check-label" for="perm5">View Flights</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="perm6">
                                    <label class="form-check-label" for="perm6">Create Flights</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="perm7">
                                    <label class="form-check-label" for="perm7">Edit Flights</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="perm8">
                                    <label class="form-check-label" for="perm8">Delete Flights</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary">Create Role</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Role Modal -->
<div class="modal fade" id="editRoleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Edit Role</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="editRoleName" class="form-label">Role Name</label>
                            <input type="text" class="form-control" id="editRoleName" placeholder="Enter role name">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editRoleColor" class="form-label">Role Color</label>
                            <select class="form-select" id="editRoleColor">
                                <option value="primary">Blue</option>
                                <option value="success">Green</option>
                                <option value="warning">Yellow</option>
                                <option value="info">Cyan</option>
                                <option value="secondary">Gray</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="editRoleDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="editRoleDescription" rows="3" placeholder="Enter role description"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Permissions</label>
                        <div class="row">
                            @php
                                $editPermissions = \Spatie\Permission\Models\Permission::all();
                                $editPermissionGroups = [
                                    'Dashboard' => ['view-dashboard'],
                                    'Flight Management' => ['manage-flights'],
                                    'Booking Management' => ['manage-bookings'],
                                    'User Management' => ['manage-users'],
                                    'Role Management' => ['manage-roles'],
                                    'Airlines & Airports' => ['manage-airlines', 'manage-airports'],
                                    'Analytics & Reports' => ['view-analytics', 'view-reports', 'export-data'],
                                    'Content & Settings' => ['manage-content', 'manage-settings']
                                ];
                            @endphp

                            @foreach($editPermissionGroups as $group => $groupPermissions)
                            <div class="col-md-6 mb-3">
                                <h6 class="fw-bold">{{ $group }}</h6>
                                @foreach($groupPermissions as $permissionName)
                                    @if($editPermissions->where('name', $permissionName)->first())
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="edit_{{ $permissionName }}" name="permissions[]" value="{{ $permissionName }}">
                                        <label class="form-check-label" for="edit_{{ $permissionName }}">
                                            {{ ucwords(str_replace('-', ' ', $permissionName)) }}
                                        </label>
                                    </div>
                                    @endif
                                @endforeach
                            </div>
                            @endforeach
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary">Update Role</button>
            </div>
        </div>
    </div>
</div>

<!-- View Permissions Modal -->
<div class="modal fade" id="viewPermissionsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-eye me-2"></i>Role Permissions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="fw-bold text-primary">Role Information</h6>
                        <div class="mb-3">
                            <strong>Name:</strong> <span id="viewRoleName">-</span>
                        </div>
                        <div class="mb-3">
                            <strong>Users:</strong> <span id="viewRoleUsers" class="badge bg-primary">0</span>
                        </div>
                        <div class="mb-3">
                            <strong>Total Permissions:</strong> <span id="viewRolePermissionCount" class="badge bg-info">0</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="fw-bold text-success">Assigned Permissions</h6>
                        <div id="viewRolePermissions" class="small">
                            <!-- Permissions will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#editRoleModal" data-bs-dismiss="modal">Edit Role</button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
function confirmDelete(roleName) {
    if (confirm(`Are you sure you want to delete the "${roleName}" role? This action cannot be undone.`)) {
        // Handle role deletion
        alert('Role deletion functionality would be implemented here.');
    }
}

// Handle view permissions modal
document.addEventListener('DOMContentLoaded', function() {
    const viewPermissionsModal = document.getElementById('viewPermissionsModal');
    if (viewPermissionsModal) {
        viewPermissionsModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const roleRow = button.closest('tr');

            if (roleRow) {
                const roleName = roleRow.querySelector('.fw-bold').textContent.trim();
                const userCount = roleRow.querySelector('.badge.bg-primary').textContent.trim();
                const permissionCount = roleRow.querySelector('.badge.bg-info').textContent.replace(' Permissions', '').trim();

                document.getElementById('viewRoleName').textContent = roleName;
                document.getElementById('viewRoleUsers').textContent = userCount;
                document.getElementById('viewRolePermissionCount').textContent = permissionCount;

                // Show loading message
                document.getElementById('viewRolePermissions').innerHTML =
                    '<div class="text-muted"><i class="fas fa-spinner fa-spin me-2"></i>Loading permissions...</div>';

                // Simulate loading permissions (you can replace this with actual AJAX call)
                setTimeout(() => {
                    let permissionsHtml = '';
                    if (roleName === 'Admin') {
                        permissionsHtml = `
                            <div class="badge bg-success me-1 mb-1">View Dashboard</div>
                            <div class="badge bg-success me-1 mb-1">Manage Flights</div>
                            <div class="badge bg-success me-1 mb-1">Manage Bookings</div>
                            <div class="badge bg-success me-1 mb-1">Manage Airlines</div>
                            <div class="badge bg-success me-1 mb-1">Manage Airports</div>
                            <div class="badge bg-success me-1 mb-1">Manage Users</div>
                            <div class="badge bg-success me-1 mb-1">Manage Roles</div>
                            <div class="badge bg-success me-1 mb-1">View Analytics</div>
                            <div class="badge bg-success me-1 mb-1">Manage Content</div>
                            <div class="badge bg-success me-1 mb-1">Manage Settings</div>
                            <div class="badge bg-success me-1 mb-1">Export Data</div>
                            <div class="badge bg-success me-1 mb-1">View Reports</div>
                        `;
                    } else if (roleName === 'Manager') {
                        permissionsHtml = `
                            <div class="badge bg-info me-1 mb-1">View Dashboard</div>
                            <div class="badge bg-info me-1 mb-1">Manage Flights</div>
                            <div class="badge bg-info me-1 mb-1">Manage Bookings</div>
                            <div class="badge bg-info me-1 mb-1">View Analytics</div>
                            <div class="badge bg-info me-1 mb-1">View Reports</div>
                            <div class="badge bg-info me-1 mb-1">Export Data</div>
                        `;
                    } else if (roleName === 'Staff') {
                        permissionsHtml = `
                            <div class="badge bg-warning me-1 mb-1">View Dashboard</div>
                            <div class="badge bg-warning me-1 mb-1">Manage Bookings</div>
                            <div class="badge bg-warning me-1 mb-1">View Reports</div>
                        `;
                    } else if (roleName === 'Viewer') {
                        permissionsHtml = `
                            <div class="badge bg-secondary me-1 mb-1">View Dashboard</div>
                            <div class="badge bg-secondary me-1 mb-1">View Reports</div>
                        `;
                    } else {
                        permissionsHtml = '<div class="text-muted">No permissions assigned</div>';
                    }

                    document.getElementById('viewRolePermissions').innerHTML = permissionsHtml;
                }, 500);
            }
        });
    }

    // Handle edit role modal
    const editRoleModal = document.getElementById('editRoleModal');
    if (editRoleModal) {
        editRoleModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const roleRow = button.closest('tr');

            if (roleRow) {
                const roleName = roleRow.querySelector('.fw-bold').textContent.trim();
                document.getElementById('editRoleName').value = roleName;

                // You can add AJAX call here to load role data for editing
                // For now, just populate the role name
            }
        });
    }
});
</script>
@endpush
