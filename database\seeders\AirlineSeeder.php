<?php

namespace Database\Seeders;

use App\Models\Airline;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AirlineSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $airlines = [
            [
                'code' => 'AA',
                'name' => 'American Airlines',
                'country' => 'US',
                'is_active' => true,
            ],
            [
                'code' => 'BA',
                'name' => 'British Airways',
                'country' => 'GB',
                'is_active' => true,
            ],
            [
                'code' => 'DL',
                'name' => 'Delta Air Lines',
                'country' => 'US',
                'is_active' => true,
            ],
            [
                'code' => 'UA',
                'name' => 'United Airlines',
                'country' => 'US',
                'is_active' => true,
            ],
            [
                'code' => 'LH',
                'name' => 'Lufthansa',
                'country' => 'DE',
                'is_active' => true,
            ],
            [
                'code' => 'AF',
                'name' => 'Air France',
                'country' => 'FR',
                'is_active' => true,
            ],
            [
                'code' => 'EK',
                'name' => 'Emirates',
                'country' => 'AE',
                'is_active' => true,
            ],
            [
                'code' => 'QR',
                'name' => 'Qatar Airways',
                'country' => 'QA',
                'is_active' => true,
            ],
            [
                'code' => 'SQ',
                'name' => 'Singapore Airlines',
                'country' => 'SG',
                'is_active' => true,
            ],
            [
                'code' => 'JL',
                'name' => 'Japan Airlines',
                'country' => 'JP',
                'is_active' => true,
            ],
        ];

        foreach ($airlines as $airline) {
            Airline::create($airline);
        }
    }
}
