<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Add new columns for flight search functionality
            $table->string('departure_airport', 3)->nullable()->after('flight_id');
            $table->string('arrival_airport', 3)->nullable()->after('departure_airport');
            $table->date('departure_date')->nullable()->after('arrival_airport');
            $table->time('departure_time')->nullable()->after('departure_date');
            $table->time('arrival_time')->nullable()->after('departure_time');
            $table->string('airline_code', 3)->nullable()->after('arrival_time');
            $table->string('flight_number', 10)->nullable()->after('airline_code');
            $table->integer('total_passengers')->nullable()->after('passenger_count');
            $table->string('currency', 3)->default('PKR')->after('total_amount');
            $table->string('status')->nullable()->after('booking_status');
            $table->text('notes')->nullable()->after('special_requests');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->dropColumn([
                'departure_airport',
                'arrival_airport',
                'departure_date',
                'departure_time',
                'arrival_time',
                'airline_code',
                'flight_number',
                'total_passengers',
                'currency',
                'status',
                'notes'
            ]);
        });
    }
};
