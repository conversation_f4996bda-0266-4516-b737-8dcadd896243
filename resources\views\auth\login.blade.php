<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Login to {{ config('app.name', 'FlyNow Airlines') }} - Professional airline booking system">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>Login | {{ config('app.name', 'FlyNow Airlines') }}</title>

    <!-- Favicon -->
    <link rel="shortcut icon" type="image/x-icon" href="{{ asset('assets/media/favicon.png') }}">

    <!-- All CSS files -->
    <link rel="stylesheet" href="{{ asset('assets/css/vendor/font-awesome.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/fonts/icomoon/style.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/vendor/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/vendor/slick.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/vendor/slick-theme.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
</head>

<body class="o-scroll" id="js-scroll">
    <!-- Preloader-->
    <div id="preloader">
        <div class="loader">
            <div class="plane">
                <img src="{{ asset('assets/media/plane.gif') }}" class="plane-img" alt="">
            </div>
            <div class="earth-wrapper">
                <div class="earth"></div>
            </div>
        </div>
    </div>

    <!-- Back To Top Start -->
    <a href="#main-wrapper" id="backto-top" class="back-to-top"><i class="fas fa-angle-up"></i></a>

    <!-- Main Wrapper Start -->
    <div id="main-wrapper" class="main-wrapper overflow-hidden">
        <!-- Main Content Start -->
        <div class="page-content m-0">
            <!-- Login Form Start -->
            <section class="signup bg-white">
                <div class="row align-items-center justify-content-center">
                    <div class="col-lg-5 col-md-9 col-sm-10 p-0">
                        <div class="container-fluid">
                            <div class="form-block">
                                <a href="{{ route('home') }}" class="color-primary h6 mb-30">
                                    <i class="fal fa-chevron-left"></i>&nbsp;&nbsp;Back To Home
                                </a>
                                <h2 class="mb-30 light-black">Log in</h2>

                                <!-- Session Status -->
                                @if (session('status'))
                                    <div class="alert alert-success mb-24">
                                        {{ session('status') }}
                                    </div>
                                @endif

                                <!-- Social Login Buttons -->
                                <div class="google mb-24">
                                    <h6 class="color-white">
                                        <a href="#" class="link-btn mb-24 mb-sm-0">
                                            Continue with Google&nbsp;<img src="{{ asset('assets/media/icons/google-icon.png') }}" alt="">
                                        </a>
                                    </h6>
                                </div>
                                <div class="facebook mb-30">
                                    <h6 class="color-white">
                                        <a href="#" class="link-btn">
                                            Continue with Facebook&nbsp;<img src="{{ asset('assets/media/icons/facebook-icon.png') }}" alt="">
                                        </a>
                                    </h6>
                                </div>

                                <h5 class="or mb-8">or</h5>
                                <h6 class="mb-24 text-center">Sign in with your email address</h6>

                                <!-- Login Form -->
                                <form method="POST" action="{{ route('login') }}" class="form-group contact-form">
                                    @csrf
                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="mb-24">
                                                <input type="email"
                                                       class="form-control @error('email') is-invalid @enderror"
                                                       id="email"
                                                       name="email"
                                                       value="{{ old('email') }}"
                                                       required
                                                       autofocus
                                                       placeholder="Email">
                                                @error('email')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-sm-12">
                                            <div class="mb-24">
                                                <input type="password"
                                                       class="form-control @error('password') is-invalid @enderror"
                                                       id="password"
                                                       name="password"
                                                       required
                                                       placeholder="Password">
                                                @error('password')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>

                                        <!-- Remember Me -->
                                        <div class="col-sm-12 mb-24">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="remember" id="remember" {{ old('remember') ? 'checked' : '' }}>
                                                <label class="form-check-label" for="remember">
                                                    Remember me
                                                </label>
                                            </div>
                                        </div>

                                        <div class="d-flex justify-content-between align-items-center">
                                            @if (Route::has('password.request'))
                                                <a href="{{ route('password.request') }}" class="color-primary">
                                                    Forgot your password?
                                                </a>
                                            @endif
                                            <button type="submit" class="cus-btn small-pad mb-24">Login</button>
                                        </div>

                                        <div class="text-center">
                                            <p>Don't have an account? <a href="{{ route('register') }}" class="color-primary">Sign up</a></p>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-7 p-0">
                        <div class="img-block">
                            <img src="{{ asset('assets/media/images/login.jpg') }}" alt="Login">
                        </div>
                    </div>
                </div>
            </section>
            <!-- Login Form End -->
        </div>
        <!-- Main Content End -->
    </div>

    <!-- All JS files -->
    <script src="{{ asset('assets/js/vendor/jquery-3.6.3.min.js') }}"></script>
    <script src="{{ asset('assets/js/vendor/bootstrap.min.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}"></script>
</body>
</html>
