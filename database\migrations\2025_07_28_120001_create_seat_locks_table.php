<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('seat_locks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pnr_id')->constrained()->onDelete('cascade');
            $table->string('session_id'); // User session identifier
            $table->string('user_ip')->nullable(); // User IP for additional security
            $table->integer('seats_locked'); // Number of seats locked by this session
            $table->timestamp('locked_at'); // When the lock was created
            $table->timestamp('expires_at'); // When the lock expires (50-60 seconds)
            $table->enum('status', ['active', 'expired', 'completed', 'cancelled'])->default('active');
            $table->string('booking_reference')->nullable(); // If booking was completed
            $table->timestamps();

            // Indexes for performance
            $table->index(['pnr_id', 'status']);
            $table->index(['session_id', 'status']);
            $table->index('expires_at');
            $table->index(['status', 'expires_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('seat_locks');
    }
};
