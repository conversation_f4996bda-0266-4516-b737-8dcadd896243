@extends('layouts.admin')

@section('title', 'Edit User')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-user-edit me-2"></i>Edit User
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.users.index') }}">Users</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.users.show', $user) }}">{{ $user->name }}</a></li>
                    <li class="breadcrumb-item active">Edit</li>
                </ol>
            </nav>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('admin.users.show', $user) }}" class="btn btn-info">
                <i class="fas fa-eye me-1"></i>View User
            </a>
            <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Users
            </a>
        </div>
    </div>

    @if ($errors->any())
        <div class="alert alert-danger">
            <ul class="mb-0">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <form action="{{ route('admin.users.update', $user) }}" method="POST">
        @csrf
        @method('PUT')
        
        <div class="row">
            <!-- User Information -->
            <div class="col-xl-8 col-lg-7">
                <!-- Basic Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-info-circle me-2"></i>Basic Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name', $user->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                       id="email" name="email" value="{{ old('email', $user->email) }}" required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">New Password</label>
                                <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                       id="password" name="password" placeholder="Leave blank to keep current password">
                                <div class="form-text">
                                    <small>Password must be at least 8 characters long. Leave blank to keep current password.</small>
                                </div>
                                @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="password_confirmation" class="form-label">Confirm New Password</label>
                                <input type="password" class="form-control" 
                                       id="password_confirmation" name="password_confirmation" 
                                       placeholder="Confirm new password">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Roles and Permissions -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-success">
                            <i class="fas fa-user-shield me-2"></i>Roles and Permissions
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Assign Roles</label>
                            <div class="row">
                                @foreach($roles as $role)
                                    <div class="col-md-6 col-lg-4 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" 
                                                   id="role_{{ $role->id }}" name="roles[]" value="{{ $role->name }}"
                                                   {{ $user->hasRole($role->name) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="role_{{ $role->id }}">
                                                @php
                                                    $badgeClass = match($role->name) {
                                                        'Admin' => 'bg-danger',
                                                        'Manager' => 'bg-primary',
                                                        'Staff' => 'bg-info',
                                                        'Viewer' => 'bg-secondary',
                                                        default => 'bg-secondary'
                                                    };
                                                @endphp
                                                <span class="badge {{ $badgeClass }} me-1">{{ $role->name }}</span>
                                            </label>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                            @error('roles')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Role Descriptions:</strong>
                            <ul class="mb-0 mt-2">
                                <li><strong>Admin:</strong> Full system access and user management</li>
                                <li><strong>Manager:</strong> Flight and booking management, user oversight</li>
                                <li><strong>Staff:</strong> Basic booking and customer service operations</li>
                                <li><strong>Viewer:</strong> Read-only access to system data</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="card shadow mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>Update User
                                </button>
                                <button type="reset" class="btn btn-secondary">
                                    <i class="fas fa-undo me-1"></i>Reset Form
                                </button>
                            </div>
                            <div>
                                <a href="{{ route('admin.users.show', $user) }}" class="btn btn-info">
                                    <i class="fas fa-eye me-1"></i>View User
                                </a>
                                <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>Cancel
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Sidebar -->
            <div class="col-xl-4 col-lg-5">
                <!-- Current User Info -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-user me-2"></i>Current User Info
                        </h6>
                    </div>
                    <div class="card-body text-center">
                        <img src="https://ui-avatars.com/api/?name={{ urlencode($user->name) }}&size=120&background=4e73df&color=ffffff" 
                             class="rounded-circle mb-3" width="120" height="120" alt="Profile Picture">
                        <h5 class="card-title">{{ $user->name }}</h5>
                        <p class="text-muted">{{ $user->email }}</p>
                        
                        <div class="mt-3">
                            <strong>Current Status:</strong><br>
                            @if($user->email_verified_at)
                                <span class="badge bg-success">
                                    <i class="fas fa-check-circle me-1"></i>Active
                                </span>
                            @else
                                <span class="badge bg-warning">
                                    <i class="fas fa-clock me-1"></i>Pending Verification
                                </span>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Current Roles -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-success">
                            <i class="fas fa-user-tag me-2"></i>Current Roles
                        </h6>
                    </div>
                    <div class="card-body">
                        @if($user->roles->count() > 0)
                            @foreach($user->roles as $role)
                                @php
                                    $badgeClass = match($role->name) {
                                        'Admin' => 'bg-danger',
                                        'Manager' => 'bg-primary',
                                        'Staff' => 'bg-info',
                                        'Viewer' => 'bg-secondary',
                                        default => 'bg-secondary'
                                    };
                                @endphp
                                <span class="badge {{ $badgeClass }} me-2 mb-2">
                                    <i class="fas fa-user-tag me-1"></i>{{ $role->name }}
                                </span>
                            @endforeach
                        @else
                            <p class="text-muted mb-0">No roles currently assigned</p>
                        @endif
                    </div>
                </div>

                <!-- Account Details -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-info">
                            <i class="fas fa-info-circle me-2"></i>Account Details
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>User ID:</strong><br>
                            <code>{{ $user->id }}</code>
                        </div>
                        <div class="mb-3">
                            <strong>Member Since:</strong><br>
                            {{ $user->created_at->format('F j, Y') }}
                            <small class="text-muted d-block">{{ $user->created_at->diffForHumans() }}</small>
                        </div>
                        <div class="mb-3">
                            <strong>Last Updated:</strong><br>
                            {{ $user->updated_at->format('M j, Y g:i A') }}
                            <small class="text-muted d-block">{{ $user->updated_at->diffForHumans() }}</small>
                        </div>
                        @if($user->email_verified_at)
                        <div class="mb-3">
                            <strong>Email Verified:</strong><br>
                            {{ $user->email_verified_at->format('M j, Y g:i A') }}
                            <small class="text-muted d-block">{{ $user->email_verified_at->diffForHumans() }}</small>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Security Warning -->
                @if($user->id === auth()->id())
                <div class="card shadow mb-4 border-warning">
                    <div class="card-header py-3 bg-warning">
                        <h6 class="m-0 font-weight-bold text-dark">
                            <i class="fas fa-exclamation-triangle me-2"></i>Security Notice
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="text-warning mb-0">
                            <i class="fas fa-info-circle me-1"></i>
                            You are editing your own account. Be careful when changing roles or permissions.
                        </p>
                    </div>
                </div>
                @endif

                <!-- Help Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-secondary">
                            <i class="fas fa-question-circle me-2"></i>Help & Tips
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2">
                                <i class="fas fa-lightbulb text-warning me-2"></i>
                                <small>Leave password fields blank to keep current password</small>
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-shield-alt text-success me-2"></i>
                                <small>Admin role grants full system access</small>
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-users text-info me-2"></i>
                                <small>Users can have multiple roles</small>
                            </li>
                            <li>
                                <i class="fas fa-save text-primary me-2"></i>
                                <small>Changes take effect immediately after saving</small>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

@endsection

@push('scripts')
<script>
// Password confirmation validation
document.getElementById('password_confirmation').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmation = this.value;
    
    if (password && confirmation && password !== confirmation) {
        this.setCustomValidity('Passwords do not match');
        this.classList.add('is-invalid');
    } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
    }
});

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const password = document.getElementById('password').value;
    const confirmation = document.getElementById('password_confirmation').value;
    
    if (password && password !== confirmation) {
        e.preventDefault();
        alert('Passwords do not match. Please check your password confirmation.');
        return false;
    }
});

// Role selection helper
document.querySelectorAll('input[name="roles[]"]').forEach(function(checkbox) {
    checkbox.addEventListener('change', function() {
        const selectedRoles = document.querySelectorAll('input[name="roles[]"]:checked');
        if (selectedRoles.length === 0) {
            // Optionally warn about no roles selected
        }
    });
});
</script>
@endpush
