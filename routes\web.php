<?php

use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\FlightController;
use App\Http\Controllers\Admin\BookingController;
use App\Http\Controllers\Admin\SettingsController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

// Frontend Routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/about', [HomeController::class, 'about'])->name('about');
Route::get('/contact', [HomeController::class, 'contact'])->name('contact');
Route::get('/privacy-policy', [HomeController::class, 'privacy'])->name('privacy');

// Test route to show current app name and settings
Route::get('/test-app-name', function() {
    return response()->json([
        'app_name_from_config' => config('app.name'),
        'app_name_from_env' => env('APP_NAME'),
        'site_name_from_cache' => \Illuminate\Support\Facades\Cache::get('settings.site_name'),
        'contact_email_from_cache' => \Illuminate\Support\Facades\Cache::get('settings.contact_email'),
        'contact_phone_from_cache' => \Illuminate\Support\Facades\Cache::get('settings.contact_phone'),
        'current_time' => now()->toISOString()
    ]);
})->name('test.app-name');

// Test route to verify providers
Route::get('/test-providers', function() {
    $flightService = app(\App\Services\FlightApiService::class);
    $providers = $flightService->getAvailableProviders();

    $result = [];
    foreach ($providers as $name => $provider) {
        $result[$name] = [
            'class' => get_class($provider),
            'is_enabled' => $provider->isEnabled(),
            'has_search_method' => method_exists($provider, 'searchFlights'),
            'has_airport_search' => method_exists($provider, 'searchAirports'),
        ];
    }

    return response()->json([
        'providers' => $result,
        'total_providers' => count($providers),
        'enabled_providers' => array_filter($result, fn($p) => $p['is_enabled'])
    ]);
})->name('test.providers');

// Flight Search Results Routes
Route::get('/flight-search-results', [App\Http\Controllers\FlightSearchResultsController::class, 'index'])->name('flight-search.results');
Route::post('/flight-search-results/filter-airline', [App\Http\Controllers\FlightSearchResultsController::class, 'filterByAirline'])->name('flight-search.filter-airline');
Route::post('/flight-search-results/filter-price', [App\Http\Controllers\FlightSearchResultsController::class, 'filterByPrice'])->name('flight-search.filter-price');
Route::post('/flight-search-results/sort', [App\Http\Controllers\FlightSearchResultsController::class, 'sortFlights'])->name('flight-search.sort');

// Booking Routes
Route::get('/booking/{flightId}', [App\Http\Controllers\BookingController::class, 'create'])->name('booking.create');
Route::post('/booking', [App\Http\Controllers\BookingController::class, 'store'])->name('booking.store');
Route::get('/booking/{bookingReference}/details', [App\Http\Controllers\BookingController::class, 'show'])->name('booking.show');
Route::post('/booking/{bookingReference}/cancel', [App\Http\Controllers\BookingController::class, 'cancel'])->name('booking.cancel');

// Admin Booking Routes (moved to admin group below)

// Frontend Service Routes
Route::get('/flights', function () { return view('frontend.flights.listing'); })->name('flights.listing');

// Book Tickets Routes
Route::get('/book-tickets', [App\Http\Controllers\BookTicketsController::class, 'index'])->name('book-tickets');
Route::get('/api/airports/search', [App\Http\Controllers\BookTicketsController::class, 'searchAirports'])->name('airports.search');



Route::middleware('auth')->group(function () {
    Route::get('/dashboard', function () {
        try {
            \Log::info('Dashboard route accessed by user: ' . auth()->id());
            return redirect()->route('admin.dashboard');
        } catch (\Exception $e) {
            \Log::error('Dashboard redirect error: ' . $e->getMessage());
            return redirect()->route('home')->with('error', 'Dashboard temporarily unavailable. Please try again.');
        }
    })->name('dashboard');
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Admin Routes
Route::middleware(['auth'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    
    // User Management Routes
    Route::resource('users', UserController::class);

    // Flight Management Routes
    Route::resource('flights', FlightController::class);

    // Booking Management Routes
    Route::resource('bookings', BookingController::class);
    Route::post('bookings/{booking}/cancel', [BookingController::class, 'cancel'])->name('bookings.cancel');
    Route::post('bookings/{booking}/check-in', [BookingController::class, 'checkIn'])->name('bookings.check-in');

    // Role Management Routes
    Route::get('/roles', function () {
        return view('admin.roles.index');
    })->name('roles.index');
    
    Route::get('/roles/create', function () {
        return view('admin.roles.create');
    })->name('roles.create');
    
    // Content Management Routes
    Route::get('/content', function () {
        return view('admin.content.index');
    })->name('content.index');
    
    Route::get('/content/create', function () {
        return view('admin.content.create');
    })->name('content.create');
    
    // Analytics Route
    Route::get('/analytics', function () {
        return view('admin.analytics');
    })->name('analytics');
    
    // Settings Routes
    Route::get('/settings', [SettingsController::class, 'index'])->name('settings');
    Route::post('/settings', [SettingsController::class, 'update'])->name('settings.update');
    Route::post('/settings/reset', [SettingsController::class, 'reset'])->name('settings.reset');

    // Test route to show current settings
    Route::get('/settings/test', function() {
        return response()->json([
            'app_name' => config('app.name'),
            'site_name_from_cache' => \Illuminate\Support\Facades\Cache::get('settings.site_name'),
            'contact_email' => \Illuminate\Support\Facades\Cache::get('settings.contact_email'),
            'all_settings_keys' => \Illuminate\Support\Facades\Cache::get('settings.*')
        ]);
    })->name('settings.test');
    
    // Activity Logs Route
    Route::get('/activity-logs', function () {
        return view('admin.activity-logs');
    })->name('activity-logs');
    
    // Profile Route
    Route::get('/profile', function () {
        return view('admin.profile');
    })->name('profile');

    // Flight API Routes
    Route::prefix('flight-api')->name('flight-api.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\FlightApiController::class, 'index'])->name('index');
        Route::post('/search', [App\Http\Controllers\Admin\FlightApiController::class, 'search'])->name('search');
        Route::post('/import', [App\Http\Controllers\Admin\FlightApiController::class, 'import'])->name('import');
        Route::post('/sync', [App\Http\Controllers\Admin\FlightApiController::class, 'sync'])->name('sync');
        Route::post('/prices', [App\Http\Controllers\Admin\FlightApiController::class, 'getPrices'])->name('prices');
        Route::get('/airports', [App\Http\Controllers\Admin\FlightApiController::class, 'searchAirports'])->name('airports');
    });

    // Flight Search Management Routes
    Route::prefix('flight-search-admin')->name('flight-search.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\FlightSearchController::class, 'index'])->name('index');
        Route::post('/test-connection', [App\Http\Controllers\Admin\FlightSearchController::class, 'testConnection'])->name('test-connection');
        Route::get('/analytics', [App\Http\Controllers\Admin\FlightSearchController::class, 'analytics'])->name('analytics');
        Route::post('/clear-cache', [App\Http\Controllers\Admin\FlightSearchController::class, 'clearCache'])->name('clear-cache');
    });

    // Admin Book Tickets Routes
    Route::prefix('book-tickets')->name('book-tickets.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\BookTicketsController::class, 'index'])->name('index');
        Route::post('/search', [App\Http\Controllers\Admin\BookTicketsController::class, 'searchFlights'])->name('search');
        Route::post('/book', [App\Http\Controllers\Admin\BookTicketsController::class, 'bookFlight'])->name('book');
        Route::get('/airports/search', [App\Http\Controllers\Admin\BookTicketsController::class, 'searchAirports'])->name('airports.search');
    });

    // Airport Management Routes
    Route::resource('airports', App\Http\Controllers\Admin\AirportController::class);
    Route::post('airports/bulk-import', [App\Http\Controllers\Admin\AirportController::class, 'bulkImport'])->name('airports.bulk-import');
    Route::get('airports/export', [App\Http\Controllers\Admin\AirportController::class, 'export'])->name('airports.export');
    Route::post('airports/{airport}/toggle-status', [App\Http\Controllers\Admin\AirportController::class, 'toggleStatus'])->name('airports.toggle-status');

    // Booking Management Routes
    Route::prefix('bookings')->name('bookings.')->group(function () {
        Route::get('/', [App\Http\Controllers\BookingController::class, 'index'])->name('index');
        Route::patch('/{id}/status', [App\Http\Controllers\BookingController::class, 'updateStatus'])->name('update-status');
        Route::get('/export', [App\Http\Controllers\BookingController::class, 'export'])->name('export');
    });
});

// Frontend Flight Search Routes (Public Access)
Route::prefix('flight-search')->name('flight-search.')->group(function () {
    Route::get('/', [App\Http\Controllers\FlightSearchController::class, 'index'])->name('index');
    Route::post('/search', [App\Http\Controllers\FlightSearchController::class, 'search'])->name('search');
    Route::get('/flight-details', [App\Http\Controllers\FlightSearchController::class, 'getFlightDetails'])->name('flight-details');
    Route::get('/airports', [App\Http\Controllers\FlightSearchController::class, 'searchAirports'])->name('airports');
    Route::get('/popular-destinations', [App\Http\Controllers\FlightSearchController::class, 'getPopularDestinations'])->name('popular-destinations');
});

// Simple test route for API
Route::get('/test-flight-api', function() {
    try {
        $flightService = app(\App\Services\FlightApiService::class);
        $airports = $flightService->searchAirports('new');
        return response()->json([
            'success' => true,
            'message' => 'Flight API is working!',
            'airports' => $airports,
            'providers' => $flightService->getAvailableProviders()
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Flight API error: ' . $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// API Routes for AJAX calls (Public Access)
Route::prefix('api')->name('api.')->group(function () {
    Route::post('/flights/search', [App\Http\Controllers\FlightSearchController::class, 'search'])->name('flights.search');

    // Airport Autocomplete Routes
    Route::get('/airports/autocomplete', [App\Http\Controllers\AirportAutocompleteController::class, 'search'])->name('airports.autocomplete');
    Route::get('/airports/details', [App\Http\Controllers\AirportAutocompleteController::class, 'getAirportDetails'])->name('airports.details');
    Route::get('/airports/popular', [App\Http\Controllers\AirportAutocompleteController::class, 'getPopularAirports'])->name('airports.popular');

    // Debug route
    Route::get('/debug', function() {
        return response()->json([
            'success' => true,
            'message' => 'API routes are working!',
            'timestamp' => now()->toISOString()
        ]);
    });
});

// Frontend Service Routes
Route::get('/flights/booking', function () {
    // Check if we have flight search parameters
    $flightId = request('flight_id');
    if ($flightId) {
        // Redirect to the proper booking controller
        return redirect()->route('booking.create', ['flightId' => $flightId]);
    }

    // If no flight selected, redirect to flight search
    return redirect()->route('home')->with('message', 'Please select a flight to book.');
})->name('flights.booking');

Route::get('/hotels', function () {
    return view('frontend.hotels.index');
})->name('hotels.index');

Route::get('/contact', function () {
    return view('frontend.contact');
})->name('contact');

Route::get('/blog', function () {
    return view('frontend.blog.index');
})->name('blog.index');

// Authentication Routes
Route::get('/login', function () {
    return view('auth.login');
})->name('login');

Route::post('/login', [App\Http\Controllers\Auth\AuthenticatedSessionController::class, 'store']);

Route::get('/register', function () {
    return view('auth.register');
})->name('register');

Route::post('/register', [App\Http\Controllers\Auth\RegisteredUserController::class, 'store']);

Route::post('/logout', [App\Http\Controllers\Auth\AuthenticatedSessionController::class, 'destroy'])
    ->middleware('auth')
    ->name('logout');

Route::get('/forgot-password', function () {
    return view('auth.forgot-password');
})->name('password.request');

Route::post('/forgot-password', [App\Http\Controllers\Auth\PasswordResetLinkController::class, 'store'])
    ->name('password.email');

Route::get('/reset-password/{token}', function ($token) {
    return view('auth.reset-password', ['token' => $token]);
})->name('password.reset');

Route::post('/reset-password', [App\Http\Controllers\Auth\NewPasswordController::class, 'store'])
    ->name('password.store');

// Additional Frontend Routes - Redirect to main pages with coming soon message
Route::get('/hotels/listing', function () {
    return redirect()->route('hotels.index')->with('message', 'Hotel listing feature coming soon!');
})->name('hotels.listing');

Route::get('/hotels/booking', function () {
    return redirect()->route('hotels.index')->with('message', 'Hotel booking feature coming soon!');
})->name('hotels.booking');

Route::get('/hotels/detail', function () {
    return redirect()->route('hotels.index')->with('message', 'Hotel details feature coming soon!');
})->name('hotels.detail');

Route::get('/tours/packages', function () {
    return redirect()->route('home')->with('message', 'Tour packages feature coming soon!');
})->name('tours.packages');

Route::get('/tours/detail', function () {
    return redirect()->route('home')->with('message', 'Tour details feature coming soon!');
})->name('tours.detail');

Route::get('/cars/listing', function () {
    return redirect()->route('home')->with('message', 'Car rental feature coming soon!');
})->name('cars.listing');

Route::get('/cars/booking', function () {
    return redirect()->route('home')->with('message', 'Car booking feature coming soon!');
})->name('cars.booking');

Route::get('/cars/detail', function () {
    return redirect()->route('home')->with('message', 'Car details feature coming soon!');
})->name('cars.detail');

Route::get('/blog/detail', function () {
    return redirect()->route('blog.index')->with('message', 'Blog post details coming soon!');
})->name('blog.detail');

Route::get('/blog/listing', function () {
    return redirect()->route('blog.index');
})->name('blog.listing');

// Profile route (for authenticated users)
Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});
