@extends('layouts.admin')

@section('title', 'Manager Dashboard')

@section('content')
<div class="admin-main">
    <!-- Page Header -->
    <div class="page-header">
        <div class="page-title-wrapper">
            <h1 class="page-title">Manager Dashboard</h1>
            <p class="page-subtitle">Flight booking management and analytics</p>
        </div>
    </div>

    <!-- Manager Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="modern-stats-card gradient-primary">
                <div class="stats-content">
                    <div class="stats-header">
                        <div class="stats-icon-modern">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <div class="stats-trend">
                            <span class="trend-value">+{{ number_format((($stats['bookings_today'] / max($stats['total_bookings'], 1)) * 100), 1) }}%</span>
                            <i class="fas fa-arrow-up"></i>
                        </div>
                    </div>
                    <div class="stats-body">
                        <div class="stats-number">{{ number_format($stats['total_bookings']) }}</div>
                        <div class="stats-label">Total Bookings</div>
                        <div class="stats-sublabel">{{ $stats['bookings_today'] }} today</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="modern-stats-card gradient-success">
                <div class="stats-content">
                    <div class="stats-header">
                        <div class="stats-icon-modern">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stats-trend">
                            <span class="trend-value">{{ number_format((($stats['confirmed_bookings'] / max($stats['total_bookings'], 1)) * 100), 1) }}%</span>
                            <i class="fas fa-arrow-up"></i>
                        </div>
                    </div>
                    <div class="stats-body">
                        <div class="stats-number">{{ number_format($stats['confirmed_bookings']) }}</div>
                        <div class="stats-label">Confirmed Bookings</div>
                        <div class="stats-sublabel">{{ $stats['pending_bookings'] }} pending</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="modern-stats-card gradient-info">
                <div class="stats-content">
                    <div class="stats-header">
                        <div class="stats-icon-modern">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stats-trend">
                            <span class="trend-value">+12.5%</span>
                            <i class="fas fa-arrow-up"></i>
                        </div>
                    </div>
                    <div class="stats-body">
                        <div class="stats-number">${{ number_format($stats['total_revenue'], 0) }}</div>
                        <div class="stats-label">Total Revenue</div>
                        <div class="stats-sublabel">${{ number_format($stats['monthly_revenue'], 0) }} this month</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="modern-stats-card gradient-warning">
                <div class="stats-content">
                    <div class="stats-header">
                        <div class="stats-icon-modern">
                            <i class="fas fa-plane"></i>
                        </div>
                        <div class="stats-trend">
                            <span class="trend-value">{{ number_format((($stats['active_flights'] / max($stats['total_flights'], 1)) * 100), 1) }}%</span>
                            <i class="fas fa-arrow-up"></i>
                        </div>
                    </div>
                    <div class="stats-body">
                        <div class="stats-number">{{ number_format($stats['total_flights']) }}</div>
                        <div class="stats-label">Total Flights</div>
                        <div class="stats-sublabel">{{ $stats['active_flights'] }} active</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- PNR Statistics Row -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="modern-stats-card gradient-info">
                <div class="stats-content">
                    <div class="stats-header">
                        <div class="stats-icon-modern">
                            <i class="fas fa-list-alt"></i>
                        </div>
                        <div class="stats-trend">
                            <span class="trend-value">{{ number_format((($stats['available_pnrs'] / max($stats['total_pnrs'], 1)) * 100), 1) }}%</span>
                            <i class="fas fa-arrow-up"></i>
                        </div>
                    </div>
                    <div class="stats-body">
                        <div class="stats-number">{{ number_format($stats['total_pnrs']) }}</div>
                        <div class="stats-label">Total PNRs</div>
                        <div class="stats-sublabel">{{ $stats['available_pnrs'] }} available</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="modern-stats-card gradient-secondary">
                <div class="stats-content">
                    <div class="stats-header">
                        <div class="stats-icon-modern">
                            <i class="fas fa-chair"></i>
                        </div>
                        <div class="stats-trend">
                            <span class="trend-value">{{ number_format((($stats['total_live_seats'] / max($stats['total_inventory_seats'], 1)) * 100), 1) }}%</span>
                            <i class="fas fa-arrow-up"></i>
                        </div>
                    </div>
                    <div class="stats-body">
                        <div class="stats-number">{{ number_format($stats['total_inventory_seats']) }}</div>
                        <div class="stats-label">Inventory Seats</div>
                        <div class="stats-sublabel">{{ number_format($stats['total_live_seats']) }} live</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="modern-stats-card gradient-success">
                <div class="stats-content">
                    <div class="stats-header">
                        <div class="stats-icon-modern">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <div class="stats-trend">
                            <span class="trend-value">{{ number_format((($stats['total_booked_seats'] / max($stats['total_live_seats'], 1)) * 100), 1) }}%</span>
                            <i class="fas fa-arrow-up"></i>
                        </div>
                    </div>
                    <div class="stats-body">
                        <div class="stats-number">{{ number_format($stats['total_booked_seats']) }}</div>
                        <div class="stats-label">Booked Seats</div>
                        <div class="stats-sublabel">{{ number_format($stats['total_live_seats'] - $stats['total_booked_seats']) }} available</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="modern-stats-card gradient-danger">
                <div class="stats-content">
                    <div class="stats-header">
                        <div class="stats-icon-modern">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stats-trend">
                            <span class="trend-value">{{ number_format((($stats['sold_out_pnrs'] / max($stats['total_pnrs'], 1)) * 100), 1) }}%</span>
                            <i class="fas fa-arrow-up"></i>
                        </div>
                    </div>
                    <div class="stats-body">
                        <div class="stats-number">{{ number_format($stats['sold_out_pnrs']) }}</div>
                        <div class="stats-label">Sold Out PNRs</div>
                        <div class="stats-sublabel">{{ $stats['inventory_only_pnrs'] }} inventory only</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- PNR Management Section -->
    <div class="modern-widget mb-4">
        <div class="widget-header">
            <h3 class="widget-title">
                <i class="fas fa-list-alt me-2"></i>PNR Management
            </h3>
            <p class="widget-subtitle">Quick access to PNR operations</p>
        </div>
        <div class="widget-body">
            <div class="row g-3">
                <div class="col-md-4">
                    <a href="{{ route('admin.pnr.index') }}" class="manager-quick-action">
                        <div class="quick-action-icon">
                            <i class="fas fa-list"></i>
                        </div>
                        <div class="quick-action-content">
                            <h5>View All PNRs</h5>
                            <p>Manage passenger name records</p>
                        </div>
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="{{ route('admin.pnr.create') }}" class="manager-quick-action">
                        <div class="quick-action-icon">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div class="quick-action-content">
                            <h5>Create New PNR</h5>
                            <p>Add new passenger record</p>
                        </div>
                    </a>
                </div>
                <div class="col-md-4">
                    <button class="manager-quick-action" onclick="refreshPnrStats()">
                        <div class="quick-action-icon">
                            <i class="fas fa-sync-alt"></i>
                        </div>
                        <div class="quick-action-content">
                            <h5>Refresh Stats</h5>
                            <p>Update PNR statistics</p>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Flight Search Filters -->
    <div class="modern-widget mb-4">
        <div class="widget-header">
            <h3 class="widget-title">
                <i class="fas fa-filter me-2"></i>Flight Search & Filters
            </h3>
            <p class="widget-subtitle">Search and filter flight bookings</p>
        </div>
        <div class="widget-body">
            <form id="flight-filter-form" class="manager-filter-form">
                <div class="row g-3">
                    <!-- Departure Airport -->
                    <div class="col-md-3">
                        <label class="booking-label">
                            <i class="fas fa-plane-departure me-1"></i>From
                        </label>
                        <div class="airport-autocomplete-container">
                            <input type="text" 
                                   class="booking-input airport-autocomplete" 
                                   id="departure_airport" 
                                   name="departure_airport"
                                   placeholder="Enter departure city or airport"
                                   autocomplete="off">
                            <div class="airport-suggestions" id="departure_suggestions"></div>
                        </div>
                    </div>

                    <!-- Arrival Airport -->
                    <div class="col-md-3">
                        <label class="booking-label">
                            <i class="fas fa-plane-arrival me-1"></i>To
                        </label>
                        <div class="airport-autocomplete-container">
                            <input type="text" 
                                   class="booking-input airport-autocomplete" 
                                   id="arrival_airport" 
                                   name="arrival_airport"
                                   placeholder="Enter destination city or airport"
                                   autocomplete="off">
                            <div class="airport-suggestions" id="arrival_suggestions"></div>
                        </div>
                    </div>

                    <!-- Date Range -->
                    <div class="col-md-2">
                        <label class="booking-label">
                            <i class="fas fa-calendar me-1"></i>Departure Date
                        </label>
                        <input type="date" 
                               class="booking-input" 
                               id="departure_date" 
                               name="departure_date">
                    </div>

                    <!-- Passenger Count -->
                    <div class="col-md-2">
                        <label class="booking-label">
                            <i class="fas fa-users me-1"></i>Passengers
                        </label>
                        <select class="booking-select" id="passenger_count" name="passenger_count">
                            <option value="">All</option>
                            <option value="1">1 Passenger</option>
                            <option value="2">2 Passengers</option>
                            <option value="3">3 Passengers</option>
                            <option value="4">4+ Passengers</option>
                        </select>
                    </div>

                    <!-- Search Button -->
                    <div class="col-md-2">
                        <label class="booking-label">&nbsp;</label>
                        <button type="submit" class="booking-search-btn">
                            <i class="fas fa-search me-2"></i>Search
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Flight Bookings Table -->
    <div class="modern-widget">
        <div class="widget-header">
            <h3 class="widget-title">
                <i class="fas fa-list me-2"></i>Flight Bookings Management
            </h3>
            <p class="widget-subtitle">Comprehensive flight and booking data</p>
        </div>
        <div class="widget-body">
            <div class="table-responsive">
                <table class="table manager-flights-table" id="flights-table">
                    <thead>
                        <tr>
                            <th>Flight</th>
                            <th>Route</th>
                            <th>Schedule</th>
                            <th>Bookings</th>
                            <th>Revenue</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($flights as $flight)
                        <tr class="flight-row" 
                            data-departure="{{ $flight->departureAirport->iata_code ?? '' }}"
                            data-arrival="{{ $flight->arrivalAirport->iata_code ?? '' }}"
                            data-date="{{ $flight->departure_time->format('Y-m-d') }}"
                            data-passengers="{{ $flight->bookings->sum('total_passengers') }}">
                            <td>
                                <div class="flight-info">
                                    <div class="flight-number">{{ $flight->flight_number }}</div>
                                    <div class="airline-name">{{ $flight->airline->name ?? 'N/A' }}</div>
                                </div>
                            </td>
                            <td>
                                <div class="route-info">
                                    <div class="route-airports">
                                        <span class="airport-code">{{ $flight->departureAirport->iata_code ?? 'N/A' }}</span>
                                        <i class="fas fa-arrow-right mx-2"></i>
                                        <span class="airport-code">{{ $flight->arrivalAirport->iata_code ?? 'N/A' }}</span>
                                    </div>
                                    <div class="route-cities">
                                        {{ $flight->departureAirport->city ?? 'N/A' }} → {{ $flight->arrivalAirport->city ?? 'N/A' }}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="schedule-info">
                                    <div class="departure-time">
                                        <i class="fas fa-plane-departure me-1"></i>
                                        {{ $flight->departure_time->format('M d, Y H:i') }}
                                    </div>
                                    <div class="arrival-time">
                                        <i class="fas fa-plane-arrival me-1"></i>
                                        {{ $flight->arrival_time->format('M d, Y H:i') }}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="booking-stats">
                                    <div class="booking-count">
                                        <i class="fas fa-ticket-alt me-1"></i>
                                        {{ $flight->bookings->count() }} bookings
                                    </div>
                                    <div class="passenger-count">
                                        <i class="fas fa-users me-1"></i>
                                        {{ $flight->bookings->sum('total_passengers') }} passengers
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="revenue-info">
                                    <div class="total-revenue">
                                        ${{ number_format($flight->bookings->where('payment_status', 'Paid')->sum('total_amount'), 2) }}
                                    </div>
                                    <div class="avg-price">
                                        Avg: ${{ number_format($flight->bookings->where('payment_status', 'Paid')->avg('total_amount') ?? 0, 0) }}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="status-badge status-{{ strtolower($flight->status) }}">
                                    {{ $flight->status }}
                                </span>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="{{ route('admin.flights.show', $flight->id) }}" 
                                       class="btn btn-sm btn-outline-primary" 
                                       title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.flights.edit', $flight->id) }}" 
                                       class="btn btn-sm btn-outline-secondary" 
                                       title="Edit Flight">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <div class="empty-state">
                                    <i class="fas fa-plane-slash fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No flights found</h5>
                                    <p class="text-muted">Try adjusting your search criteria</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($flights->hasPages())
            <div class="d-flex justify-content-center mt-4">
                {{ $flights->links() }}
            </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* Manager Dashboard Specific Styles */
.manager-filter-form {
    background: linear-gradient(135deg, rgba(248, 248, 255, 0.5) 0%, rgba(255, 255, 255, 0.8) 100%);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    border: 1px solid rgba(77, 115, 252, 0.1);
}

/* Airport Autocomplete */
.airport-autocomplete-container {
    position: relative;
}

.airport-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    box-shadow: var(--shadow-secondary);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
    display: none;
}

.airport-suggestion {
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.2s ease;
}

.airport-suggestion:hover,
.airport-suggestion.active {
    background: linear-gradient(135deg, var(--primary-blue-light) 0%, rgba(255, 255, 255, 0.9) 100%);
    color: white;
}

.airport-suggestion:last-child {
    border-bottom: none;
}

.suggestion-main {
    font-weight: 600;
    color: var(--text-primary);
}

.suggestion-details {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

.airport-suggestion:hover .suggestion-main,
.airport-suggestion.active .suggestion-main {
    color: white;
}

.airport-suggestion:hover .suggestion-details,
.airport-suggestion.active .suggestion-details {
    color: rgba(255, 255, 255, 0.9);
}

/* Manager Flights Table */
.manager-flights-table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-secondary);
}

.manager-flights-table thead {
    background: var(--gradient-blue-white);
    color: white;
}

.manager-flights-table thead th {
    border: none;
    padding: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

.manager-flights-table tbody tr {
    border-bottom: 1px solid rgba(77, 115, 252, 0.1);
    transition: all 0.3s ease;
}

.manager-flights-table tbody tr:hover {
    background: linear-gradient(135deg, rgba(248, 248, 255, 0.5) 0%, rgba(255, 255, 255, 0.8) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(77, 115, 252, 0.1);
}

.manager-flights-table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border: none;
}

/* Flight Info Styles */
.flight-info .flight-number {
    font-weight: 700;
    color: var(--primary-blue);
    font-size: 1.1rem;
}

.flight-info .airline-name {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.route-info .route-airports {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.route-info .airport-code {
    background: linear-gradient(135deg, var(--primary-blue-light) 0%, var(--primary-blue) 100%);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 600;
}

.route-info .route-cities {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.schedule-info div {
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}

.schedule-info .departure-time {
    color: var(--primary-blue);
    font-weight: 600;
}

.schedule-info .arrival-time {
    color: var(--text-secondary);
}

.booking-stats div {
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}

.booking-stats .booking-count {
    color: var(--primary-blue);
    font-weight: 600;
}

.booking-stats .passenger-count {
    color: var(--text-secondary);
}

.revenue-info .total-revenue {
    font-weight: 700;
    color: var(--success-color);
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
}

.revenue-info .avg-price {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Status Badges */
.status-badge {
    padding: 0.375rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-scheduled {
    background: linear-gradient(135deg, var(--success-color) 0%, #34d399 100%);
    color: white;
}

.status-delayed {
    background: linear-gradient(135deg, var(--warning-color) 0%, #fbbf24 100%);
    color: white;
}

.status-cancelled {
    background: linear-gradient(135deg, var(--danger-color) 0%, #f87171 100%);
    color: white;
}

.status-completed {
    background: linear-gradient(135deg, var(--info-color) 0%, #38bdf8 100%);
    color: white;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-buttons .btn {
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.action-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-secondary);
}

/* Empty State */
.empty-state {
    padding: 3rem 2rem;
}

.empty-state i {
    color: var(--text-light);
}

/* Responsive Design */
@media (max-width: 768px) {
    .manager-filter-form .row {
        gap: 1rem;
    }

    .manager-filter-form .col-md-3,
    .manager-filter-form .col-md-2 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .manager-flights-table {
        font-size: 0.875rem;
    }

    .manager-flights-table thead th,
    .manager-flights-table tbody td {
        padding: 0.75rem 0.5rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Airport data (sample - in production this would come from an API)
    const airports = [
        { code: 'DXB', name: 'Dubai International Airport', city: 'Dubai', country: 'UAE' },
        { code: 'AUH', name: 'Abu Dhabi International Airport', city: 'Abu Dhabi', country: 'UAE' },
        { code: 'SHJ', name: 'Sharjah International Airport', city: 'Sharjah', country: 'UAE' },
        { code: 'LHR', name: 'London Heathrow Airport', city: 'London', country: 'UK' },
        { code: 'JFK', name: 'John F. Kennedy International Airport', city: 'New York', country: 'USA' },
        { code: 'LAX', name: 'Los Angeles International Airport', city: 'Los Angeles', country: 'USA' },
        { code: 'CDG', name: 'Charles de Gaulle Airport', city: 'Paris', country: 'France' },
        { code: 'FRA', name: 'Frankfurt Airport', city: 'Frankfurt', country: 'Germany' },
        { code: 'NRT', name: 'Narita International Airport', city: 'Tokyo', country: 'Japan' },
        { code: 'SIN', name: 'Singapore Changi Airport', city: 'Singapore', country: 'Singapore' },
        { code: 'BOM', name: 'Chhatrapati Shivaji International Airport', city: 'Mumbai', country: 'India' },
        { code: 'DEL', name: 'Indira Gandhi International Airport', city: 'Delhi', country: 'India' }
    ];

    // Airport Autocomplete Functionality
    function initializeAirportAutocomplete(inputId, suggestionsId) {
        const input = document.getElementById(inputId);
        const suggestionsContainer = document.getElementById(suggestionsId);
        let currentFocus = -1;
        let debounceTimer;

        input.addEventListener('input', function() {
            clearTimeout(debounceTimer);
            debounceTimer = setTimeout(() => {
                const query = this.value.toLowerCase().trim();

                if (query.length === 0) {
                    hideSuggestions();
                    return;
                }

                const filteredAirports = airports.filter(airport =>
                    airport.code.toLowerCase().includes(query) ||
                    airport.name.toLowerCase().includes(query) ||
                    airport.city.toLowerCase().includes(query) ||
                    airport.country.toLowerCase().includes(query)
                ).slice(0, 8);

                showSuggestions(filteredAirports);
            }, 300);
        });

        input.addEventListener('keydown', function(e) {
            const suggestions = suggestionsContainer.querySelectorAll('.airport-suggestion');

            if (e.key === 'ArrowDown') {
                e.preventDefault();
                currentFocus = Math.min(currentFocus + 1, suggestions.length - 1);
                updateActiveSuggestion(suggestions);
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                currentFocus = Math.max(currentFocus - 1, -1);
                updateActiveSuggestion(suggestions);
            } else if (e.key === 'Enter') {
                e.preventDefault();
                if (currentFocus >= 0 && suggestions[currentFocus]) {
                    selectAirport(suggestions[currentFocus]);
                }
            } else if (e.key === 'Escape') {
                hideSuggestions();
            }
        });

        input.addEventListener('blur', function() {
            setTimeout(() => hideSuggestions(), 150);
        });

        function showSuggestions(filteredAirports) {
            if (filteredAirports.length === 0) {
                hideSuggestions();
                return;
            }

            suggestionsContainer.innerHTML = '';
            currentFocus = -1;

            filteredAirports.forEach((airport, index) => {
                const suggestionDiv = document.createElement('div');
                suggestionDiv.className = 'airport-suggestion';
                suggestionDiv.innerHTML = `
                    <div class="suggestion-main">${airport.code} - ${airport.name}</div>
                    <div class="suggestion-details">${airport.city}, ${airport.country}</div>
                `;

                suggestionDiv.addEventListener('click', () => selectAirport(suggestionDiv));
                suggestionsContainer.appendChild(suggestionDiv);
            });

            suggestionsContainer.style.display = 'block';
        }

        function hideSuggestions() {
            suggestionsContainer.style.display = 'none';
            currentFocus = -1;
        }

        function updateActiveSuggestion(suggestions) {
            suggestions.forEach((suggestion, index) => {
                suggestion.classList.toggle('active', index === currentFocus);
            });
        }

        function selectAirport(suggestionElement) {
            const mainText = suggestionElement.querySelector('.suggestion-main').textContent;
            const airportCode = mainText.split(' - ')[0];
            const airportName = mainText.split(' - ')[1];

            input.value = `${airportCode} - ${airportName}`;
            input.dataset.selectedCode = airportCode;
            hideSuggestions();
        }
    }

    // Initialize autocomplete for both inputs
    initializeAirportAutocomplete('departure_airport', 'departure_suggestions');
    initializeAirportAutocomplete('arrival_airport', 'arrival_suggestions');

    // Flight Filter Form
    const filterForm = document.getElementById('flight-filter-form');
    const flightRows = document.querySelectorAll('.flight-row');

    filterForm.addEventListener('submit', function(e) {
        e.preventDefault();
        applyFilters();
    });

    // Real-time filtering on input change
    filterForm.addEventListener('input', function() {
        clearTimeout(this.filterTimeout);
        this.filterTimeout = setTimeout(applyFilters, 500);
    });

    function applyFilters() {
        const departureInput = document.getElementById('departure_airport');
        const arrivalInput = document.getElementById('arrival_airport');
        const dateInput = document.getElementById('departure_date');
        const passengerSelect = document.getElementById('passenger_count');

        const filters = {
            departure: departureInput.dataset.selectedCode || '',
            arrival: arrivalInput.dataset.selectedCode || '',
            date: dateInput.value,
            passengers: passengerSelect.value
        };

        let visibleCount = 0;

        flightRows.forEach(row => {
            let shouldShow = true;

            // Departure airport filter
            if (filters.departure && row.dataset.departure !== filters.departure) {
                shouldShow = false;
            }

            // Arrival airport filter
            if (filters.arrival && row.dataset.arrival !== filters.arrival) {
                shouldShow = false;
            }

            // Date filter
            if (filters.date && row.dataset.date !== filters.date) {
                shouldShow = false;
            }

            // Passenger count filter
            if (filters.passengers) {
                const rowPassengers = parseInt(row.dataset.passengers);
                const filterPassengers = parseInt(filters.passengers);

                if (filters.passengers === '4' && rowPassengers < 4) {
                    shouldShow = false;
                } else if (filters.passengers !== '4' && rowPassengers !== filterPassengers) {
                    shouldShow = false;
                }
            }

            row.style.display = shouldShow ? '' : 'none';
            if (shouldShow) visibleCount++;
        });

        // Update results count or show empty state
        updateResultsDisplay(visibleCount);
    }

    function updateResultsDisplay(count) {
        // You can add a results counter here if needed
        console.log(`Showing ${count} flights`);
    }

    // Table sorting functionality
    const tableHeaders = document.querySelectorAll('.manager-flights-table thead th');
    tableHeaders.forEach((header, index) => {
        if (index < tableHeaders.length - 1) { // Exclude actions column
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => sortTable(index));
        }
    });

    function sortTable(columnIndex) {
        const table = document.querySelector('.manager-flights-table tbody');
        const rows = Array.from(table.querySelectorAll('tr:not(.empty-state)'));

        if (rows.length === 0) return;

        const isAscending = !table.dataset.sortAsc || table.dataset.sortAsc === 'false';
        table.dataset.sortAsc = isAscending;

        rows.sort((a, b) => {
            const aText = a.cells[columnIndex].textContent.trim();
            const bText = b.cells[columnIndex].textContent.trim();

            // Try to parse as numbers first
            const aNum = parseFloat(aText.replace(/[^0-9.-]/g, ''));
            const bNum = parseFloat(bText.replace(/[^0-9.-]/g, ''));

            if (!isNaN(aNum) && !isNaN(bNum)) {
                return isAscending ? aNum - bNum : bNum - aNum;
            }

            // Fall back to string comparison
            return isAscending ? aText.localeCompare(bText) : bText.localeCompare(aText);
        });

        // Re-append sorted rows
        rows.forEach(row => table.appendChild(row));
    }
});
</script>
@endpush
