@extends('layouts.frontend')

@section('title', 'FlyNow | Travling And Tours Template')
@section('description', 'FlyNow HTML5 Template')

@section('content')
<!-- Hero Banner start -->
<section class="hero-banner-1">
    <div class="container-fluid">
        <div class="content">
            <div class="vector-image">
                <svg xmlns="http://www.w3.org/2000/svg" width="1414" height="319" viewBox="0 0 1414 319" fill="none">
                    <path class="path"
                        d="M-0.5 215C62.4302 220.095 287 228 373 143.5C444.974 72.7818 368.5 -3.73136 320.5 1.99997C269.5 8.08952 231.721 43.5 253.5 119C275.279 194.5 367 248.212 541.5 207.325C675.76 175.867 795.5 82.7122 913 76.7122C967.429 73.9328 1072.05 88.6813 1085 207.325C1100 344.712 882 340.212 922.5 207.325C964.415 69.7967 1354 151.5 1479 183.5" 
                        stroke="#ECECF2" stroke-width="6" stroke-linecap="round" stroke-dasharray="round"/>
                    
                    <path class="dashed"
                        d="M-0.5 215C62.4302 220.095 287 228 373 143.5C444.974 72.7818 368.5 -3.73136 320.5 1.99997C269.5 8.08952 231.721 43.5 253.5 119C275.279 194.5 367 248.212 541.5 207.325C675.76 175.867 795.5 82.7122 913 76.7122C967.429 73.9328 1072.05 88.6813 1085 207.325C1100 344.712 882 340.212 922.5 207.325C964.415 69.7967 1354 151.5 1479 183.5" 
                        stroke="#212627" stroke-width="6" stroke-linecap="round" stroke-dasharray="22 22"/>
                </svg>
                <div class="location-image">
                    <img src="{{ asset('assets/media/icons/location-blue.png') }}" alt="">
                </div>
            </div>
            <div class="row align-items-center row-gap-5">
                <div class="col-xxl-3 col-xl-4 col-lg-4 col-md-5 col-sm-5">
                    <div class="content-block">
                        <h1 class="lightest-black mb-16"><span class="color-primary">Book</span> Your Dream <span class="color-primary">Flights</span> Now!</h1>
                        <p class="dark-gray mb-24">Lorem ipsum dolor sit amet consectetur. Felis tristique pretium leo nisi at risus ac enim.</p>
                        <a href="{{ route('flights.booking') }}" class="cus-btn">Book Now</a>
                    </div>
                </div>
                <div class="col-xxl-9 col-xl-8 col-lg-8 col-md-7 col-sm-7">
                    <div class="image flynow-tilt"
                        data-tilt-options='{ "glare": false, "maxGlare": 0, "maxTilt": 3, "speed": 700, "scale": 1.02 }'>
                        <img src="{{ asset('assets/media/banner/plane.png') }}" alt="">
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- Hero Banner End -->

<!-- Booking Area Start -->
<section class="booking mb-20">
    <div class="container-fluid">
        <div class="content">
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" data-bs-tabs="tabs">
                        <li class="nav-item">
                            <a href="#flight" class="cus-btn primary active" aria-current="true" data-bs-toggle="tab">
                                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
                                    <g clip-path="url(#clip0_502_1331)">
                                        <path d="M31.6933 0.544584C30.6572 -0.491824 27.1402 1.34503 26.1041 2.38143L21.9545 6.53127H3.07887C2.63024 6.53127 2.24462 6.85011 2.16055 7.29104C2.07669 7.73189 2.31798 8.16995 2.73524 8.3348L15.2174 13.2677L7.5633 20.9216H0.323909C0.168651 20.9221 0.0346723 21.0323 0.00576263 21.1852C-0.023357 21.3385 0.060152 21.4901 0.20498 21.5471L6.29687 23.9548C6.33201 24.1078 6.38108 24.2574 6.44394 24.4038L6.17745 24.6709C5.79778 25.0503 5.79778 25.6651 6.17745 26.045C6.55664 26.4247 7.17263 26.4247 7.55182 26.045L7.81194 25.785C7.95935 25.8501 8.11132 25.9014 8.26623 25.9382L10.6144 31.9561C10.6709 32.1013 10.8229 32.1851 10.976 32.1568C11.0419 32.145 11.1002 32.1123 11.1451 32.0673C11.2044 32.0087 11.2399 31.9274 11.2399 31.8382V24.7512L19.0155 16.976L23.9019 29.4993C24.0654 29.9177 24.5037 30.1608 24.9452 30.0781C25.136 30.0421 25.3038 29.9498 25.4333 29.8212C25.6038 29.6499 25.7071 29.4151 25.7077 29.1591V10.284L29.8573 6.13423C30.893 5.09789 32.7293 1.58085 31.6933 0.544584Z" fill="#16191A"/>
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_502_1331">
                                            <rect width="32" height="32" fill="white"/>
                                        </clipPath>
                                    </defs>
                                </svg>
                                Flights
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#hotel" class="cus-btn primary" aria-current="false" data-bs-toggle="tab">
                                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
                                    <g clip-path="url(#clip0_502_1334)">
                                        <path d="M10.7589 9.00703H12.6339V10.882H10.7589V9.00703Z" fill="#16191A"/>
                                        <path d="M10.7589 13.3106H12.6339V15.1856H10.7589V13.3106Z" fill="#16191A"/>
                                        <path d="M19.3661 13.3106H21.2411V15.1856H19.3661V13.3106Z" fill="#16191A"/>
                                        <path d="M23.6696 13.3106H25.5446V15.1856H23.6696V13.3106Z" fill="#16191A"/>
                                        <path d="M19.3661 17.6142H21.2411V19.4892H19.3661V17.6142Z" fill="#16191A"/>
                                        <path d="M23.6696 17.6142H25.5446V19.4892H23.6696V17.6142Z" fill="#16191A"/>
                                        <path d="M19.3661 21.9178H21.2411V23.7928H19.3661V21.9178Z" fill="#16191A"/>
                                        <path d="M23.6696 21.9178H25.5446V23.7928H23.6696V21.9178Z" fill="#16191A"/>
                                        <path d="M19.3661 26.2213H21.2411V28.0963H19.3661V26.2213Z" fill="#16191A"/>
                                        <path d="M23.6696 26.2213H25.5446V28.0963H23.6696V26.2213Z" fill="#16191A"/>
                                        <path d="M6.45538 13.3106H8.33037V15.1856H6.45538V13.3106Z" fill="#16191A"/>
                                        <path d="M10.7589 17.6142H12.6339V19.4892H10.7589V17.6142Z" fill="#16191A"/>
                                        <path d="M6.45538 17.6142H8.33037V19.4892H6.45538V17.6142Z" fill="#16191A"/>
                                        <path d="M10.7589 21.9178H12.6339V23.7928H10.7589V21.9178Z" fill="#16191A"/>
                                        <path d="M6.45538 21.9178H8.33037V23.7928H6.45538V21.9178Z" fill="#16191A"/>
                                        <path d="M29.8482 30.5249V9.00703H16.9375V4.53428L14.7857 4.8929V0.399902H4.30356V6.63997L2.15181 6.99859V30.5249H0V32.3999H32V30.5249H29.8482ZM27.9732 10.882V30.5249H16.9375V10.882H27.9732ZM6.17856 2.2749H12.9107V5.2054L6.17856 6.32747V2.2749ZM4.02681 8.58696L15.0625 6.74765V30.5249H12.6339V26.2213H6.45538V30.5249H4.02681V8.58696ZM10.7589 30.5249H8.33037V28.0963H10.7589V30.5249Z" fill="#16191A"/>
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_502_1334">
                                            <rect width="32" height="32" fill="white"/>
                                        </clipPath>
                                    </defs>
                                </svg>
                                Hotel
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#rental" class="cus-btn primary" aria-current="false" data-bs-toggle="tab">
                                <svg xmlns="http://www.w3.org/2000/svg" width="32" class="stroke_svg" height="32" viewBox="0 0 32 32" fill="none">
                                    <path d="M28.2644 18.7196L28.2966 18.5427C28.3266 18.3779 28.343 18.2088 28.343 18.0363C28.343 16.4794 27.0761 15.2125 25.5192 15.2125C24.4705 15.2125 23.5536 15.7877 23.0664 16.64L23.0232 16.7155H22.9362H12.1135C11.7975 16.7155 11.4996 16.5921 11.2761 16.3687L11.2761 16.3686L10.2133 15.3059C10.0888 15.1814 9.88701 15.1814 9.7626 15.3059L9.76258 15.3059C9.63809 15.4304 9.63812 15.6322 9.76257 15.7566L9.76258 15.7566L10.8253 16.8194C11.1697 17.1637 11.6266 17.353 12.1135 17.353H22.5942H22.774L22.7418 17.5299C22.7118 17.6947 22.6954 17.8637 22.6954 18.0363C22.6954 18.2088 22.7118 18.3779 22.7418 18.5427L22.774 18.7195H22.5942H8.90483H8.72508L8.75725 18.5427C8.78724 18.3779 8.80364 18.2088 8.80364 18.0363C8.80364 16.4793 7.53674 15.2124 5.97982 15.2124C4.47017 15.2124 3.23316 16.4038 3.15957 17.8955L3.14984 18.0929L2.96232 18.0304L1.59728 17.5754C1.113 17.414 0.787502 16.9624 0.787502 16.4519V13.8156V13.6857L0.916062 13.6672L9.49777 12.4279L9.66921 12.4031V12.5763V12.6581C9.66921 13.3107 10.1572 13.8733 10.8032 13.9656C10.8032 13.9656 10.8032 13.9656 10.8032 13.9656L11.5076 14.0662C11.5232 14.0684 11.5385 14.0695 11.5535 14.0695C11.7094 14.0695 11.8457 13.9546 11.8685 13.7958C11.8933 13.6215 11.7722 13.4601 11.598 13.4352L10.8934 13.3346L10.8934 13.3346C10.5586 13.2867 10.3068 12.9965 10.3068 12.6582V12.441V12.3111L10.4353 12.2925L13.424 11.8609C15.7407 11.5263 18.1201 11.7936 20.3049 12.6339L22.5295 13.4896L22.9124 13.6369L22.5248 13.7713C21.9087 13.9849 21.3819 14.2208 21.0063 14.4052C20.6196 14.595 20.1846 14.6619 19.7492 14.5996C19.7492 14.5996 19.7492 14.5996 19.7492 14.5996L13.5402 13.7126L28.2644 18.7196ZM28.2644 18.7196H28.4442H30.5293C30.9062 18.7196 31.2126 18.4132 31.2126 18.0363V17.002V16.852H31.0626H30.0284C29.8523 16.852 29.7096 16.7093 29.7096 16.5333C29.7096 16.3572 29.8523 16.2145 30.0284 16.2145H30.6604H31.0998L30.7521 15.9458L30.7458 15.9408C30.7428 15.9384 30.7382 15.9347 30.7328 15.9308M28.2644 18.7196L30.7328 15.9308M30.7328 15.9308C29.6872 15.1615 27.5995 13.8459 25.5193 13.8459C23.837 13.8459 22.1412 14.5581 21.2874 14.9774C20.7862 15.2234 20.2234 15.3112 19.6591 15.2307C19.6591 15.2307 19.659 15.2307 19.659 15.2307L13.4499 14.3437L30.7328 15.9308ZM13.1795 13.9831C13.1546 14.1574 13.2757 14.3188 13.4499 14.3437L13.1795 13.9831ZM13.1795 13.9831C13.2043 13.809 13.3655 13.6879 13.5401 13.7126L13.1795 13.9831ZM5.4002 18.6159L5.40018 18.6159C5.27573 18.4915 5.27571 18.2896 5.4002 18.1651L6.1087 17.4566C6.2332 17.3321 6.43502 17.3322 6.55943 17.4566L6.55947 17.4567C6.68392 17.5811 6.68394 17.7829 6.55945 17.9074L5.85095 18.6159L5.85091 18.6159C5.7887 18.6782 5.70738 18.7093 5.62557 18.7093C5.54382 18.7093 5.46246 18.6781 5.4002 18.6159ZM24.9396 17.4566L24.9396 17.4566C25.064 17.3322 25.2658 17.3321 25.3903 17.4566L26.0988 18.1651L26.0988 18.1652C26.2233 18.2896 26.2233 18.4914 26.0988 18.6159L26.0988 18.6159C26.0366 18.6782 25.9552 18.7093 25.8734 18.7093C25.7917 18.7093 25.7103 18.6781 25.6481 18.6159L24.9396 17.9074L24.9395 17.9074C24.8151 17.783 24.8151 17.5811 24.9396 17.4566ZM23.949 13.3531L23.9892 13.3686L24.0314 13.3604C24.5154 13.2665 25.0175 13.2084 25.5192 13.2084C27.763 13.2084 29.9777 14.5839 31.1105 15.4173C31.5733 15.7577 31.8501 16.306 31.85 16.8836V18.0363C31.85 18.7644 31.2574 19.357 30.5292 19.357H28.1022H28.0152L27.972 19.4326C27.4848 20.2848 26.5679 20.86 25.5192 20.86C24.4705 20.86 23.5536 20.2848 23.0664 19.4326L23.0232 19.357H22.9362H8.56283H8.4758L8.43261 19.4326C7.94541 20.2848 7.02852 20.86 5.97982 20.86C4.71522 20.86 3.6414 20.0241 3.28321 18.8751L3.26034 18.8018L3.18744 18.7775L1.39569 18.1802C0.650496 17.9318 0.15 17.2374 0.15 16.4519V13.4098C0.15 13.2513 0.266359 13.1169 0.42319 13.0943C0.423191 13.0943 0.423193 13.0943 0.423195 13.0943L13.3328 11.23L13.3114 11.0816L13.3328 11.23C15.7572 10.8798 18.2474 11.1596 20.5337 12.039L23.949 13.3531ZM3.79351 18.0363C3.79351 19.2419 4.77417 20.2226 5.97982 20.2226C7.18555 20.2226 8.16614 19.2419 8.16614 18.0363C8.16614 16.8306 7.18548 15.8499 5.97982 15.8499C4.77417 15.8499 3.79351 16.8306 3.79351 18.0363ZM23.3329 18.0363C23.3329 19.2419 24.3136 20.2226 25.5192 20.2226C26.7248 20.2226 27.7056 19.2419 27.7056 18.0363C27.7056 16.8306 26.7249 15.8499 25.5192 15.8499C24.3136 15.8499 23.3329 16.8306 23.3329 18.0363Z" fill="#16191A" stroke="#16191A" stroke-width="0.3"/>
                                </svg>
                                Car Rentals
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="card-body tab-content">
                    <div class="tab-pane fade show active" id="flight">
                        <div class="alert alert-info">
                            <h5>Flight Booking System</h5>
                            <p>Our professional flight booking system is coming soon! 
                            @auth
                                You can manage bookings from your <a href="{{ route('admin.dashboard') }}" class="alert-link">admin dashboard</a>.
                            @else
                                Please <a href="{{ route('login') }}" class="alert-link">login</a> to access the booking system.
                            @endauth
                            </p>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="hotel">
                        <div class="alert alert-info">
                            <h5>Hotel Booking System</h5>
                            <p>Hotel booking functionality will be available soon!</p>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="rental">
                        <div class="alert alert-info">
                            <h5>Car Rental System</h5>
                            <p>Car rental functionality will be available soon!</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- Booking Area End -->

<!-- Benefits Start -->
<section class="benefit p-40" data-sal="slide-up" data-sal-duration="800" data-sal-delay="100" data-sal-easing="ease-in-out">
    <div class="container-fluid">
        <div class="row">
            <div class="col-xl-4 col-lg-6 col-md-6 mb-xl-0 mb-24">
                <div class="benefit-block bg-white">
                    <div class="image-box">
                        <img src="{{ asset('assets/media/vector/benefit-1.png') }}" alt="">
                    </div>
                    <div class="text-box">
                        <h4 class="lightest-black mb-8">We are Now Available</h4>
                        <p class="color-medium-gray">Call +1 555 666 888 contact with us</p>
                    </div>
                </div>
            </div>
            <div class="col-xl-4 col-lg-6 col-md-6 mb-lg-0 mb-24">
                <div class="benefit-block bg-white">
                    <div class="image-box">
                        <img src="{{ asset('assets/media/vector/benefit-2.png') }}" alt="">
                    </div>
                    <div class="text-box">
                        <h4 class="lightest-black mb-8">International Flight</h4>
                        <p class="color-medium-gray">Call +1 555 666 888 contact with us</p>
                    </div>
                </div>
            </div>
            <div class="col-xl-4 col-lg-6 col-md-6">
                <div class="benefit-block bg-white">
                    <div class="image-box">
                        <img src="{{ asset('assets/media/vector/benefit-3.png') }}" alt="">
                    </div>
                    <div class="text-box">
                        <h4 class="lightest-black mb-8">Check Refund</h4>
                        <p class="color-medium-gray">Call +1 555 666 888 contact with us</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- Benefits End -->
@endsection
