/*!
 * aksVideoPlayer v1.0.0
 * Video Player
 * MIT License
 */
:root {
    --aks-video-primary: #3CBC1C;
  }
  .aks-video-player {
    position: relative;
    width: auto !important;
    height: 325px !important;
    display: block;
    overflow: hidden;
    margin: 0 auto;
    background: #0e0e0e;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>,
      "Helvetica Neue", <PERSON>l, "Noto Sans", sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  }
  .variable_height .aks-video-player{
    height: 630px !important;
    margin-bottom: 24px;
  }
  @media (max-width: 992px) {
    .variable_height .aks-video-player{
      height: 450px !important;
      margin-bottom: 16px;
    }
  }
  @media (max-width: 767px) {
    .variable_height .aks-video-player{
      height: 350px !important;
    }
  }
  @media (max-width: 575px) {
    .variable_height .aks-video-player{
      height: 300px !important;
    }
  }
  @media (max-width: 492px) {
    .variable_height .aks-video-player{
      height: 250px !important;
    }
  }
  .no_variable_height .aks-video-player{
    height: 325px !important;
  }
  .aks-vp-container {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }
  .aks-vp-poster {
    width: 100%;
    height: 100%;
    background-position: center;
    background-size: cover;
    z-index: 20;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    position: absolute;
    pointer-events: none;
  }
  .aks-vp-start {
    cursor: pointer;
    /* width: 88px;
    height: 88px; */
    /* background: #00000073; */
    /* border-radius: 9999px; */
    text-align: center;
    outline: none;
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
    /* border: 4px solid white; */
    -webkit-transition: all 0.4s ease;
    -o-transition: all 0.4s ease;
    transition: all 0.4s ease;
    position: absolute;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    z-index: 21;
    /* color: white; */
  }
  .aks-vp-start:hover {
    -webkit-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }
  .aks-vp-loading {
    display: none;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    position: absolute;
    z-index: 1;
    background: transparent;
    pointer-events: none;
  }
  .aks-vp-loading-spinner {
    width: 55px;
    height: 55px;
    border: 5px solid #ffffff;
    border-top: 5px solid #9e9e9e;
    border-radius: 50%;
  }
  .aks-vp-loading-spinner {
    -webkit-transition-property: -webkit-transform;
    -webkit-animation-name: rotate;
    -webkit-animation-iteration-count: infinite;
    -webkit-animation-timing-function: linear;
    -moz-transition-property: -moz-transform;
    -moz-animation-name: rotate;
    -moz-animation-duration: 1.2s;
    -moz-animation-iteration-count: infinite;
    -moz-animation-timing-function: linear;
    transition-property: -webkit-transform;
    -o-transition-property: transform;
    transition-property: transform;
    transition-property: transform, -webkit-transform;
    animation-name: rotate;
    -webkit-animation-duration: 1.2s;
    animation-duration: 1.2s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
  }
  @-webkit-keyframes rotate {
    from {
      -webkit-transform: rotate(0deg);
    }
    to {
      -webkit-transform: rotate(360deg);
    }
  }
  @keyframes rotate {
    from {
      -webkit-transform: rotate(0deg);
      transform: rotate(0deg);
    }
    to {
      -webkit-transform: rotate(360deg);
      transform: rotate(360deg);
    }
  }
  .aks-vp-controls {
    position: absolute;
    z-index: 5;
    bottom: 10px;
    width: 100%;
    display: none;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    padding-left: 15px;
    padding-right: 15px;
    -webkit-transition: all 150ms ease;
    -o-transition: all 150ms ease;
    transition: all 150ms ease;
  }
  .aks-vp-controls.aks-active {
    visibility: hidden;
    opacity: 0;
  }
  .aks-vp-slider {
    position: relative;
    height: 30px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
  }
  .aks-vp-range-rail {
    position: relative;
    width: 100%;
    height: 6px;
    background: #212121;
    border-radius: 9999px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    cursor: pointer;
  }
  .aks-vp-range-rail:hover {
    -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, 0.4);
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.4);
  }
  .aks-vp-range-loading {
    position: absolute;
    width: 0px;
    height: 100%;
    background: #ffffff36;
    border-radius: 9999px;
    overflow: hidden;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    cursor: pointer;
  }
  .aks-vp-range-buffer {
    position: absolute;
    width: 0;
    height: 100%;
    background: var(--aks-video-primary);
    border-radius: 9999px;
    overflow: hidden;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    cursor: pointer;
  }
  .aks-vp-range-handler {
    width: 13px;
    height: 13px;
    border-radius: 9999px;
    left: 0;
    background: var(--aks-video-primary);
    position: absolute;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    padding: 0;
    margin: 0;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    outline: none;
    margin-left: -13px;
  }
  .aks-vp-range-handler:active {
    -webkit-box-shadow: 0 0 26px rgba(0, 0, 0, 0.2), 0 0 10px rgb(124 182 255),
      0 0 0 5px rgba(255, 255, 255, 0.2);
    box-shadow: 0 0 26px rgba(0, 0, 0, 0.2), 0 0 10px rgb(124 182 255),
      0 0 0 5px rgba(255, 255, 255, 0.2);
  }
  .aks-vp-row {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    position: relative;
    width: 100%;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
  }
  .aks-vp-wrap {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    position: relative;
    width: -webkit-fit-content;
    width: -moz-fit-content;
    width: fit-content;
  }
  .aks-vp-btn {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    color: white;
    border: none;
    background: transparent;
    outline: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    cursor: pointer;
    -webkit-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
    width: -webkit-fit-content;
    width: -moz-fit-content;
    width: fit-content;
    height: -webkit-fit-content;
    height: -moz-fit-content;
    height: fit-content;
    margin-right: 10px;
    padding: 0;
  }
  .aks-vp-btn:hover {
    color: var(--aks-video-primary);
  }
  .aks-vp-btn:last-child {
    margin-right: 0px;
  }
  .aks-vp-control-pause {
    display: none;
  }
  .aks-vp-voice {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: -webkit-fit-content;
    width: -moz-fit-content;
    width: fit-content;
    position: relative;
  }
  .aks-vp-voice-off {
    display: none;
  }
  .aks-vp-voice-range-rail {
    position: relative;
    width: 65px;
    height: 5px;
    background: #212121;
    border-radius: 9999px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-right: 10px;
  }
  .aks-vp-voice-range-buffer {
    position: absolute;
    width: 0;
    height: 100%;
    background: var(--aks-video-primary);
    border-radius: 9999px;
    overflow: hidden;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    cursor: pointer;
  }
  .aks-vp-voice-range-handler {
    width: 11px;
    height: 11px;
    border-radius: 9999px;
    left: 0;
    background: var(--aks-video-primary);
    position: absolute;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    padding: 0;
    margin: 0;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    outline: none;
  }
  .aks-vp-voice-range-handler:active {
    -webkit-box-shadow: 0 0 26px rgba(0, 0, 0, 0.2), 0 0 10px rgba(0, 0, 0, 4),
      0 0 0 5px rgba(255, 255, 255, 0.2);
    box-shadow: 0 0 26px rgba(0, 0, 0, 0.2), 0 0 10px rgba(0, 0, 0, 4),
      0 0 0 5px rgba(255, 255, 255, 0.2);
  }
  .aks-vp-voice:hover .aks-vp-btn {
    color: var(--aks-video-primary);
  }
  .aks-vp-count {
    color: white;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    line-height: normal;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    pointer-events: none;
  }
  .aks-vp-divider {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-left: 5px;
    margin-right: 5px;
  }
  .aks-vp-full-screen-exit {
    display: none;
  }
  .aks-video {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
  .aks-video video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: relative;
    outline: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-transition: all 350ms ease;
    -o-transition: all 350ms ease;
    transition: all 350ms ease;
  }
  .aks-vp-gradient-bottom {
    height: 210px;
    padding-top: 57px;
    bottom: 0;
    z-index: 1;
    background-position: bottom;
    background-repeat: repeat-x;
    position: absolute;
    width: 100%;
    -webkit-transition: opacity 0.25s cubic-bezier(0, 0, 0.2, 1);
    -o-transition: opacity 0.25s cubic-bezier(0, 0, 0.2, 1);
    transition: opacity 0.25s cubic-bezier(0, 0, 0.2, 1);
    pointer-events: none;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAACfCAYAAAA4ebO6AAAA9klEQVQ4T2XI10cFAABG8dvee++67b33uM17b1MkkSSSSBJJJIkkkkQSSSKJ9EfmvJyXvoef4wsE/i2GJ1biqHhJoBIliUqWFCpV0qh0yaAyJYvKlhwqV/KofCmgCqWIKpYSqlTKqHKpoCqliqqWGiootVSd1FMN0kg1QbO0UK3SRrVLB9UpXVS39FC90kf1ywA1KEPUsIxQozJGjcsEFZJJakqmqRmZpeYkTEUkSs3LArUoS9QyrMAqrME6bMAmbME27MAu7ME+HMAhHMExnMApnME5XMAlXME13MAt3ME9PMAjPMEzvMArvME7fMAnfME3/MDvHz/hJc40WBS4AAAAAElFTkSuQmCC);
  }
  
  [aks-tooltip] {
    position: relative;
  }
  [aks-tooltip]::before,
  [aks-tooltip]::after {
    text-transform: none;
    font-size: 12px;
    line-height: 1;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    pointer-events: none;
    position: absolute;
    display: none;
    opacity: 0;
  }
  [aks-tooltip]::before {
    content: "";
    border: 5px solid transparent;
    z-index: 1001;
  }
  [aks-tooltip]::after {
    content: attr(aks-tooltip);
    font-family: "Segoe UI", Arial, sans-serif;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
    padding: 5px 8px;
    border-radius: 5px;
    -webkit-box-shadow: 0 1em 2em -0.5em rgba(0, 0, 0, 0.35);
    box-shadow: 0 1em 2em -0.5em rgba(0, 0, 0, 0.35);
    background: white;
    color: #000;
    z-index: 1000;
  }
  [aks-tooltip]:hover::before,
  [aks-tooltip]:hover::after {
    display: block;
  }
  [aks-tooltip=""]::before,
  [aks-tooltip=""]::after {
    display: none !important;
  }
  [aks-tooltip]:not([flow])::before,
  [aks-tooltip][flow^="up"]::before {
    bottom: calc(100% + 31px);
    border-bottom-width: 0;
    border-top-color: #fff;
  }
  [aks-tooltip]:not([flow])::after,
  [aks-tooltip][flow^="up"]::after {
    bottom: calc(100% + 36px);
  }
  [aks-tooltip]:not([flow])::before,
  [aks-tooltip]:not([flow])::after,
  [aks-tooltip][flow^="up"]::before,
  [aks-tooltip][flow^="up"]::after {
    left: 50%;
    -webkit-transform: translate(-50%, -0.5em);
    -ms-transform: translate(-50%, -0.5em);
    transform: translate(-50%, -0.5em);
  }
  [aks-tooltip]:not([flow])::before,
  [aks-tooltip][flow^="up-left"]::before {
    bottom: 100%;
    border-bottom-width: 0;
    border-top-color: #111;
  }
  [aks-tooltip]:not([flow])::after,
  [aks-tooltip][flow^="up-left"]::after {
    bottom: calc(100% + 5px);
  }
  [aks-tooltip]:not([flow])::before,
  [aks-tooltip][flow^="up-left"]::before {
    left: 25%;
  }
  [aks-tooltip]:not([flow])::after,
  [aks-tooltip][flow^="up-left"]::after {
    left: 50%;
  }
  [aks-tooltip]:not([flow])::before,
  [aks-tooltip][flow^="up-right"]::before {
    bottom: 100%;
    border-bottom-width: 0;
    border-top-color: #111;
  }
  [aks-tooltip]:not([flow])::after,
  [aks-tooltip][flow^="up-right"]::after {
    bottom: calc(100% + 5px);
  }
  [aks-tooltip]:not([flow])::before,
  [aks-tooltip][flow^="up-right"]::before {
    left: 75%;
    -webkit-transform: translate(-50%, -0.5em);
    -ms-transform: translate(-50%, -0.5em);
    transform: translate(-50%, -0.5em);
  }
  [aks-tooltip]:not([flow])::after,
  [aks-tooltip][flow^="up-right"]::after {
    left: 50%;
    -webkit-transform: translate(-50%, -0.5em);
    -ms-transform: translate(-50%, -0.5em);
    transform: translate(-50%, -0.5em);
  }
  [aks-tooltip][flow^="down"]::before {
    top: 100%;
    border-top-width: 0;
    border-bottom-color: #020204;
  }
  [aks-tooltip][flow^="down"]::after {
    top: calc(100% + 5px);
  }
  [aks-tooltip][flow^="down"]::before,
  [aks-tooltip][flow^="down"]::after {
    left: 50%;
    -webkit-transform: translate(-50%, 0.5em);
    -ms-transform: translate(-50%, 0.5em);
    transform: translate(-50%, 0.5em);
  }
  [aks-tooltip][flow^="down-left"]::before {
    top: 100%;
    border-top-width: 0;
    border-bottom-color: #020204;
  }
  [aks-tooltip][flow^="down-left"]::after {
    top: calc(100% + 5px);
  }
  [aks-tooltip][flow^="down-left"]::before {
    left: 30%;
  }
  [aks-tooltip][flow^="down-left"]::after {
    left: 50%;
  }
  [aks-tooltip][flow^="down-right"]::before {
    top: 100%;
    border-top-width: 0;
    border-bottom-color: #020204;
  }
  [aks-tooltip][flow^="down-right"]::after {
    top: calc(100% + 5px);
  }
  [aks-tooltip][flow^="down-right"]::before {
    left: 70%;
    -webkit-transform: translate(-85%, 0.5em);
    -ms-transform: translate(-85%, 0.5em);
    transform: translate(-85%, 0.5em);
  }
  [aks-tooltip][flow^="down-right"]::after {
    left: 50%;
    -webkit-transform: translate(-50%, 0.5em);
    -ms-transform: translate(-50%, 0.5em);
    transform: translate(-50%, 0.5em);
  }
  [aks-tooltip][flow^="left"]::before {
    top: 50%;
    border-right-width: 0;
    border-left-color: #020204;
    left: calc(0em - 5px);
    -webkit-transform: translate(-0.5em, -50%);
    -ms-transform: translate(-0.5em, -50%);
    transform: translate(-0.5em, -50%);
  }
  [aks-tooltip][flow^="left"]::after {
    top: 50%;
    right: calc(100% + 5px);
    -webkit-transform: translate(-0.5em, -50%);
    -ms-transform: translate(-0.5em, -50%);
    transform: translate(-0.5em, -50%);
  }
  [aks-tooltip][flow^="right"]::before {
    top: 50%;
    border-left-width: 0;
    border-right-color: #020204;
    right: calc(0em - 5px);
    -webkit-transform: translate(0.5em, -50%);
    -ms-transform: translate(0.5em, -50%);
    transform: translate(0.5em, -50%);
  }
  [aks-tooltip][flow^="right"]::after {
    top: 50%;
    left: calc(100% + 5px);
    -webkit-transform: translate(0.5em, -50%);
    -ms-transform: translate(0.5em, -50%);
    transform: translate(0.5em, -50%);
  }
  [aks-tooltip]:not([flow]):hover::before,
  [aks-tooltip]:not([flow]):hover::after,
  [aks-tooltip][flow^="up"]:hover::before,
  [aks-tooltip][flow^="up"]:hover::after,
  [aks-tooltip][flow^="up-left"]:hover::before,
  [aks-tooltip][flow^="up-left"]:hover::after,
  [aks-tooltip][flow^="up-right"]:hover::before,
  [aks-tooltip][flow^="up-right"]:hover::after,
  [aks-tooltip][flow^="down"]:hover::before,
  [aks-tooltip][flow^="down"]:hover::after,
  [aks-tooltip][flow^="down-left"]:hover::before,
  [aks-tooltip][flow^="down-left"]:hover::after,
  [aks-tooltip][flow^="down-right"]:hover::before,
  [aks-tooltip][flow^="down-right"]:hover::after {
    -webkit-animation: tooltips-vert 300ms ease-out forwards;
    animation: tooltips-vert 300ms ease-out forwards;
  }
  [aks-tooltip][flow^="left"]:hover::before,
  [aks-tooltip][flow^="left"]:hover::after,
  [aks-tooltip][flow^="right"]:hover::before,
  [aks-tooltip][flow^="right"]:hover::after {
    -webkit-animation: tooltips-horz 300ms ease-out forwards;
    animation: tooltips-horz 300ms ease-out forwards;
  }
  @-webkit-keyframes tooltips-vert {
    to {
      opacity: 1;
      -webkit-transform: translate(-50%, 0);
      transform: translate(-50%, 0);
    }
  }
  @keyframes tooltips-vert {
    to {
      opacity: 1;
      -webkit-transform: translate(-50%, 0);
      transform: translate(-50%, 0);
    }
  }
  @-webkit-keyframes tooltips-horz {
    to {
      opacity: 1;
      -webkit-transform: translate(0, -50%);
      transform: translate(0, -50%);
    }
  }
  @keyframes tooltips-horz {
    to {
      opacity: 1;
      -webkit-transform: translate(0, -50%);
      transform: translate(0, -50%);
    }
  }
  .aks-vp-slider-timer-tooltip {
    left: 0;
    position: absolute;
    top: -27px;
    font-size: 13px;
    font-family: "Segoe UI", Arial, sans-serif;
    text-align: center;
    white-space: nowrap;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
    padding: 0.3em 0.8em;
    border-radius: 4px;
    -webkit-box-shadow: rgba(0, 0, 0, 0.35) 0px 1em 2em -0.5em;
    box-shadow: rgba(0, 0, 0, 0.35) 0px 1em 2em -0.5em;
    background: white;
    color: rgb(0, 0, 0);
    z-index: 5;
    line-height: normal;
    display: none;
    -webkit-animation: tooltips-vert 300ms ease-out forwards;
    animation: tooltips-vert 300ms ease-out forwards;
    width: 52px;
    -webkit-transform: translate(-50%, -0.5em);
    -ms-transform: translate(-50%, -0.5em);
    transform: translate(-50%, -0.5em);
  }
  .aks-vp-slider-timer-tooltip:after {
    content: "";
    left: 22px;
    position: absolute;
    bottom: calc(100% + -29px);
    background: #fff;
    display: block;
    z-index: 11;
    width: 10px;
    height: 10px;
    -webkit-transform: rotate(224deg);
    -ms-transform: rotate(224deg);
    transform: rotate(224deg);
  }
  .aks-vp-slider-image-tooltip {
    left: 0;
    position: absolute;
    top: -130px;
    font-size: 13px;
    font-family: "Segoe UI", Arial, sans-serif;
    text-align: center;
    white-space: nowrap;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
    border-radius: 7px;
    -webkit-box-shadow: rgba(0, 0, 0, 0.35) 0px 1em 2em -0.5em;
    box-shadow: rgba(0, 0, 0, 0.35) 0px 1em 2em -0.5em;
    background: white;
    color: rgb(0, 0, 0);
    z-index: 5;
    line-height: normal;
    overflow: hidden;
    display: none;
    -webkit-animation: tooltips-vert 300ms ease-out forwards;
    animation: tooltips-vert 300ms ease-out forwards;
    width: 140px;
    height: 95px;
    -webkit-transform: translate(-50%, -0.5em);
    -ms-transform: translate(-50%, -0.5em);
    transform: translate(-50%, -0.5em);
  }
  .aks-video-2 {
    display: none;
    visibility: hidden;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    outline: none;
  }
  .aks-vp-top {
    position: absolute;
    width: 100%;
    height: 82%;
    top: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    z-index: 1;
    display: none;
  }
  .aks-vp-voice-slider-rail {
    width: 65px;
    height: 4px;
    background: #212121;
    border-radius: 9999px;
    position: relative;
    display: none;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    cursor: pointer;
    margin-right: 10px;
  }
  .aks-vp-voice:hover .aks-vp-voice-slider-rail {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }
  .aks-vp-voice-slider-rail:hover {
    -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, 0.4);
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.4);
  }
  .aks-vp-voice-slider-buffer {
    height: 4px;
    background: var(--aks-video-primary);
    border-radius: 9999px;
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    width: 0;
  }
  .aks-vp-voice-slider-range {
    -webkit-appearance: none;
    width: 100%;
    height: 100%;
    background: transparent;
    outline: none;
    margin: 0;
    padding: 0;
    z-index: 15;
  }
  
  .aks-vp-voice-slider-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    margin: 0;
    border: none;
    outline: none;
  }
  
  .aks-vp-voice-slider-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 12px;
    height: 12px;
    background: var(--aks-video-primary);
    border-radius: 9999px;
    cursor: pointer;
    margin-top: -3px;
    -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.4);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.4);
  }
  
  .aks-vp-voice-slider-range::-webkit-slider-runnable-track {
    width: 100%;
    height: 6px;
    cursor: pointer;
    background: none;
    border-radius: 9999px;
  }
  
  .aks-vp-voice-slider-range:focus::-webkit-slider-runnable-track {
    background: none;
  }
  .aks-vp-voice-slider-range:active::-webkit-slider-runnable-track {
    background: none;
  }
  
  .aks-vp-voice-slider-range::-webkit-slider-thumb:active {
    -webkit-box-shadow: 0 0 26px rgba(0, 0, 0, 0.2), 0 0 10px rgba(0, 0, 0, 4),
      0 0 0 5px rgba(255, 255, 255, 0.2);
    box-shadow: 0 0 26px rgba(0, 0, 0, 0.2), 0 0 10px rgba(0, 0, 0, 4),
      0 0 0 5px rgba(255, 255, 255, 0.2);
  }
  
  .aks-vp-rewind span {
    position: absolute;
    font-size: 10px;
    line-height: normal;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    font-weight: 400;
    margin-top: 2px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }
  .aks-vp-forward span {
    position: absolute;
    font-size: 10px;
    line-height: normal;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    font-weight: 400;
    margin-top: 2px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }
  
  .aks-vp-settings-menu {
    display: none;
    overflow: hidden;
    border-radius: 6px;
    background: rgba(28, 28, 28, 0.9);
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
    -webkit-transition: opacity 0.1s cubic-bezier(0, 0, 0.2, 1);
    -o-transition: opacity 0.1s cubic-bezier(0, 0, 0.2, 1);
    transition: opacity 0.1s cubic-bezier(0, 0, 0.2, 1);
    -moz-user-select: none;
    -ms-user-select: none;
    -webkit-user-select: none;
    will-change: width, height;
    position: absolute;
    bottom: 64px;
    padding-top: 5px;
    padding-bottom: 5px;
    color: white;
    overflow-y: auto;
    right: 0;
  }
  .aks-vp-settings-menu.aks-opened {
    display: block;
  }
  .aks-vp-settings-menu-item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 8px 8px;
    background: transparent;
    font-size: 14px;
    cursor: pointer;
    width: 165px;
  }
  .aks-vp-settings-menu-item:hover {
    background: rgba(255, 255, 255, 0.1);
  }
  .aks-vp-settings-menu-item span {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    line-height: normal;
  }
  .aks-vp-settings-menu-item span svg {
    height: 17px;
    width: 17px;
  }
  .aks-vp-settings-menu-contents {
    height: 140px;
    display: none;
  }
  .aks-vp-settings-menu-contents.aks-opened {
    display: block;
  }
  .aks-vp-settings-menu::-webkit-scrollbar {
    width: 10px;
  }
  .aks-vp-settings-menu::-webkit-scrollbar-track {
    background: rgb(47 47 47 / 90%);
    border-radius: 6px;
  }
  .aks-vp-settings-menu::-webkit-scrollbar-thumb {
    background: rgb(75 75 75 / 90%);
    border-radius: 6px;
  }
  .aks-vp-settings-menu::-webkit-scrollbar-thumb:hover {
    background: rgb(57 57 57 / 90%);
    border-radius: 6px;
  }
  .aks-vp-settings-menu-back {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    padding: 8px 8px;
    background: transparent;
    font-size: 14px;
    cursor: pointer;
    line-height: normal;
    border-bottom: 1px solid #d4d4d4a1;
  }
  .aks-vp-settings-menu-back:hover {
    background: rgba(255, 255, 255, 0.1);
  }
  .aks-vp-settings-menu-back svg {
    width: 14px;
    height: 14px;
    fill: currentColor;
    margin-right: 5px;
  }
  .aks-vp-settings-menu-items.aks-hide {
    display: none;
  }
  .aks-vp-settings-menu-icons {
    width: 15px;
    height: 15px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
  .aks-vp-settings-menu-item.aks-active .aks-vp-settings-menu-icons {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' fill='none' shape-rendering='geometricPrecision' viewBox='0 0 24 24'%3E%3Cpath d='M20 6L9 17l-5-5'/%3E%3C/svg%3E");
  }
  
  video::cue {
    color: white;
    font-family: "Roboto", sans-serif;
    font-size: 16px;
    line-height: 1.5;
    background: rgba(28, 28, 28, 0.9);
    text-shadow: 0px 0px 20px black;
    margin-top: -240px;
    position: relative;
    top: -66px;
  }
  video::cue i {
    font-style: italic;
  }
  video::cue(v) {
    color: #c00;
  }
  video::cue(c) {
    color: white;
    opacity: 0.4;
  }
  .aks-vp-settings-box {
    position: relative;
    width: -webkit-fit-content;
    width: -moz-fit-content;
    width: fit-content;
  }
  
  .aks-vp-cue {
    width: 100%;
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    text-align: center;
    opacity: 0;
    visibility: hidden;
  }
  .aks-vp-cue.aks-active {
    opacity: 1;
    visibility: visible;
  }
  .aks-vp-cue-text {
    color: white;
    font-family: "Roboto", sans-serif;
    font-size: 20px;
    line-height: 1.5;
    background: rgba(28, 28, 28, 0.9);
    text-shadow: 0px 0px 20px black;
    bottom: 65px;
    position: absolute;
    z-index: 10;
    border-radius: 2px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-transition: all 150ms ease;
    -o-transition: all 150ms ease;
    transition: all 150ms ease;
  }
  .aks-vp-cue-text span {
    padding: 1px 6px;
    width: 100%;
    display: block;
  }
  .aks-video-player.aks-mouse .aks-vp-controls {
    opacity: 0;
    visibility: hidden;
  }
  .aks-video-player.aks-mouse .aks-vp-cue-text {
    bottom: 15px;
  }
  .aks-vp-ad-container {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    display: block;
    z-index: 1;
    opacity: 0;
    visibility: hidden;
  }
  
  .aks-video-player.aks-ads-play .aks-vp-controls {
    opacity: 0;
    visibility: hidden;
  }
  .aks-video-player.aks-ads-play .aks-vp-gradient-bottom {
    opacity: 0;
    visibility: hidden;
  }
  .aks-video-player.aks-ads-play .aks-vp-ad-container {
    opacity: 1;
    visibility: visible;
  }
  .aks-vp-ads-image {
    width: 100%;
    display: none;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    margin: 0 auto;
    position: absolute;
    bottom: 65px;
  }
  .aks-video-player.aks-mouse .aks-vp-ads-image {
    bottom: 15px;
  }
  .aks-vp-ads-image img {
    text-decoration: none;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: relative;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    outline: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    cursor: pointer;
  }
  .aks-vp-ads-close {
    position: absolute;
    top: 0;
    right: 0;
    width: 15px;
    height: 15px;
    background: #0000004a;
    border: 1px solid #00000054;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    padding: 2px;
    line-height: normal;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }
  .aks-vp-ads-close svg {
    width: 12px;
    height: 12px;
  }
  .aks-vp-ads-box {
    width: 100%;
    height: 100%;
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    border: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  .aks-vp-ads-box a {
    text-decoration: none;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: relative;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    outline: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    cursor: pointer;
  }
  .aks-vp-ads-video {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    left: 0;
    right: 0;
    z-index: 50;
    display: none;
  }
  .aks-vp-ads-video video {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    outline: none;
    width: 100%;
    height: auto;
    display: block;
    pointer-events: none;
  }
  .aks-vp-ads-skip {
    position: absolute;
    bottom: 40px;
    z-index: 1;
    background: #000000ab;
    right: 0;
    color: white;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    padding: 8px;
    line-height: normal;
    font-size: 16px;
    cursor: pointer;
    -webkit-transition: all 150ms ease;
    -o-transition: all 150ms ease;
    transition: all 150ms ease;
  }
  .aks-vp-ads-skip:hover {
    background: #000000d4;
  }
  .aks-vp-ads-skip svg {
    margin-left: 10px;
  }
  
  .aks-vp-ads-html {
    width: 100%;
    display: none;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    margin: 0 auto;
    position: absolute;
    bottom: 65px;
  }
  .aks-video-player.aks-mouse .aks-vp-ads-html {
    bottom: 15px;
  }
  
  #aks-vp-ad-container div:first-child {
    width: 100% !important;
    height: 100% !important;
  }
  
  .aks-vp-picture-in-picture {
    width: 340px;
    height: 240px;
    background: black;
    position: fixed;
    bottom: 15px;
    right: 15px;
    border-radius: 10px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    overflow: hidden;
    display: block;
    z-index: 9999999999;
  }
  .aks-vp-picture-in-video-controls {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 100%;
    position: absolute;
    bottom: 15px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -webkit-transition: all 150ms ease;
    -o-transition: all 150ms ease;
    transition: all 150ms ease;
  }
  .aks-vp-picture-in-video {
    width: 100%;
    height: 100%;
  }
  .aks-vp-picture-in-video video {
    width: 100%;
    height: 100%;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    outline: none;
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
  }
  
  .aks-vp-picturein.aks-active {
    color: var(--aks-video-primary);
  }
  .aks-vp-settings.aks-active {
    color: var(--aks-video-primary);
  }
  
  .aks-vp-contextmenu {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: none;
  }
  .aks-vp-contextmenu-items {
    background: rgba(28, 28, 28, 0.9);
    width: 200px;
    color: white;
    position: fixed;
    top: 0;
    left: 0;
    overflow: hidden;
    border-radius: 10px;
    z-index: 10;
    padding-top: 5px;
    padding-bottom: 5px;
    font-size: 15px;
  }
  .aks-vp-contextmenu-items.aks-hide {
    display: none !important;
  }
  .aks-vp-contextmenu-item {
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 5px 10px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    outline: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    cursor: pointer;
  }
  .aks-vp-contextmenu-item:hover {
    background: #282e32;
  }
  .aks-vp-contextmenu-contents {
    background: rgba(28, 28, 28, 0.9);
    color: white;
    position: absolute;
    overflow: hidden;
    border-radius: 10px;
    z-index: 100;
    padding-top: 5px;
    padding-bottom: 5px;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    display: none;
  }
  .aks-vp-contextmenu-contents.aks-active {
    display: block;
  }
  .aks-vp-contextmenu-close {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    position: absolute;
    right: 15px;
    top: 15px;
    width: 24px;
    height: 24px;
    background: transparent;
    color: white;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    text-align: center;
    -webkit-transition: all 250ms cubic-bezier(0.4, 0, 1, 1);
    -o-transition: all 250ms cubic-bezier(0.4, 0, 1, 1);
    transition: all 250ms cubic-bezier(0.4, 0, 1, 1);
  }
  .aks-vp-contextmenu-close:hover {
    background: #273035;
  }
  .aks-vp-contextmenu-content {
    width: 100%;
    height: 100%;
    display: none;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
  }
  .aks-vp-contextmenu-content.aks-active {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }
  .aks-vp-contextmenu-title {
    font-size: 26px;
    font-weight: 600;
    line-height: normal;
    margin-bottom: 25px;
  }
  .aks-vp-contextmenu-copy {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    background: #2c3537;
    padding: 5px 15px;
    border-radius: 10px;
    position: relative;
    width: 220px;
    font-size: 16px;
  }
  .aks-vp-contextmenu-copy span {
    overflow: hidden;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 88%;
    text-align: left;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .aks-vp-contextmenu-copy-btn {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    background: #202628;
    border: none;
    color: white;
    width: 24px;
    height: 24px;
    padding: 3px;
    border-radius: 4px;
    position: absolute;
    right: 8px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    cursor: pointer;
    outline: none;
    -webkit-transition: all 150ms ease;
    -o-transition: all 150ms ease;
    transition: all 150ms ease;
  }
  .aks-vp-contextmenu-copy-btn:active {
    -webkit-transform: scale(0.8);
    -ms-transform: scale(0.8);
    transform: scale(0.8);
  }
  .aks-vp-contextmenu-socials-btn {
    width: 35px;
    height: 35px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    margin-right: 15px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    outline: none;
    border-radius: 5px;
    line-height: normal;
    cursor: pointer;
  }
  .aks-vp-contextmenu-socials-btn:last-child {
    margin-right: 0px;
  }
  .aks-vp-contextmenu-socials-btn svg {
    width: 18px;
    height: 18px;
    fill: currentColor;
  }
  .aks-vp-contextmenu-socials {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    position: relative;
  }