@extends('layouts.admin')

@section('page-title', 'Booking Management')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="page-title">Booking Management</h1>
            <p class="page-subtitle">Manage all flight bookings and passenger information</p>
        </div>
    </div>

    <!-- Filters Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters & Search</h5>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.bookings.index') }}" class="row g-3" id="filterForm">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ request('search') }}" placeholder="Booking ref, email, passenger...">
                </div>
                
                <div class="col-md-2">
                    <label for="booking_status" class="form-label">Booking Status</label>
                    <select class="form-select" id="booking_status" name="booking_status">
                        <option value="">All Statuses</option>
                        @foreach($bookingStatuses as $status)
                            <option value="{{ $status }}" {{ request('booking_status') == $status ? 'selected' : '' }}>
                                {{ $status }}
                            </option>
                        @endforeach
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="payment_status" class="form-label">Payment Status</label>
                    <select class="form-select" id="payment_status" name="payment_status">
                        <option value="">All Payments</option>
                        @foreach($paymentStatuses as $status)
                            <option value="{{ $status }}" {{ request('payment_status') == $status ? 'selected' : '' }}>
                                {{ $status }}
                            </option>
                        @endforeach
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="flight_type" class="form-label">Flight Type</label>
                    <select class="form-select" id="flight_type" name="flight_type">
                        <option value="">All Types</option>
                        @foreach($flightTypes as $type)
                            <option value="{{ $type }}" {{ request('flight_type') == $type ? 'selected' : '' }}>
                                {{ $type }}
                            </option>
                        @endforeach
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="date_from" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="date_from" name="date_from"
                           value="{{ request('date_from') }}">
                </div>

                <div class="col-md-2">
                    <label for="date_to" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="date_to" name="date_to"
                           value="{{ request('date_to') }}">
                </div>

                <div class="col-md-12">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Search
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="clearFilters">
                            <i class="fas fa-times me-2"></i>Clear
                        </button>
                        <button type="button" class="btn btn-outline-success" id="exportBookings">
                            <i class="fas fa-download me-2"></i>Export CSV
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Bookings Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Bookings ({{ $bookings->total() }} total)</h5>
            <div class="d-flex gap-2">
                <span class="badge bg-warning">{{ $bookings->where('booking_status', 'Pending')->count() }} Pending</span>
                <span class="badge bg-success">{{ $bookings->where('booking_status', 'Confirmed')->count() }} Confirmed</span>
                <span class="badge bg-danger">{{ $bookings->where('booking_status', 'Cancelled')->count() }} Cancelled</span>
                <span class="badge bg-info">{{ $bookings->where('booking_status', 'Completed')->count() }} Completed</span>
            </div>
        </div>
        <div class="card-body p-0">
            @if($bookings->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Booking Ref</th>
                                <th>Flight</th>
                                <th>Passenger(s)</th>
                                <th>Contact</th>
                                <th>Amount</th>
                                <th>Booking Status</th>
                                <th>Payment</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($bookings as $booking)
                                <tr>
                                    <td>
                                        <div class="fw-bold">{{ $booking->booking_reference }}</div>
                                        @if($booking->is_checked_in)
                                            <span class="badge bg-success">Checked In</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="fw-bold">{{ $booking->flight->flight_number }}</div>
                                        <small class="text-muted">
                                            {{ $booking->flight->departureAirport->code }} → {{ $booking->flight->arrivalAirport->code }}
                                        </small>
                                        <div class="small text-muted">
                                            {{ $booking->flight->departure_time->format('M d, Y H:i') }}
                                        </div>
                                    </td>
                                    <td>
                                        <div>{{ $booking->passenger_count }} passenger(s)</div>
                                        @if($booking->passengers->count() > 0)
                                            <small class="text-muted">
                                                {{ $booking->passengers->first()->full_name }}
                                                @if($booking->passenger_count > 1)
                                                    +{{ $booking->passenger_count - 1 }} more
                                                @endif
                                            </small>
                                        @endif
                                    </td>
                                    <td>
                                        <div>{{ $booking->contact_email }}</div>
                                        @if($booking->contact_phone)
                                            <small class="text-muted">{{ $booking->contact_phone }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="fw-bold">${{ number_format($booking->total_amount, 2) }}</span>
                                    </td>
                                    <td>
                                        @php
                                            $statusClass = match($booking->booking_status) {
                                                'Pending' => 'bg-warning',
                                                'Confirmed' => 'bg-success',
                                                'Cancelled' => 'bg-danger',
                                                'Completed' => 'bg-info',
                                                default => 'bg-secondary'
                                            };
                                        @endphp
                                        <span class="badge {{ $statusClass }}">{{ $booking->booking_status }}</span>
                                    </td>
                                    <td>
                                        @php
                                            $paymentClass = match($booking->payment_status) {
                                                'Pending' => 'bg-warning',
                                                'Paid' => 'bg-success',
                                                'Failed' => 'bg-danger',
                                                'Refunded' => 'bg-info',
                                                default => 'bg-secondary'
                                            };
                                        @endphp
                                        <span class="badge {{ $paymentClass }}">{{ $booking->payment_status }}</span>
                                    </td>
                                    <td>
                                        <div>{{ $booking->booking_date->format('M d, Y') }}</div>
                                        <small class="text-muted">{{ $booking->booking_date->format('H:i') }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.bookings.show', $booking) }}" 
                                               class="btn btn-sm btn-outline-primary" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.bookings.edit', $booking) }}" 
                                               class="btn btn-sm btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            @if($booking->can_check_in)
                                                <form action="{{ route('admin.bookings.check-in', $booking) }}" 
                                                      method="POST" class="d-inline">
                                                    @csrf
                                                    <button type="submit" class="btn btn-sm btn-outline-success" 
                                                            title="Check In">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                </form>
                                            @endif
                                            @if($booking->can_be_cancelled)
                                                <form action="{{ route('admin.bookings.cancel', $booking) }}" 
                                                      method="POST" class="d-inline">
                                                    @csrf
                                                    <button type="submit" class="btn btn-sm btn-outline-warning" 
                                                            title="Cancel" onclick="return confirm('Cancel this booking?')">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center p-3">
                    <div class="text-muted">
                        Showing {{ $bookings->firstItem() }} to {{ $bookings->lastItem() }} of {{ $bookings->total() }} results
                    </div>
                    {{ $bookings->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-ticket-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No bookings found</h5>
                    <p class="text-muted">Try adjusting your search criteria.</p>
                </div>
            @endif
        </div>
    </div>
</div>

@if(session('success'))
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div class="toast show" role="alert">
            <div class="toast-header">
                <i class="fas fa-check-circle text-success me-2"></i>
                <strong class="me-auto">Success</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                {{ session('success') }}
            </div>
        </div>
    </div>
@endif

@if(session('error'))
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div class="toast show" role="alert">
            <div class="toast-header">
                <i class="fas fa-exclamation-circle text-danger me-2"></i>
                <strong class="me-auto">Error</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                {{ session('error') }}
            </div>
        </div>
    </div>
@endif
@endsection

@push('scripts')
<script>
    // Auto-hide toasts after 5 seconds
    setTimeout(function() {
        $('.toast').toast('hide');
    }, 5000);

    // Real-time search functionality
    let searchTimeout;
    $('#search').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            $('#filterForm').submit();
        }, 500);
    });

    // Auto-submit form when filters change
    $('.form-select').on('change', function() {
        $('#filterForm').submit();
    });

    // Clear all filters
    $('#clearFilters').on('click', function(e) {
        e.preventDefault();
        $('#filterForm')[0].reset();
        window.location.href = '{{ route("admin.bookings.index") }}';
    });

    // Export functionality
    $('#exportBookings').on('click', function() {
        const params = new URLSearchParams(window.location.search);
        params.set('export', 'csv');
        window.location.href = '{{ route("admin.bookings.index") }}?' + params.toString();
    });
</script>
@endpush
