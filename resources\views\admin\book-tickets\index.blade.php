@extends('layouts.admin')

@section('title', 'Book Tickets')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-ticket-alt me-2"></i>Book Tickets
                    </h1>
                    <p class="text-muted mb-0">Search and book flights for customers</p>
                </div>
                <div>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#searchModal">
                        <i class="fas fa-search me-2"></i>New Search
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Summary -->
    @if(!empty($searchParams['departure_airport']) && !empty($searchParams['arrival_airport']))
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-1">
                                <i class="fas fa-plane-departure text-primary me-2"></i>
                                {{ $departureAirport['city'] ?? $searchParams['departure_airport'] }} ({{ $searchParams['departure_airport'] }}) 
                                <i class="fas fa-arrow-right mx-2"></i>
                                {{ $arrivalAirport['city'] ?? $searchParams['arrival_airport'] }} ({{ $searchParams['arrival_airport'] }})
                            </h5>
                            <p class="text-muted mb-0">
                                <i class="fas fa-calendar me-1"></i>
                                {{ \Carbon\Carbon::parse($searchParams['departure_date'])->format('M d, Y') }}
                                @if(!empty($searchParams['return_date']))
                                    - {{ \Carbon\Carbon::parse($searchParams['return_date'])->format('M d, Y') }}
                                @endif
                                <span class="ms-3">
                                    <i class="fas fa-users me-1"></i>
                                    {{ $searchParams['passengers'] }} {{ Str::plural('Passenger', $searchParams['passengers']) }}
                                </span>
                                <span class="ms-3">
                                    <i class="fas fa-chair me-1"></i>
                                    {{ ucfirst(str_replace('_', ' ', $searchParams['cabin_class'])) }}
                                </span>
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#searchModal">
                                <i class="fas fa-edit me-1"></i>
                                Modify Search
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <div class="row">
        <!-- Filters Sidebar -->
        <div class="col-lg-3">
            <!-- Trip Types -->
            <div class="card border-0 shadow-sm mb-3">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0 fw-bold">
                        <i class="fas fa-route me-2"></i>All Types
                    </h6>
                </div>
                <div class="card-body">
                    <div class="btn-group w-100 mb-3" role="group">
                        <input type="radio" class="btn-check" name="trip_type_filter" id="uae_one_way" value="uae_one_way" checked>
                        <label class="btn btn-outline-primary btn-sm" for="uae_one_way">UAE One Way</label>
                        
                        <input type="radio" class="btn-check" name="trip_type_filter" id="ksa_one_way" value="ksa_one_way">
                        <label class="btn btn-outline-primary btn-sm" for="ksa_one_way">KSA One Way</label>
                    </div>
                    <div class="btn-group w-100 mb-3" role="group">
                        <input type="radio" class="btn-check" name="trip_type_filter" id="oman_one_way" value="oman_one_way">
                        <label class="btn btn-outline-primary btn-sm" for="oman_one_way">Oman One Way</label>
                        
                        <input type="radio" class="btn-check" name="trip_type_filter" id="bahrain_one_way" value="bahrain_one_way">
                        <label class="btn btn-outline-primary btn-sm" for="bahrain_one_way">Bahrain One Way</label>
                    </div>
                    <div class="btn-group w-100 mb-3" role="group">
                        <input type="radio" class="btn-check" name="trip_type_filter" id="umrah" value="umrah">
                        <label class="btn btn-outline-primary btn-sm" for="umrah">Umrah</label>
                        
                        <input type="radio" class="btn-check" name="trip_type_filter" id="uk_one_way" value="uk_one_way">
                        <label class="btn btn-outline-primary btn-sm" for="uk_one_way">UK One Way</label>
                    </div>
                </div>
            </div>

            <!-- Airlines Filter -->
            <div class="card border-0 shadow-sm mb-3">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0 fw-bold">Airlines</h6>
                </div>
                <div class="card-body">
                    @foreach($airlines as $airline)
                    <div class="form-check mb-2">
                        <input class="form-check-input airline-filter" type="checkbox" value="{{ $airline['code'] }}" id="airline-{{ $airline['code'] }}">
                        <label class="form-check-label d-flex align-items-center" for="airline-{{ $airline['code'] }}">
                            <div class="airline-logo me-2" style="width: 20px; height: 14px; background: {{ $airline['color'] }}; border-radius: 2px; display: flex; align-items: center; justify-content: center; color: white; font-size: 8px; font-weight: bold;">
                                {{ $airline['code'] }}
                            </div>
                            {{ $airline['name'] }}
                        </label>
                    </div>
                    @endforeach
                </div>
            </div>

            <!-- Sectors Filter -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0 fw-bold">Sectors</h6>
                </div>
                <div class="card-body">
                    @foreach($sectors as $sector)
                    <div class="form-check mb-2">
                        <input class="form-check-input sector-filter" type="checkbox" value="{{ $sector['code'] }}" id="sector-{{ $sector['code'] }}">
                        <label class="form-check-label" for="sector-{{ $sector['code'] }}">
                            {{ $sector['name'] }}
                        </label>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Flight Results -->
        <div class="col-lg-9">
            <!-- Advanced Search Toggle -->
            <div class="card border-0 shadow-sm mb-3">
                <div class="card-body py-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="advancedSearch">
                            <label class="form-check-label fw-bold" for="advancedSearch">
                                <i class="fas fa-search-plus me-1"></i>Advance Search
                            </label>
                        </div>
                        <div class="d-flex align-items-center gap-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="searchOnDeal" checked>
                                <label class="form-check-label fw-bold" for="searchOnDeal">
                                    <i class="fas fa-tags me-1"></i>Search On Deal Date
                                </label>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="searchByKeyword">
                                <label class="form-check-label fw-bold" for="searchByKeyword">
                                    <i class="fas fa-key me-1"></i>Search by Keyword
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Results Header -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <h5 class="mb-1">Available Flights</h5>
                    <p class="text-muted mb-0">{{ count($flights) }} flights found</p>
                </div>
                <div class="d-flex align-items-center gap-3">
                    <div class="d-flex align-items-center">
                        <label class="form-label me-2 mb-0 fw-bold">Sort by:</label>
                        <select class="form-select form-select-sm" style="width: auto;" onchange="sortFlights(this.value)">
                            <option value="price">Price (Low to High)</option>
                            <option value="price_desc">Price (High to Low)</option>
                            <option value="duration">Duration</option>
                            <option value="departure_time">Departure Time</option>
                            <option value="arrival_time">Arrival Time</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Flight Results Table -->
            <div class="card border-0 shadow-sm">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="flightsTable">
                        <thead class="table-primary">
                            <tr>
                                <th>Date</th>
                                <th>Flight</th>
                                <th>Origin-Destination</th>
                                <th>Time</th>
                                <th>Baggage</th>
                                <th>Meal</th>
                                <th>Price</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($flights as $flight)
                            <tr class="flight-row" 
                                data-airline="{{ $flight['airline_code'] ?? 'Unknown' }}"
                                data-price="{{ $flight['price'] ?? 0 }}"
                                data-duration="{{ $flight['duration_minutes'] ?? 150 }}"
                                data-departure="{{ $flight['departure_time'] ?? '00:00' }}"
                                data-stops="{{ $flight['stops'] ?? 0 }}">
                                <td>
                                    <div class="fw-bold text-primary">{{ \Carbon\Carbon::parse($flight['departure_date'] ?? now())->format('d M Y') }}</div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="airline-logo me-2" style="width: 24px; height: 16px; background: {{ $flight['airline_color'] ?? '#1e40af' }}; border-radius: 2px; display: flex; align-items: center; justify-content: center; color: white; font-size: 8px; font-weight: bold;">
                                            {{ $flight['airline_code'] ?? 'XX' }}
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ $flight['flight_number'] ?? 'XX000' }}</div>
                                            <small class="text-muted">{{ $flight['airline_name'] ?? 'Unknown Airline' }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="fw-bold">{{ $flight['departure_airport'] ?? 'DEP' }}-{{ $flight['arrival_airport'] ?? 'ARR' }}</div>
                                </td>
                                <td>
                                    <div class="fw-bold">{{ $flight['departure_time'] ?? '14:30' }}-{{ $flight['arrival_time'] ?? '17:00' }}</div>
                                    <small class="text-muted">{{ $flight['duration'] ?? '2h 30m' }}</small>
                                </td>
                                <td>
                                    <span class="badge bg-info">20+7 KG</span>
                                </td>
                                <td>
                                    <span class="badge {{ ($flight['meal'] ?? 'No') === 'Yes' ? 'bg-success' : 'bg-secondary' }}">
                                        {{ ($flight['meal'] ?? 'No') === 'Yes' ? 'Yes' : 'No' }}
                                    </span>
                                </td>
                                <td>
                                    <div class="fw-bold text-primary">PKR {{ number_format($flight['price'] ?? 84000) }}</div>
                                </td>
                                <td>
                                    <button class="btn btn-warning btn-sm" onclick="bookFlight('{{ $flight['id'] ?? 'flight-' . $loop->index }}')">
                                        <i class="fas fa-ticket-alt me-1"></i>Book now
                                    </button>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="8" class="text-center py-5">
                                    <i class="fas fa-plane-slash fa-3x text-muted mb-3"></i>
                                    <h5>No flights found</h5>
                                    <p class="text-muted">Try adjusting your search criteria or dates.</p>
                                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#searchModal">
                                        <i class="fas fa-search me-1"></i>New Search
                                    </button>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Search Modal -->
<div class="modal fade" id="searchModal" tabindex="-1" aria-labelledby="searchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="searchModalLabel">
                    <i class="fas fa-search me-2"></i>Search Flights
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('admin.book-tickets.index') }}" method="GET" id="flightSearchForm">
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">From</label>
                            <div class="position-relative">
                                <input type="text" class="form-control airport-search" id="departure_airport" name="departure_airport"
                                       value="{{ $searchParams['departure_airport'] ?? '' }}" placeholder="Enter city or airport code" autocomplete="off" required>
                                <div class="airport-suggestions" id="departure_suggestions"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">To</label>
                            <div class="position-relative">
                                <input type="text" class="form-control airport-search" id="arrival_airport" name="arrival_airport"
                                       value="{{ $searchParams['arrival_airport'] ?? '' }}" placeholder="Enter city or airport code" autocomplete="off" required>
                                <div class="airport-suggestions" id="arrival_suggestions"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">Departure Date</label>
                            <input type="date" class="form-control" name="departure_date"
                                   value="{{ $searchParams['departure_date'] ?? now()->addDay()->format('Y-m-d') }}"
                                   min="{{ date('Y-m-d') }}" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">Return Date (Optional)</label>
                            <input type="date" class="form-control" name="return_date"
                                   value="{{ $searchParams['return_date'] ?? '' }}"
                                   min="{{ date('Y-m-d') }}">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">Passengers</label>
                            <select class="form-select" name="passengers">
                                @for($i = 1; $i <= 9; $i++)
                                    <option value="{{ $i }}" {{ ($searchParams['passengers'] ?? 1) == $i ? 'selected' : '' }}>
                                        {{ $i }} {{ Str::plural('Passenger', $i) }}
                                    </option>
                                @endfor
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">Cabin Class</label>
                            <select class="form-select" name="cabin_class">
                                <option value="economy" {{ ($searchParams['cabin_class'] ?? 'economy') == 'economy' ? 'selected' : '' }}>Economy</option>
                                <option value="premium_economy" {{ ($searchParams['cabin_class'] ?? 'economy') == 'premium_economy' ? 'selected' : '' }}>Premium Economy</option>
                                <option value="business" {{ ($searchParams['cabin_class'] ?? 'economy') == 'business' ? 'selected' : '' }}>Business</option>
                                <option value="first" {{ ($searchParams['cabin_class'] ?? 'economy') == 'first' ? 'selected' : '' }}>First Class</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>Search Flights
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('styles')
<style>
.airport-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.airport-suggestion {
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
}

.airport-suggestion:hover,
.airport-suggestion.highlighted {
    background-color: #f8f9fa;
}

.airport-suggestion:last-child {
    border-bottom: none;
}

.airport-code {
    font-weight: bold;
    color: #007bff;
}

.airport-name {
    color: #333;
    margin-left: 8px;
}

.airport-city {
    color: #666;
    font-size: 0.9em;
}

.table-primary th {
    background-color: #4e73df;
    color: white;
    border-color: #4e73df;
}

.flight-row:hover {
    background-color: #f8f9fa;
}

.airline-logo {
    font-family: 'Arial', sans-serif;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Airport autocomplete functionality
    let searchTimeout;

    function setupAirportSearch(inputId, suggestionsId) {
        const input = document.getElementById(inputId);
        const suggestions = document.getElementById(suggestionsId);

        if (!input || !suggestions) return;

        input.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();

            if (query.length < 1) {
                suggestions.style.display = 'none';
                return;
            }

            searchTimeout = setTimeout(() => {
                suggestions.innerHTML = '<div class="airport-suggestion"><i class="fas fa-spinner fa-spin me-2"></i>Searching airports...</div>';
                suggestions.style.display = 'block';

                fetch(`{{ route('admin.book-tickets.airports.search') }}?q=${encodeURIComponent(query)}`)
                    .then(response => response.json())
                    .then(airports => {
                        if (airports.length > 0) {
                            suggestions.innerHTML = airports.map(airport => `
                                <div class="airport-suggestion" onclick="selectAirport('${inputId}', '${airport.code}', '${airport.name}', '${airport.city}')">
                                    <span class="airport-code">${airport.code}</span>
                                    <span class="airport-name">${airport.name}</span>
                                    <div class="airport-city">${airport.city}, ${airport.country}</div>
                                </div>
                            `).join('');
                            suggestions.style.display = 'block';
                        } else {
                            suggestions.innerHTML = '<div class="airport-suggestion text-muted">No airports found</div>';
                            suggestions.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        console.error('Airport search error:', error);
                        suggestions.innerHTML = '<div class="airport-suggestion text-danger">Error searching airports</div>';
                        suggestions.style.display = 'block';
                    });
            }, 300);
        });

        // Hide suggestions when clicking outside
        document.addEventListener('click', function(e) {
            if (!input.contains(e.target) && !suggestions.contains(e.target)) {
                suggestions.style.display = 'none';
            }
        });
    }

    setupAirportSearch('departure_airport', 'departure_suggestions');
    setupAirportSearch('arrival_airport', 'arrival_suggestions');

    // Filter functionality
    document.querySelectorAll('.airline-filter, .sector-filter').forEach(checkbox => {
        checkbox.addEventListener('change', applyFilters);
    });
});

function selectAirport(inputId, code, name, city) {
    const input = document.getElementById(inputId);
    const suggestions = document.getElementById(inputId.replace('_airport', '_suggestions'));

    input.value = code;
    input.dataset.airportCode = code;
    suggestions.style.display = 'none';
}

function applyFilters() {
    const rows = document.querySelectorAll('.flight-row');
    let visibleCount = 0;

    rows.forEach(row => {
        let shouldShow = true;

        // Airline filter
        const selectedAirlines = Array.from(document.querySelectorAll('.airline-filter:checked'))
            .map(cb => cb.value);

        if (selectedAirlines.length > 0) {
            const airline = row.dataset.airline;
            shouldShow = shouldShow && selectedAirlines.includes(airline);
        }

        row.style.display = shouldShow ? '' : 'none';
        if (shouldShow) visibleCount++;
    });
}

function sortFlights(sortBy) {
    const tbody = document.querySelector('#flightsTable tbody');
    const rows = Array.from(tbody.querySelectorAll('.flight-row'));

    rows.sort((a, b) => {
        switch(sortBy) {
            case 'price':
                return parseInt(a.dataset.price) - parseInt(b.dataset.price);
            case 'price_desc':
                return parseInt(b.dataset.price) - parseInt(a.dataset.price);
            case 'duration':
                return parseInt(a.dataset.duration) - parseInt(b.dataset.duration);
            case 'departure_time':
                return a.dataset.departure.localeCompare(b.dataset.departure);
            default:
                return 0;
        }
    });

    rows.forEach(row => tbody.appendChild(row));
}

function bookFlight(flightId) {
    // Show booking confirmation
    if (confirm('Are you sure you want to book this flight?')) {
        // Here you would implement the actual booking logic
        alert('Flight booking functionality will be implemented here.');
    }
}
</script>
@endpush
@endsection
