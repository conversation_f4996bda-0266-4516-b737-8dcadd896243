@extends('layouts.admin')

@section('page-title', 'Edit Flight')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="page-title">Edit Flight</h1>
            <p class="page-subtitle">{{ $flight->flight_number }} - {{ $flight->airline->name }}</p>
        </div>
        <a href="{{ route('admin.flights.show', $flight) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Flight Details
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-edit me-2"></i>Flight Information</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.flights.update', $flight) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="flight_number" class="form-label">Flight Number</label>
                                <input type="text" class="form-control @error('flight_number') is-invalid @enderror" 
                                       id="flight_number" name="flight_number" value="{{ old('flight_number', $flight->flight_number) }}" required>
                                @error('flight_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="airline_id" class="form-label">Airline</label>
                                <select class="form-select @error('airline_id') is-invalid @enderror" id="airline_id" name="airline_id" required>
                                    <option value="">Select Airline</option>
                                    @foreach($airlines as $airline)
                                        <option value="{{ $airline->id }}" {{ old('airline_id', $flight->airline_id) == $airline->id ? 'selected' : '' }}>
                                            {{ $airline->name }} ({{ $airline->code }})
                                        </option>
                                    @endforeach
                                </select>
                                @error('airline_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="departure_airport_id" class="form-label">Departure Airport</label>
                                <select class="form-select @error('departure_airport_id') is-invalid @enderror" id="departure_airport_id" name="departure_airport_id" required>
                                    <option value="">Select Departure Airport</option>
                                    @foreach($airports as $airport)
                                        <option value="{{ $airport->id }}" {{ old('departure_airport_id', $flight->departure_airport_id) == $airport->id ? 'selected' : '' }}>
                                            {{ $airport->code }} - {{ $airport->city }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('departure_airport_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="arrival_airport_id" class="form-label">Arrival Airport</label>
                                <select class="form-select @error('arrival_airport_id') is-invalid @enderror" id="arrival_airport_id" name="arrival_airport_id" required>
                                    <option value="">Select Arrival Airport</option>
                                    @foreach($airports as $airport)
                                        <option value="{{ $airport->id }}" {{ old('arrival_airport_id', $flight->arrival_airport_id) == $airport->id ? 'selected' : '' }}>
                                            {{ $airport->code }} - {{ $airport->city }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('arrival_airport_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="departure_time" class="form-label">Departure Time</label>
                                <input type="datetime-local" class="form-control @error('departure_time') is-invalid @enderror" 
                                       id="departure_time" name="departure_time" value="{{ old('departure_time', $flight->departure_time->format('Y-m-d\TH:i')) }}" required>
                                @error('departure_time')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="arrival_time" class="form-label">Arrival Time</label>
                                <input type="datetime-local" class="form-control @error('arrival_time') is-invalid @enderror" 
                                       id="arrival_time" name="arrival_time" value="{{ old('arrival_time', $flight->arrival_time->format('Y-m-d\TH:i')) }}" required>
                                @error('arrival_time')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="aircraft_type" class="form-label">Aircraft Type</label>
                                <select class="form-select @error('aircraft_type') is-invalid @enderror" id="aircraft_type" name="aircraft_type" required>
                                    <option value="">Select Aircraft Type</option>
                                    @foreach(['Boeing 737', 'Boeing 747', 'Boeing 777', 'Boeing 787', 'Airbus A320', 'Airbus A330', 'Airbus A350', 'Airbus A380'] as $aircraft)
                                        <option value="{{ $aircraft }}" {{ old('aircraft_type', $flight->aircraft_type) == $aircraft ? 'selected' : '' }}>
                                            {{ $aircraft }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('aircraft_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="total_seats" class="form-label">Total Seats</label>
                                <input type="number" class="form-control @error('total_seats') is-invalid @enderror" 
                                       id="total_seats" name="total_seats" value="{{ old('total_seats', $flight->total_seats) }}" min="1" required>
                                @error('total_seats')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="available_seats" class="form-label">Available Seats</label>
                                <input type="number" class="form-control @error('available_seats') is-invalid @enderror" 
                                       id="available_seats" name="available_seats" value="{{ old('available_seats', $flight->available_seats) }}" min="0" required>
                                @error('available_seats')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="base_price" class="form-label">Base Price ($)</label>
                                <input type="number" step="0.01" class="form-control @error('base_price') is-invalid @enderror" 
                                       id="base_price" name="base_price" value="{{ old('base_price', $flight->base_price) }}" min="0" required>
                                @error('base_price')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="flight_type" class="form-label">Flight Type</label>
                                <select class="form-select @error('flight_type') is-invalid @enderror" id="flight_type" name="flight_type" required>
                                    <option value="">Select Flight Type</option>
                                    @foreach(['Domestic', 'International'] as $type)
                                        <option value="{{ $type }}" {{ old('flight_type', $flight->flight_type) == $type ? 'selected' : '' }}>
                                            {{ $type }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('flight_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="class_type" class="form-label">Class Type</label>
                                <select class="form-select @error('class_type') is-invalid @enderror" id="class_type" name="class_type" required>
                                    <option value="">Select Class Type</option>
                                    @foreach(['Economy', 'Business', 'First'] as $class)
                                        <option value="{{ $class }}" {{ old('class_type', $flight->class_type) == $class ? 'selected' : '' }}>
                                            {{ $class }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('class_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                                    <option value="">Select Status</option>
                                    @foreach(['Scheduled', 'Delayed', 'Cancelled', 'Completed'] as $status)
                                        <option value="{{ $status }}" {{ old('status', $flight->status) == $status ? 'selected' : '' }}>
                                            {{ $status }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="gate" class="form-label">Gate</label>
                                <input type="text" class="form-control @error('gate') is-invalid @enderror" 
                                       id="gate" name="gate" value="{{ old('gate', $flight->gate) }}" placeholder="e.g., A12">
                                @error('gate')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Flight
                            </button>
                            <a href="{{ route('admin.flights.show', $flight) }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Current Flight Info</h5>
                </div>
                <div class="card-body">
                    <div class="small text-muted mb-2">Current Route</div>
                    <div class="fw-bold mb-3">{{ $flight->departureAirport->code }} → {{ $flight->arrivalAirport->code }}</div>
                    
                    <div class="small text-muted mb-2">Current Status</div>
                    <div class="mb-3">
                        @php
                            $statusClass = match($flight->status) {
                                'Scheduled' => 'bg-primary',
                                'Delayed' => 'bg-warning',
                                'Cancelled' => 'bg-danger',
                                'Completed' => 'bg-success',
                                default => 'bg-secondary'
                            };
                        @endphp
                        <span class="badge {{ $statusClass }}">{{ $flight->status }}</span>
                    </div>
                    
                    <div class="small text-muted mb-2">Bookings</div>
                    <div class="fw-bold mb-3">{{ $flight->bookings->count() }} booking(s)</div>
                    
                    <div class="small text-muted mb-2">Occupancy</div>
                    <div class="fw-bold">{{ $flight->total_seats - $flight->available_seats }}/{{ $flight->total_seats }} seats</div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Auto-calculate flight type based on airports
    $('#departure_airport_id, #arrival_airport_id').on('change', function() {
        const departureSelect = $('#departure_airport_id');
        const arrivalSelect = $('#arrival_airport_id');
        const flightTypeSelect = $('#flight_type');
        
        if (departureSelect.val() && arrivalSelect.val()) {
            const departureCountry = departureSelect.find(':selected').data('country');
            const arrivalCountry = arrivalSelect.find(':selected').data('country');
            
            if (departureCountry && arrivalCountry) {
                if (departureCountry === arrivalCountry) {
                    flightTypeSelect.val('Domestic');
                } else {
                    flightTypeSelect.val('International');
                }
            }
        }
    });

    // Validate that available seats doesn't exceed total seats
    $('#available_seats, #total_seats').on('input', function() {
        const totalSeats = parseInt($('#total_seats').val()) || 0;
        const availableSeats = parseInt($('#available_seats').val()) || 0;
        
        if (availableSeats > totalSeats) {
            $('#available_seats').val(totalSeats);
        }
    });
</script>
@endpush
