@extends('layouts.admin')

@section('title', 'Activity Logs')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-history me-2"></i>Activity Logs
            </h1>
            <p class="text-muted mb-0">Monitor system activities and user actions</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary" onclick="refreshLogs()">
                <i class="fas fa-sync-alt me-1"></i>Refresh
            </button>
            <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#filterModal">
                <i class="fas fa-filter me-1"></i>Filter
            </button>
            <button class="btn btn-success" onclick="exportLogs()">
                <i class="fas fa-download me-1"></i>Export
            </button>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Today's Activities
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">247</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                This Week
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">1,429</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-week fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Active Users
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">24</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Critical Events
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">3</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Logs Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Recent Activity Logs</h6>
            <div class="d-flex gap-2">
                <select class="form-select form-select-sm" id="logTypeFilter" style="width: auto;">
                    <option value="">All Types</option>
                    <option value="login">Login</option>
                    <option value="logout">Logout</option>
                    <option value="create">Create</option>
                    <option value="update">Update</option>
                    <option value="delete">Delete</option>
                    <option value="booking">Booking</option>
                    <option value="payment">Payment</option>
                </select>
                <input type="text" class="form-control form-control-sm" placeholder="Search..." id="searchLogs" style="width: 200px;">
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="activityLogsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Timestamp</th>
                            <th>User</th>
                            <th>Action</th>
                            <th>Description</th>
                            <th>IP Address</th>
                            <th>User Agent</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Sample Activity Logs -->
                        <tr>
                            <td>
                                <span class="text-muted small">2025-07-03 15:30:45</span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm me-2">
                                        <div class="avatar-title bg-primary rounded-circle">
                                            <i class="fas fa-user"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="fw-bold">FlyNow Admin</div>
                                        <small class="text-muted"><EMAIL></small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-success">Login</span>
                            </td>
                            <td>User logged into admin dashboard</td>
                            <td><code>127.0.0.1</code></td>
                            <td class="text-truncate" style="max-width: 200px;">
                                <span title="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36">
                                    Mozilla/5.0 (Windows NT 10.0...)
                                </span>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-info" onclick="viewLogDetails(1)">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <span class="text-muted small">2025-07-03 15:25:12</span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm me-2">
                                        <div class="avatar-title bg-info rounded-circle">
                                            <i class="fas fa-user"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="fw-bold">John Smith</div>
                                        <small class="text-muted"><EMAIL></small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-primary">Create</span>
                            </td>
                            <td>Created new flight booking (REF: ABC123)</td>
                            <td><code>*************</code></td>
                            <td class="text-truncate" style="max-width: 200px;">
                                <span title="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36">
                                    Mozilla/5.0 (Macintosh...)
                                </span>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-info" onclick="viewLogDetails(2)">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <span class="text-muted small">2025-07-03 15:20:33</span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm me-2">
                                        <div class="avatar-title bg-warning rounded-circle">
                                            <i class="fas fa-user"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="fw-bold">Manager User</div>
                                        <small class="text-muted"><EMAIL></small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-warning">Update</span>
                            </td>
                            <td>Updated flight FL001 departure time</td>
                            <td><code>10.0.0.50</code></td>
                            <td class="text-truncate" style="max-width: 200px;">
                                <span title="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36">
                                    Mozilla/5.0 (Windows NT 10.0...)
                                </span>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-info" onclick="viewLogDetails(3)">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <span class="text-muted small">2025-07-03 15:15:22</span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm me-2">
                                        <div class="avatar-title bg-danger rounded-circle">
                                            <i class="fas fa-user"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="fw-bold">System</div>
                                        <small class="text-muted"><EMAIL></small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-danger">Delete</span>
                            </td>
                            <td>Cancelled booking DEF456 due to payment failure</td>
                            <td><code>-</code></td>
                            <td>System Process</td>
                            <td>
                                <button class="btn btn-sm btn-outline-info" onclick="viewLogDetails(4)">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <span class="text-muted small">2025-07-03 15:10:15</span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm me-2">
                                        <div class="avatar-title bg-success rounded-circle">
                                            <i class="fas fa-user"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="fw-bold">Jane Doe</div>
                                        <small class="text-muted"><EMAIL></small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">Payment</span>
                            </td>
                            <td>Completed payment for booking GHI789 ($450.00)</td>
                            <td><code>************</code></td>
                            <td class="text-truncate" style="max-width: 200px;">
                                <span title="Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15">
                                    Mozilla/5.0 (iPhone...)
                                </span>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-info" onclick="viewLogDetails(5)">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <nav aria-label="Activity logs pagination">
                <ul class="pagination justify-content-center">
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1">Previous</a>
                    </li>
                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                    <li class="page-item">
                        <a class="page-link" href="#">Next</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- Filter Modal -->
<div class="modal fade" id="filterModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-filter me-2"></i>Filter Activity Logs</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="filterDateFrom" class="form-label">Date From</label>
                            <input type="date" class="form-control" id="filterDateFrom">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="filterDateTo" class="form-label">Date To</label>
                            <input type="date" class="form-control" id="filterDateTo">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="filterUser" class="form-label">User</label>
                            <select class="form-select" id="filterUser">
                                <option value="">All Users</option>
                                <option value="admin">FlyNow Admin</option>
                                <option value="manager">Manager User</option>
                                <option value="staff">Staff User</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="filterAction" class="form-label">Action Type</label>
                            <select class="form-select" id="filterAction">
                                <option value="">All Actions</option>
                                <option value="login">Login</option>
                                <option value="logout">Logout</option>
                                <option value="create">Create</option>
                                <option value="update">Update</option>
                                <option value="delete">Delete</option>
                                <option value="booking">Booking</option>
                                <option value="payment">Payment</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="filterDescription" class="form-label">Description Contains</label>
                        <input type="text" class="form-control" id="filterDescription" placeholder="Search in descriptions...">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-outline-warning" onclick="clearFilters()">Clear Filters</button>
                <button type="button" class="btn btn-primary" onclick="applyFilters()">Apply Filters</button>
            </div>
        </div>
    </div>
</div>

<!-- Log Details Modal -->
<div class="modal fade" id="logDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-info-circle me-2"></i>Activity Log Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="fw-bold text-primary">Basic Information</h6>
                        <table class="table table-sm">
                            <tr>
                                <td class="fw-bold">Timestamp:</td>
                                <td id="detailTimestamp">-</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">User:</td>
                                <td id="detailUser">-</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Action:</td>
                                <td id="detailAction">-</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">IP Address:</td>
                                <td id="detailIP">-</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="fw-bold text-success">Technical Details</h6>
                        <table class="table table-sm">
                            <tr>
                                <td class="fw-bold">User Agent:</td>
                                <td id="detailUserAgent" class="text-break">-</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Session ID:</td>
                                <td id="detailSession">-</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Request ID:</td>
                                <td id="detailRequestId">-</td>
                            </tr>
                        </table>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <h6 class="fw-bold text-info">Description</h6>
                        <div class="bg-light p-3 rounded">
                            <p id="detailDescription" class="mb-0">-</p>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <h6 class="fw-bold text-warning">Additional Properties</h6>
                        <div class="bg-light p-3 rounded">
                            <pre id="detailProperties" class="mb-0 small">-</pre>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-outline-primary" onclick="exportSingleLog()">
                    <i class="fas fa-download me-1"></i>Export This Log
                </button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
// Refresh logs
function refreshLogs() {
    // Show loading state
    const table = document.getElementById('activityLogsTable');
    const tbody = table.querySelector('tbody');
    tbody.innerHTML = '<tr><td colspan="7" class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>Loading...</td></tr>';

    // Simulate refresh (replace with actual AJAX call)
    setTimeout(() => {
        location.reload();
    }, 1000);
}

// Export logs
function exportLogs() {
    // Simulate export functionality
    alert('Export functionality would generate a CSV/Excel file with filtered activity logs.');
}

// View log details
function viewLogDetails(logId) {
    // Sample data - replace with actual AJAX call
    const sampleData = {
        1: {
            timestamp: '2025-07-03 15:30:45',
            user: 'FlyNow Admin (<EMAIL>)',
            action: 'Login',
            ip: '127.0.0.1',
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            session: 'sess_abc123def456',
            requestId: 'req_789xyz012',
            description: 'User successfully logged into the admin dashboard using email and password authentication.',
            properties: JSON.stringify({
                login_method: 'email_password',
                remember_me: false,
                previous_login: '2025-07-02 14:22:15',
                login_attempts: 1
            }, null, 2)
        },
        2: {
            timestamp: '2025-07-03 15:25:12',
            user: 'John Smith (<EMAIL>)',
            action: 'Create',
            ip: '*************',
            userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            session: 'sess_def456ghi789',
            requestId: 'req_012abc345',
            description: 'Created new flight booking with reference ABC123 for flight FL001 from London to New York.',
            properties: JSON.stringify({
                booking_reference: 'ABC123',
                flight_id: 'FL001',
                passenger_count: 2,
                total_amount: 850.00,
                payment_method: 'credit_card'
            }, null, 2)
        }
    };

    const data = sampleData[logId] || sampleData[1];

    // Populate modal
    document.getElementById('detailTimestamp').textContent = data.timestamp;
    document.getElementById('detailUser').textContent = data.user;
    document.getElementById('detailAction').innerHTML = `<span class="badge bg-success">${data.action}</span>`;
    document.getElementById('detailIP').innerHTML = `<code>${data.ip}</code>`;
    document.getElementById('detailUserAgent').textContent = data.userAgent;
    document.getElementById('detailSession').innerHTML = `<code>${data.session}</code>`;
    document.getElementById('detailRequestId').innerHTML = `<code>${data.requestId}</code>`;
    document.getElementById('detailDescription').textContent = data.description;
    document.getElementById('detailProperties').textContent = data.properties;

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('logDetailsModal'));
    modal.show();
}

// Filter functions
function applyFilters() {
    const filters = {
        dateFrom: document.getElementById('filterDateFrom').value,
        dateTo: document.getElementById('filterDateTo').value,
        user: document.getElementById('filterUser').value,
        action: document.getElementById('filterAction').value,
        description: document.getElementById('filterDescription').value
    };

    console.log('Applying filters:', filters);

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('filterModal'));
    modal.hide();

    // Show loading and simulate filter application
    refreshLogs();
}

function clearFilters() {
    document.getElementById('filterDateFrom').value = '';
    document.getElementById('filterDateTo').value = '';
    document.getElementById('filterUser').value = '';
    document.getElementById('filterAction').value = '';
    document.getElementById('filterDescription').value = '';
}

function exportSingleLog() {
    alert('Single log export functionality would generate a detailed report for this specific activity.');
}

// Search functionality
document.getElementById('searchLogs').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const rows = document.querySelectorAll('#activityLogsTable tbody tr');

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});

// Log type filter
document.getElementById('logTypeFilter').addEventListener('change', function(e) {
    const filterType = e.target.value.toLowerCase();
    const rows = document.querySelectorAll('#activityLogsTable tbody tr');

    rows.forEach(row => {
        if (!filterType) {
            row.style.display = '';
            return;
        }

        const actionBadge = row.querySelector('.badge');
        if (actionBadge) {
            const actionText = actionBadge.textContent.toLowerCase();
            row.style.display = actionText.includes(filterType) ? '' : 'none';
        }
    });
});
</script>
@endpush
