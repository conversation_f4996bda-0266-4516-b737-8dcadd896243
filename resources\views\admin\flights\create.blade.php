@extends('layouts.admin')

@section('title', 'Create Flight')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h4><i class="fas fa-plus-circle me-2"></i>Create New Flight</h4>
                <a href="{{ route('admin.flights.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Flights
                </a>
            </div>
        </div>
    </div>

    <!-- Create Form -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="fas fa-plane me-2"></i>Flight Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.flights.store') }}" method="POST" id="flight-form">
                        @csrf
                        
                        <div class="row">
                            <!-- Basic Flight Information -->
                            <div class="col-lg-6">
                                <h6 class="text-primary mb-3">Basic Information</h6>
                                
                                <div class="mb-3">
                                    <label for="flight_number" class="form-label">Flight Number *</label>
                                    <input type="text" class="form-control @error('flight_number') is-invalid @enderror" 
                                           id="flight_number" name="flight_number" value="{{ old('flight_number') }}" 
                                           placeholder="e.g., PK123" required>
                                    @error('flight_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="airline_id" class="form-label">Airline *</label>
                                    <select class="form-select @error('airline_id') is-invalid @enderror" 
                                            id="airline_id" name="airline_id" required>
                                        <option value="">Select Airline</option>
                                        @foreach($airlines as $airline)
                                            <option value="{{ $airline->id }}" {{ old('airline_id') == $airline->id ? 'selected' : '' }}>
                                                {{ $airline->name }} ({{ $airline->code }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('airline_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="aircraft_type" class="form-label">Aircraft Type *</label>
                                    <input type="text" class="form-control @error('aircraft_type') is-invalid @enderror" 
                                           id="aircraft_type" name="aircraft_type" value="{{ old('aircraft_type') }}" 
                                           placeholder="e.g., Boeing 737-800" required>
                                    @error('aircraft_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="flight_type" class="form-label">Flight Type *</label>
                                            <select class="form-select @error('flight_type') is-invalid @enderror" 
                                                    id="flight_type" name="flight_type" required>
                                                <option value="">Select Type</option>
                                                <option value="Domestic" {{ old('flight_type') == 'Domestic' ? 'selected' : '' }}>Domestic</option>
                                                <option value="International" {{ old('flight_type') == 'International' ? 'selected' : '' }}>International</option>
                                            </select>
                                            @error('flight_type')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="class_type" class="form-label">Class Type *</label>
                                            <select class="form-select @error('class_type') is-invalid @enderror" 
                                                    id="class_type" name="class_type" required>
                                                <option value="">Select Class</option>
                                                <option value="Economy" {{ old('class_type') == 'Economy' ? 'selected' : '' }}>Economy</option>
                                                <option value="Business" {{ old('class_type') == 'Business' ? 'selected' : '' }}>Business</option>
                                                <option value="First" {{ old('class_type') == 'First' ? 'selected' : '' }}>First</option>
                                            </select>
                                            @error('class_type')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Route Information -->
                            <div class="col-lg-6">
                                <h6 class="text-primary mb-3">Route Information</h6>
                                
                                <div class="mb-3">
                                    <label for="departure_airport_id" class="form-label">Departure Airport *</label>
                                    <select class="form-select @error('departure_airport_id') is-invalid @enderror" 
                                            id="departure_airport_id" name="departure_airport_id" required>
                                        <option value="">Select Departure Airport</option>
                                        @foreach($airports as $airport)
                                            <option value="{{ $airport->id }}" {{ old('departure_airport_id') == $airport->id ? 'selected' : '' }}>
                                                {{ $airport->code }} - {{ $airport->name }}, {{ $airport->city }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('departure_airport_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="arrival_airport_id" class="form-label">Arrival Airport *</label>
                                    <select class="form-select @error('arrival_airport_id') is-invalid @enderror" 
                                            id="arrival_airport_id" name="arrival_airport_id" required>
                                        <option value="">Select Arrival Airport</option>
                                        @foreach($airports as $airport)
                                            <option value="{{ $airport->id }}" {{ old('arrival_airport_id') == $airport->id ? 'selected' : '' }}>
                                                {{ $airport->code }} - {{ $airport->name }}, {{ $airport->city }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('arrival_airport_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="departure_time" class="form-label">Departure Time *</label>
                                            <input type="datetime-local" class="form-control @error('departure_time') is-invalid @enderror" 
                                                   id="departure_time" name="departure_time" value="{{ old('departure_time') }}" required>
                                            @error('departure_time')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="arrival_time" class="form-label">Arrival Time *</label>
                                            <input type="datetime-local" class="form-control @error('arrival_time') is-invalid @enderror" 
                                                   id="arrival_time" name="arrival_time" value="{{ old('arrival_time') }}" required>
                                            @error('arrival_time')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="total_seats" class="form-label">Total Seats *</label>
                                            <input type="number" class="form-control @error('total_seats') is-invalid @enderror" 
                                                   id="total_seats" name="total_seats" value="{{ old('total_seats') }}" 
                                                   min="1" max="1000" required>
                                            @error('total_seats')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="base_price" class="form-label">Base Price (PKR) *</label>
                                            <input type="number" class="form-control @error('base_price') is-invalid @enderror" 
                                                   id="base_price" name="base_price" value="{{ old('base_price') }}" 
                                                   min="0" step="0.01" required>
                                            @error('base_price')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h6 class="text-primary mb-3">Additional Information</h6>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="gate" class="form-label">Gate</label>
                                            <input type="text" class="form-control @error('gate') is-invalid @enderror" 
                                                   id="gate" name="gate" value="{{ old('gate') }}" 
                                                   placeholder="e.g., A12">
                                            @error('gate')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="notes" class="form-label">Notes</label>
                                    <textarea class="form-control @error('notes') is-invalid @enderror" 
                                              id="notes" name="notes" rows="3" 
                                              placeholder="Additional notes about the flight">{{ old('notes') }}</textarea>
                                    @error('notes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('admin.flights.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-1"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>Create Flight
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Prevent selecting same airport for departure and arrival
    const departureSelect = document.getElementById('departure_airport_id');
    const arrivalSelect = document.getElementById('arrival_airport_id');
    
    function updateAirportOptions() {
        const departureValue = departureSelect.value;
        const arrivalValue = arrivalSelect.value;
        
        // Reset all options
        Array.from(arrivalSelect.options).forEach(option => {
            option.disabled = false;
        });
        Array.from(departureSelect.options).forEach(option => {
            option.disabled = false;
        });
        
        // Disable selected options
        if (departureValue) {
            Array.from(arrivalSelect.options).forEach(option => {
                if (option.value === departureValue) {
                    option.disabled = true;
                }
            });
        }
        
        if (arrivalValue) {
            Array.from(departureSelect.options).forEach(option => {
                if (option.value === arrivalValue) {
                    option.disabled = true;
                }
            });
        }
    }
    
    departureSelect.addEventListener('change', updateAirportOptions);
    arrivalSelect.addEventListener('change', updateAirportOptions);
    
    // Initial check
    updateAirportOptions();
    
    // Validate arrival time is after departure time
    const departureTime = document.getElementById('departure_time');
    const arrivalTime = document.getElementById('arrival_time');
    
    function validateTimes() {
        if (departureTime.value && arrivalTime.value) {
            const departure = new Date(departureTime.value);
            const arrival = new Date(arrivalTime.value);
            
            if (arrival <= departure) {
                arrivalTime.setCustomValidity('Arrival time must be after departure time');
            } else {
                arrivalTime.setCustomValidity('');
            }
        }
    }
    
    departureTime.addEventListener('change', validateTimes);
    arrivalTime.addEventListener('change', validateTimes);
});
</script>
@endpush
@endsection
