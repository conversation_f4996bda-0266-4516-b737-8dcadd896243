<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Airport Autocomplete Test</title>
    <script src="https://code.jquery.com/jquery-3.6.3.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        
        .test-container {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .airport-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e9ecef;
            border-top: none;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .airport-suggestion {
            padding: 12px 16px;
            cursor: pointer;
            border-bottom: 1px solid #f8f9fa;
            transition: background-color 0.2s ease;
        }

        .airport-suggestion:hover,
        .airport-suggestion.active {
            background-color: #f8f9fa;
        }

        .airport-suggestion:last-child {
            border-bottom: none;
        }

        .airport-suggestion .airport-code {
            font-weight: 600;
            color: #007bff;
            font-size: 14px;
        }

        .airport-suggestion .airport-name {
            color: #333;
            font-size: 14px;
            margin-bottom: 2px;
        }

        .airport-suggestion .airport-location {
            color: #6c757d;
            font-size: 12px;
        }

        .airport-autocomplete {
            position: relative;
        }
        
        .test-results {
            background: white;
            padding: 20px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .success {
            color: #28a745;
        }
        
        .error {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <h1>🛫 Airport Autocomplete Test</h1>
    
    <div class="test-container">
        <h2>Test Airport Autocomplete</h2>
        
        <div class="form-group">
            <label for="fromAirport">From Airport:</label>
            <div class="airport-autocomplete">
                <input type="text" id="fromAirport" placeholder="Type airport code or name (e.g., 'new', 'jfk', 'london')">
                <div class="airport-suggestions" id="fromSuggestions"></div>
            </div>
        </div>
        
        <div class="form-group">
            <label for="toAirport">To Airport:</label>
            <div class="airport-autocomplete">
                <input type="text" id="toAirport" placeholder="Type airport code or name">
                <div class="airport-suggestions" id="toSuggestions"></div>
            </div>
        </div>
        
        <button class="btn" onclick="testAPI()">🧪 Test API Directly</button>
        <button class="btn" onclick="testPopularAirports()">✈️ Load Popular Airports</button>
        <button class="btn" onclick="clearResults()">🗑️ Clear Results</button>
    </div>
    
    <div class="test-results" id="testResults">
        <h3>Test Results</h3>
        <p>Type in the airport fields above or click the test buttons to see results.</p>
    </div>

    <script>
        $(document).ready(function() {
            initializeAutocomplete();
        });
        
        function initializeAutocomplete() {
            $('.airport-autocomplete input').each(function() {
                const input = $(this);
                const suggestionsContainer = input.siblings('.airport-suggestions');
                let searchTimeout;
                
                input.on('input', function() {
                    const query = $(this).val().trim();
                    
                    clearTimeout(searchTimeout);
                    
                    if (query.length < 1) {
                        hideSuggestions(suggestionsContainer);
                        return;
                    }
                    
                    showLoading(suggestionsContainer);
                    
                    searchTimeout = setTimeout(function() {
                        searchAirports(query, suggestionsContainer, input);
                    }, 300);
                });
                
                input.on('focus', function() {
                    if ($(this).val().trim() === '') {
                        loadPopularAirports(suggestionsContainer, input);
                    }
                });
                
                $(document).on('click', function(e) {
                    if (!$(e.target).closest('.airport-autocomplete').length) {
                        hideSuggestions(suggestionsContainer);
                    }
                });
            });
        }
        
        function searchAirports(query, container, input) {
            $.ajax({
                url: '/flynow/public/index.php/api/airports/autocomplete',
                method: 'GET',
                data: { query: query, limit: 8 },
                timeout: 3000,
                success: function(response) {
                    if (response.success && response.data.length > 0) {
                        showSuggestions(response.data, container, input, query);
                        logResult(`✅ Found ${response.data.length} airports for "${query}" in ${response.meta.response_time}`);
                    } else {
                        showNoResults(container);
                        logResult(`⚠️ No airports found for "${query}"`);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Airport search failed:', error);
                    showError(container);
                    logResult(`❌ Search failed for "${query}": ${error}`);
                }
            });
        }
        
        function loadPopularAirports(container, input) {
            $.ajax({
                url: '/flynow/public/index.php/api/airports/popular',
                method: 'GET',
                timeout: 3000,
                success: function(response) {
                    if (response.success && response.data.length > 0) {
                        showSuggestions(response.data, container, input, '', 'Popular Airports');
                        logResult(`✅ Loaded ${response.data.length} popular airports`);
                    }
                },
                error: function() {
                    logResult(`❌ Failed to load popular airports`);
                }
            });
        }
        
        function showSuggestions(airports, container, input, query = '', title = '') {
            let html = '';
            
            if (title) {
                html += `<div style="padding: 8px 16px; background: #f8f9fa; font-weight: 600; font-size: 12px; color: #6c757d; border-bottom: 1px solid #e9ecef;">${title}</div>`;
            }
            
            airports.forEach(function(airport) {
                const displayText = highlightMatch(airport.display, query);
                const locationText = highlightMatch(airport.subtitle, query);
                
                html += `
                    <div class="airport-suggestion" data-code="${airport.code}" data-name="${airport.name}" data-city="${airport.city}" data-country="${airport.country}">
                        <div class="airport-code">${airport.code}</div>
                        <div class="airport-name">${displayText}</div>
                        <div class="airport-location">${locationText}</div>
                    </div>
                `;
            });
            
            container.html(html).show();
            
            container.find('.airport-suggestion').on('click', function() {
                selectAirport($(this), input, container);
            });
        }
        
        function highlightMatch(text, query) {
            if (!query) return text;
            
            const regex = new RegExp(`(${escapeRegex(query)})`, 'gi');
            return text.replace(regex, '<span style="background-color: #fff3cd; font-weight: 600;">$1</span>');
        }
        
        function escapeRegex(string) {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        }
        
        function selectAirport(suggestion, input, container) {
            const code = suggestion.data('code');
            const name = suggestion.data('name');
            
            input.val(`${code} - ${name}`);
            hideSuggestions(container);
            
            logResult(`✈️ Selected: ${code} - ${name}`);
        }
        
        function showLoading(container) {
            container.html('<div style="padding: 12px 16px; text-align: center; color: #6c757d; font-size: 14px;"><i>🔄 Searching airports...</i></div>').show();
        }
        
        function showNoResults(container) {
            container.html('<div style="padding: 12px 16px; text-align: center; color: #6c757d; font-size: 14px; font-style: italic;">No airports found</div>').show();
        }
        
        function showError(container) {
            container.html('<div style="padding: 12px 16px; text-align: center; color: #dc3545; font-size: 14px;">Search failed. Please try again.</div>').show();
        }
        
        function hideSuggestions(container) {
            container.hide().empty();
        }
        
        function testAPI() {
            logResult('🧪 Testing API directly...');
            
            $.ajax({
                url: '/flynow/public/index.php/api/airports/autocomplete?query=new&limit=5',
                method: 'GET',
                success: function(response) {
                    logResult(`✅ API Test Success: ${JSON.stringify(response, null, 2)}`);
                },
                error: function(xhr) {
                    logResult(`❌ API Test Failed: ${xhr.responseText}`);
                }
            });
        }
        
        function testPopularAirports() {
            logResult('✈️ Testing popular airports...');
            
            $.ajax({
                url: '/flynow/public/index.php/api/airports/popular',
                method: 'GET',
                success: function(response) {
                    logResult(`✅ Popular Airports Success: Found ${response.data.length} airports`);
                    response.data.forEach(function(airport) {
                        logResult(`   • ${airport.code} - ${airport.name} (${airport.city}, ${airport.country})`);
                    });
                },
                error: function(xhr) {
                    logResult(`❌ Popular Airports Failed: ${xhr.responseText}`);
                }
            });
        }
        
        function logResult(message) {
            const timestamp = new Date().toLocaleTimeString();
            const resultDiv = $('#testResults');
            resultDiv.append(`<div>[${timestamp}] ${message}</div>`);
            resultDiv.scrollTop(resultDiv[0].scrollHeight);
        }
        
        function clearResults() {
            $('#testResults').html('<h3>Test Results</h3><p>Results cleared.</p>');
        }
    </script>
</body>
</html>
