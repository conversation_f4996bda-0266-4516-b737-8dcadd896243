<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Booking;
use App\Models\Passenger;
use App\Services\FlightApiService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;

class BookingController extends Controller
{
    protected $flightApiService;

    public function __construct(FlightApiService $flightApiService)
    {
        $this->flightApiService = $flightApiService;
    }

    /**
     * Show booking form
     */
    public function create($flightId)
    {
        $searchParams = Session::get('flight_search_params');
        
        if (!$searchParams) {
            return redirect()->route('home')->withErrors(['booking' => 'Please perform a new search.']);
        }

        // Get flight details
        $flight = $this->getFlightById($flightId);
        
        if (!$flight) {
            return redirect()->back()->withErrors(['booking' => 'Flight not found.']);
        }

        return view('booking.create', compact('flight', 'searchParams'));
    }

    /**
     * Store booking
     */
    public function store(Request $request)
    {
        $request->validate([
            'flight_id' => 'required|string',
            'contact_email' => 'required|email',
            'contact_phone' => 'required|string',
            'passengers' => 'required|array|min:1',
            'passengers.*.first_name' => 'required|string|max:255',
            'passengers.*.last_name' => 'required|string|max:255',
            'passengers.*.date_of_birth' => 'required|date',
            'passengers.*.passport_number' => 'nullable|string|max:255',
            'passengers.*.nationality' => 'required|string|max:255',
            'passengers.*.type' => 'required|in:adult,child,infant',
        ]);

        try {
            DB::beginTransaction();

            // Generate booking reference
            $bookingReference = 'BK' . strtoupper(Str::random(8));

            // Get flight details
            $flight = $this->getFlightById($request->flight_id);
            
            if (!$flight) {
                throw new \Exception('Flight not found');
            }

            // Create booking
            $booking = Booking::create([
                'booking_reference' => $bookingReference,
                'flight_id' => $request->flight_id,
                'departure_airport' => $flight['departure_airport'],
                'arrival_airport' => $flight['arrival_airport'],
                'departure_date' => $flight['departure_date'],
                'departure_time' => $flight['departure_time'],
                'arrival_time' => $flight['arrival_time'],
                'airline_code' => $flight['airline_code'],
                'flight_number' => $flight['flight_number'],
                'contact_email' => $request->contact_email,
                'contact_phone' => $request->contact_phone,
                'total_passengers' => count($request->passengers),
                'total_amount' => $flight['price'] * count($request->passengers),
                'currency' => 'PKR',
                'status' => 'pending',
                'booking_date' => now(),
            ]);

            // Create passengers
            foreach ($request->passengers as $passengerData) {
                Passenger::create([
                    'booking_id' => $booking->id,
                    'first_name' => $passengerData['first_name'],
                    'last_name' => $passengerData['last_name'],
                    'date_of_birth' => $passengerData['date_of_birth'],
                    'passport_number' => $passengerData['passport_number'] ?? null,
                    'nationality' => $passengerData['nationality'],
                    'type' => $passengerData['type'],
                ]);
            }

            DB::commit();

            // Clear search session
            Session::forget('flight_search_params');

            return redirect()->route('booking.show', $booking->booking_reference)
                ->with('success', 'Booking created successfully!');

        } catch (\Exception $e) {
            DB::rollBack();
            
            return redirect()->back()
                ->withErrors(['booking' => 'Booking failed: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Show booking details
     */
    public function show($bookingReference)
    {
        $booking = Booking::with('passengers')
            ->where('booking_reference', $bookingReference)
            ->firstOrFail();

        return view('booking.show', compact('booking'));
    }

    /**
     * List all bookings (admin)
     */
    public function index(Request $request)
    {
        $query = Booking::with('passengers')->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('date_from')) {
            $query->whereDate('booking_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('booking_date', '<=', $request->date_to);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('booking_reference', 'like', "%{$search}%")
                  ->orWhere('contact_email', 'like', "%{$search}%")
                  ->orWhere('flight_number', 'like', "%{$search}%");
            });
        }

        $bookings = $query->paginate(20);

        return view('booking.index', compact('bookings'));
    }

    /**
     * Update booking status
     */
    public function updateStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:pending,confirmed,cancelled,completed'
        ]);

        $booking = Booking::findOrFail($id);
        $booking->update(['status' => $request->status]);

        return response()->json(['success' => true, 'message' => 'Status updated successfully']);
    }

    /**
     * Cancel booking
     */
    public function cancel($bookingReference)
    {
        $booking = Booking::where('booking_reference', $bookingReference)->firstOrFail();
        
        if ($booking->status === 'cancelled') {
            return redirect()->back()->withErrors(['booking' => 'Booking is already cancelled.']);
        }

        $booking->update(['status' => 'cancelled']);

        return redirect()->back()->with('success', 'Booking cancelled successfully.');
    }

    /**
     * Get flight by ID (mock implementation)
     */
    protected function getFlightById($flightId)
    {
        // In a real implementation, this would fetch from the flight API or database
        // For now, we'll return mock data
        return [
            'id' => $flightId,
            'departure_airport' => 'ISB',
            'arrival_airport' => 'BAH',
            'departure_date' => '2025-07-15',
            'departure_time' => '14:30',
            'arrival_time' => '17:00',
            'airline_code' => 'PK',
            'airline_name' => 'Fly Jinnah',
            'flight_number' => 'PK764',
            'price' => 84000,
            'duration' => '2h 30m',
            'stops' => 0,
        ];
    }
}
