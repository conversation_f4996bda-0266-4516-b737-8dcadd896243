<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('passengers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('booking_id')->constrained()->onDelete('cascade');
            $table->enum('title', ['Mr', 'Mrs', 'Ms', 'Miss', 'Dr']);
            $table->string('first_name');
            $table->string('last_name');
            $table->date('date_of_birth');
            $table->enum('gender', ['Male', 'Female', 'Other']);
            $table->string('passport_number')->nullable();
            $table->string('nationality', 2); // Country code
            $table->string('seat_number')->nullable();
            $table->enum('passenger_type', ['Adult', 'Child', 'Infant']);
            $table->text('special_needs')->nullable(); // Wheelchair, dietary requirements, etc.
            $table->boolean('is_frequent_flyer')->default(false);
            $table->string('frequent_flyer_number')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['first_name', 'last_name']);
            $table->index('passport_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('passengers');
    }
};
