@extends('layouts.admin')

@section('title', 'Add New Airport')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="page-title mb-2">Add New Airport</h1>
                <p class="page-subtitle mb-0">Add a new airport to the database</p>
            </div>
            <a href="{{ route('admin.airports.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Airports
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-xl-8 col-lg-10">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Airport Information</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.airports.store') }}" method="POST">
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="code" class="form-label">Airport Code (IATA) <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('code') is-invalid @enderror" 
                                       id="code" name="code" value="{{ old('code') }}" 
                                       placeholder="e.g., JFK" maxlength="3" style="text-transform: uppercase;" required>
                                @error('code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">3-letter IATA airport code</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Airport Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name') }}" 
                                       placeholder="e.g., John F. Kennedy International Airport" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="city" class="form-label">City <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('city') is-invalid @enderror" 
                                       id="city" name="city" value="{{ old('city') }}" 
                                       placeholder="e.g., New York" required>
                                @error('city')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="country" class="form-label">Country <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('country') is-invalid @enderror" 
                                       id="country" name="country" value="{{ old('country') }}" 
                                       placeholder="e.g., United States" required>
                                @error('country')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="latitude" class="form-label">Latitude</label>
                                <input type="number" class="form-control @error('latitude') is-invalid @enderror" 
                                       id="latitude" name="latitude" value="{{ old('latitude') }}" 
                                       step="0.00000001" min="-90" max="90"
                                       placeholder="e.g., 40.6413">
                                @error('latitude')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Decimal degrees (-90 to 90)</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="longitude" class="form-label">Longitude</label>
                                <input type="number" class="form-control @error('longitude') is-invalid @enderror" 
                                       id="longitude" name="longitude" value="{{ old('longitude') }}" 
                                       step="0.00000001" min="-180" max="180"
                                       placeholder="e.g., -73.7781">
                                @error('longitude')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Decimal degrees (-180 to 180)</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="timezone" class="form-label">Timezone</label>
                                <input type="text" class="form-control @error('timezone') is-invalid @enderror" 
                                       id="timezone" name="timezone" value="{{ old('timezone') }}" 
                                       placeholder="e.g., America/New_York">
                                @error('timezone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">IANA timezone identifier</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check form-switch mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_active" 
                                           name="is_active" {{ old('is_active', true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        Active Airport
                                    </label>
                                </div>
                                <div class="form-text">Only active airports appear in autocomplete</div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.airports.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Save Airport
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-xl-4 col-lg-2">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">Quick Tips</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-1"></i>Airport Code</h6>
                    <p class="mb-0 small">Use the 3-letter IATA code (e.g., JFK, LAX, LHR). This is what users will search for.</p>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-1"></i>Coordinates</h6>
                    <p class="mb-0 small">Latitude and longitude are optional but help with distance calculations and mapping features.</p>
                </div>
                
                <div class="alert alert-success">
                    <h6><i class="fas fa-clock me-1"></i>Timezone</h6>
                    <p class="mb-0 small">Use IANA timezone identifiers like "America/New_York" or "Europe/London".</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Auto-uppercase airport code
    $('#code').on('input', function() {
        this.value = this.value.toUpperCase();
    });
    
    // Auto-suggest timezone based on coordinates
    $('#latitude, #longitude').on('blur', function() {
        const lat = $('#latitude').val();
        const lng = $('#longitude').val();
        
        if (lat && lng && !$('#timezone').val()) {
            // Simple timezone suggestions based on coordinates
            const timezones = {
                'US_East': 'America/New_York',
                'US_Central': 'America/Chicago', 
                'US_Mountain': 'America/Denver',
                'US_Pacific': 'America/Los_Angeles',
                'Europe': 'Europe/London',
                'Asia': 'Asia/Tokyo'
            };
            
            // Very basic timezone detection
            if (lat >= 25 && lat <= 50 && lng >= -125 && lng <= -65) {
                if (lng >= -90) $('#timezone').val(timezones.US_East);
                else if (lng >= -105) $('#timezone').val(timezones.US_Central);
                else if (lng >= -115) $('#timezone').val(timezones.US_Mountain);
                else $('#timezone').val(timezones.US_Pacific);
            }
        }
    });
});
</script>
@endpush
