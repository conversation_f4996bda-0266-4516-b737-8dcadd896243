<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;
use Carbon\Carbon;

class Pnr extends Model
{
    protected $fillable = [
        'pnr_code',
        'flight_id',
        'total_inventory_seats',
        'live_seats',
        'booked_seats',
        'status',
        'base_price',
        'currency',
        'notes',
        'is_active',
        'live_date',
        'sold_out_date',
    ];

    protected $casts = [
        'total_inventory_seats' => 'integer',
        'live_seats' => 'integer',
        'booked_seats' => 'integer',
        'base_price' => 'decimal:2',
        'is_active' => 'boolean',
        'live_date' => 'datetime',
        'sold_out_date' => 'datetime',
    ];

    /**
     * Boot method to generate PNR code
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($pnr) {
            if (!$pnr->pnr_code) {
                $pnr->pnr_code = static::generatePnrCode();
            }
        });

        static::updating(function ($pnr) {
            // Auto-update status based on seat availability
            $pnr->updateStatus();
        });
    }

    /**
     * Generate unique PNR code
     */
    public static function generatePnrCode(): string
    {
        do {
            $code = strtoupper(Str::random(6));
        } while (static::where('pnr_code', $code)->exists());

        return $code;
    }

    /**
     * Get the flight for this PNR
     */
    public function flight(): BelongsTo
    {
        return $this->belongsTo(Flight::class);
    }

    /**
     * Get all bookings for this PNR
     */
    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Get all seat locks for this PNR
     */
    public function seatLocks(): HasMany
    {
        return $this->hasMany(SeatLock::class);
    }

    /**
     * Get active seat locks
     */
    public function activeSeatLocks(): HasMany
    {
        return $this->seatLocks()->where('status', 'active')->where('expires_at', '>', now());
    }

    /**
     * Get available seats (live seats minus booked and locked seats)
     */
    public function getAvailableSeatsAttribute(): int
    {
        $lockedSeats = $this->activeSeatLocks()->sum('seats_locked');
        return max(0, $this->live_seats - $this->booked_seats - $lockedSeats);
    }

    /**
     * Get total locked seats
     */
    public function getLockedSeatsAttribute(): int
    {
        return $this->activeSeatLocks()->sum('seats_locked');
    }

    /**
     * Check if PNR has available seats
     */
    public function hasAvailableSeats(): bool
    {
        return $this->available_seats > 0;
    }

    /**
     * Check if PNR is sold out
     */
    public function isSoldOut(): bool
    {
        return $this->booked_seats >= $this->live_seats;
    }

    /**
     * Check if PNR is available for booking
     */
    public function isAvailable(): bool
    {
        return $this->status === 'available' && $this->is_active && $this->hasAvailableSeats();
    }

    /**
     * Update PNR status based on current state
     */
    public function updateStatus(): void
    {
        if (!$this->is_active) {
            $this->status = 'cancelled';
            return;
        }

        if ($this->live_seats <= 0) {
            $this->status = 'inventory_only';
            return;
        }

        if ($this->booked_seats >= $this->live_seats) {
            $this->status = 'sold_out';
            if (!$this->sold_out_date) {
                $this->sold_out_date = now();
            }
            return;
        }

        $this->status = 'available';
    }

    /**
     * Make seats live for booking
     */
    public function makeSeatsLive(int $seats): bool
    {
        if ($seats > $this->total_inventory_seats) {
            return false;
        }

        $this->live_seats = $seats;
        if (!$this->live_date) {
            $this->live_date = now();
        }
        $this->updateStatus();
        $this->save();

        return true;
    }

    /**
     * Lock seats for a user session
     */
    public function lockSeats(int $seats, string $sessionId, string $userIp = null): ?SeatLock
    {
        if ($seats > $this->available_seats) {
            return null;
        }

        // Remove any existing locks for this session
        $this->seatLocks()
            ->where('session_id', $sessionId)
            ->where('status', 'active')
            ->update(['status' => 'cancelled']);

        // Create new lock
        $lock = $this->seatLocks()->create([
            'session_id' => $sessionId,
            'user_ip' => $userIp,
            'seats_locked' => $seats,
            'locked_at' => now(),
            'expires_at' => now()->addSeconds(55), // 55 seconds lock
            'status' => 'active',
        ]);

        return $lock;
    }

    /**
     * Release expired locks
     */
    public static function releaseExpiredLocks(): int
    {
        return SeatLock::where('status', 'active')
            ->where('expires_at', '<', now())
            ->update(['status' => 'expired']);
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'available' => 'success', // Green
            'sold_out' => 'danger',   // Red
            'inventory_only' => 'secondary', // Gray
            'cancelled' => 'warning', // Orange
            default => 'secondary'
        };
    }

    /**
     * Get status display name
     */
    public function getStatusDisplayAttribute(): string
    {
        return match($this->status) {
            'available' => 'Available',
            'sold_out' => 'Sold Out',
            'inventory_only' => 'Inventory Only',
            'cancelled' => 'Cancelled',
            default => ucfirst($this->status)
        };
    }

    /**
     * Scope for available PNRs
     */
    public function scopeAvailable($query)
    {
        return $query->where('status', 'available')
                    ->where('is_active', true)
                    ->whereRaw('live_seats > booked_seats');
    }

    /**
     * Scope for sold out PNRs
     */
    public function scopeSoldOut($query)
    {
        return $query->where('status', 'sold_out');
    }

    /**
     * Scope for inventory only PNRs
     */
    public function scopeInventoryOnly($query)
    {
        return $query->where('status', 'inventory_only');
    }
}
