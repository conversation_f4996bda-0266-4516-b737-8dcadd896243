<?php

namespace App\Services\FlightProviders;

interface FlightProviderInterface
{
    /**
     * Check if the provider is enabled
     *
     * @return bool
     */
    public function isEnabled(): bool;

    /**
     * Search for flights
     *
     * @param array $searchParams
     * @return array
     */
    public function searchFlights(array $searchParams): array;

    /**
     * Get flight details by ID
     *
     * @param string $flightId
     * @return array|null
     */
    public function getFlightDetails(string $flightId): ?array;

    /**
     * Get real-time flight prices
     *
     * @param array $flightIds
     * @return array
     */
    public function getFlightPrices(array $flightIds): array;

    /**
     * Book a flight
     *
     * @param array $bookingData
     * @return array
     */
    public function bookFlight(array $bookingData): array;

    /**
     * Search airports
     *
     * @param string $query
     * @return array
     */
    public function searchAirports(string $query): array;

    /**
     * Get airline information
     *
     * @param string $airlineCode
     * @return array|null
     */
    public function getAirlineInfo(string $airlineCode): ?array;

    /**
     * Check flight availability
     *
     * @param string $flightId
     * @return bool
     */
    public function checkAvailability(string $flightId): bool;

    /**
     * Get booking status
     *
     * @param string $bookingReference
     * @return array|null
     */
    public function getBookingStatus(string $bookingReference): ?array;
}
