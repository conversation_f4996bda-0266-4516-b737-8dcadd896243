@extends('layouts.admin')

@section('page-title', 'Dashboard')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="page-title mb-2">Dashboard</h1>
                <p class="page-subtitle mb-0">Welcome back! Here's what's happening with your airline system.</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-primary btn-sm" onclick="refreshDashboard()">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
                <div class="dropdown">
                    <button class="btn btn-primary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-calendar me-1"></i>Last 7 Days
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="changePeriod('today')">Today</a></li>
                        <li><a class="dropdown-item" href="#" onclick="changePeriod('week')">Last 7 Days</a></li>
                        <li><a class="dropdown-item" href="#" onclick="changePeriod('month')">Last 30 Days</a></li>
                        <li><a class="dropdown-item" href="#" onclick="changePeriod('year')">Last Year</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modern Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="modern-stats-card gradient-primary">
            <div class="stats-content">
                <div class="stats-header">
                    <div class="stats-icon-modern">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stats-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                        <span class="trend-value">+12%</span>
                    </div>
                </div>
                <div class="stats-body">
                    <div class="stats-number-modern">{{ number_format($stats['total_users']) }}</div>
                    <div class="stats-label-modern">Total Users</div>
                    <div class="stats-description">Registered members</div>
                </div>
            </div>
            <div class="stats-chart">
                <canvas id="usersChart" width="100" height="40"></canvas>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="modern-stats-card gradient-success">
            <div class="stats-content">
                <div class="stats-header">
                    <div class="stats-icon-modern">
                        <i class="fas fa-plane"></i>
                    </div>
                    <div class="stats-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                        <span class="trend-value">+8%</span>
                    </div>
                </div>
                <div class="stats-body">
                    <div class="stats-number-modern">{{ number_format($stats['total_flights'] ?? 156) }}</div>
                    <div class="stats-label-modern">Active Flights</div>
                    <div class="stats-description">Available routes</div>
                </div>
            </div>
            <div class="stats-chart">
                <canvas id="flightsChart" width="100" height="40"></canvas>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="modern-stats-card gradient-info">
            <div class="stats-content">
                <div class="stats-header">
                    <div class="stats-icon-modern">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                    <div class="stats-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                        <span class="trend-value">+24%</span>
                    </div>
                </div>
                <div class="stats-body">
                    <div class="stats-number-modern">{{ number_format($stats['total_bookings'] ?? 1247) }}</div>
                    <div class="stats-label-modern">Total Bookings</div>
                    <div class="stats-description">This month</div>
                </div>
            </div>
            <div class="stats-chart">
                <canvas id="bookingsChart" width="100" height="40"></canvas>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="modern-stats-card gradient-warning">
            <div class="stats-content">
                <div class="stats-header">
                    <div class="stats-icon-modern">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stats-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                        <span class="trend-value">+18%</span>
                    </div>
                </div>
                <div class="stats-body">
                    <div class="stats-number-modern">${{ number_format($stats['total_revenue'] ?? 89750) }}</div>
                    <div class="stats-label-modern">Revenue</div>
                    <div class="stats-description">This month</div>
                </div>
            </div>
            <div class="stats-chart">
                <canvas id="revenueChart" width="100" height="40"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Modern Analytics Section -->
<div class="row mb-4">
    <!-- Main Analytics Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="modern-widget">
            <div class="widget-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="widget-title">Analytics Overview</h5>
                        <p class="widget-subtitle">User registrations and booking trends</p>
                    </div>
                    <div class="widget-controls">
                        <div class="btn-group btn-group-sm" role="group">
                            <input type="radio" class="btn-check" name="chartType" id="lineChart" checked>
                            <label class="btn btn-outline-primary" for="lineChart">
                                <i class="fas fa-chart-line"></i>
                            </label>
                            <input type="radio" class="btn-check" name="chartType" id="barChart">
                            <label class="btn btn-outline-primary" for="barChart">
                                <i class="fas fa-chart-bar"></i>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="widget-body">
                <div class="chart-container">
                    <canvas id="mainAnalyticsChart" height="80"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Feed -->
    <div class="col-xl-4 col-lg-5">
        <div class="modern-widget">
            <div class="widget-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="widget-title">Live Activity</h5>
                        <p class="widget-subtitle">Real-time system events</p>
                    </div>
                    <div class="activity-indicator">
                        <span class="pulse-dot"></span>
                        <span class="text-success small">Live</span>
                    </div>
                </div>
            </div>
            <div class="widget-body">
                @if($recentActivities->count() > 0)
                    <div class="modern-activity-list">
                        @foreach($recentActivities as $activity)
                            <div class="modern-activity-item">
                                <div class="activity-avatar">
                                    @if($activity->causer)
                                        <img src="https://ui-avatars.com/api/?name={{ urlencode($activity->causer->name) }}&background=667eea&color=fff&size=32"
                                             alt="{{ $activity->causer->name }}" class="avatar-img">
                                    @else
                                        <div class="system-avatar">
                                            <i class="fas fa-cog"></i>
                                        </div>
                                    @endif
                                </div>
                                <div class="activity-content">
                                    <div class="activity-text">{{ $activity->description }}</div>
                                    <div class="activity-meta">
                                        @if($activity->causer)
                                            <span class="activity-user">{{ $activity->causer->name }}</span>
                                        @else
                                            <span class="activity-user">System</span>
                                        @endif
                                        <span class="activity-time">{{ $activity->created_at->diffForHumans() }}</span>
                                    </div>
                                </div>
                                <div class="activity-type">
                                    @php
                                        $activityIcon = 'fas fa-info-circle';
                                        $activityColor = 'text-primary';
                                        if(str_contains(strtolower($activity->description), 'created')) {
                                            $activityIcon = 'fas fa-plus-circle';
                                            $activityColor = 'text-success';
                                        } elseif(str_contains(strtolower($activity->description), 'updated')) {
                                            $activityIcon = 'fas fa-edit';
                                            $activityColor = 'text-warning';
                                        } elseif(str_contains(strtolower($activity->description), 'deleted')) {
                                            $activityIcon = 'fas fa-trash';
                                            $activityColor = 'text-danger';
                                        }
                                    @endphp
                                    <i class="{{ $activityIcon }} {{ $activityColor }}"></i>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    <div class="activity-footer">
                        <a href="{{ route('admin.activity-logs') }}" class="btn btn-link btn-sm w-100">
                            View All Activities <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                @else
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-history"></i>
                        </div>
                        <h6 class="empty-title">No Recent Activity</h6>
                        <p class="empty-text">Activity will appear here as users interact with the system</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Performance Metrics -->
<div class="row mb-4">
    <div class="col-xl-4 col-md-6">
        <div class="modern-widget performance-widget">
            <div class="widget-header">
                <h5 class="widget-title">System Performance</h5>
                <p class="widget-subtitle">Real-time metrics</p>
            </div>
            <div class="widget-body">
                <div class="performance-metrics">
                    <div class="metric-item">
                        <div class="metric-label">CPU Usage</div>
                        <div class="metric-value">
                            <div class="progress-circle" data-percentage="65">
                                <span class="percentage">65%</span>
                            </div>
                        </div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Memory</div>
                        <div class="metric-value">
                            <div class="progress-circle" data-percentage="42">
                                <span class="percentage">42%</span>
                            </div>
                        </div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Storage</div>
                        <div class="metric-value">
                            <div class="progress-circle" data-percentage="78">
                                <span class="percentage">78%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-md-6">
        <div class="modern-widget">
            <div class="widget-header">
                <h5 class="widget-title">Top Routes</h5>
                <p class="widget-subtitle">Most popular destinations</p>
            </div>
            <div class="widget-body">
                <div class="route-list">
                    <div class="route-item">
                        <div class="route-info">
                            <div class="route-cities">
                                <span class="departure">NYC</span>
                                <i class="fas fa-plane route-icon"></i>
                                <span class="arrival">LAX</span>
                            </div>
                            <div class="route-stats">
                                <span class="bookings">234 bookings</span>
                                <span class="revenue">$45,680</span>
                            </div>
                        </div>
                        <div class="route-trend">
                            <i class="fas fa-arrow-up text-success"></i>
                            <span>+12%</span>
                        </div>
                    </div>
                    <div class="route-item">
                        <div class="route-info">
                            <div class="route-cities">
                                <span class="departure">MIA</span>
                                <i class="fas fa-plane route-icon"></i>
                                <span class="arrival">JFK</span>
                            </div>
                            <div class="route-stats">
                                <span class="bookings">189 bookings</span>
                                <span class="revenue">$38,920</span>
                            </div>
                        </div>
                        <div class="route-trend">
                            <i class="fas fa-arrow-up text-success"></i>
                            <span>+8%</span>
                        </div>
                    </div>
                    <div class="route-item">
                        <div class="route-info">
                            <div class="route-cities">
                                <span class="departure">CHI</span>
                                <i class="fas fa-plane route-icon"></i>
                                <span class="arrival">SEA</span>
                            </div>
                            <div class="route-stats">
                                <span class="bookings">156 bookings</span>
                                <span class="revenue">$32,140</span>
                            </div>
                        </div>
                        <div class="route-trend">
                            <i class="fas fa-arrow-down text-danger"></i>
                            <span>-3%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-md-12">
        <div class="modern-widget">
            <div class="widget-header">
                <h5 class="widget-title">Quick Actions</h5>
                <p class="widget-subtitle">Common tasks</p>
            </div>
            <div class="widget-body">
                <div class="quick-actions-grid">
                    <a href="{{ route('admin.flights.create') }}" class="quick-action-item">
                        <div class="action-icon gradient-primary">
                            <i class="fas fa-plane"></i>
                        </div>
                        <div class="action-content">
                            <div class="action-title">Add Flight</div>
                            <div class="action-subtitle">Create new route</div>
                        </div>
                    </a>
                    <a href="{{ route('admin.users.create') }}" class="quick-action-item">
                        <div class="action-icon gradient-success">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="action-content">
                            <div class="action-title">New User</div>
                            <div class="action-subtitle">Add team member</div>
                        </div>
                    </a>
                    <a href="{{ route('admin.bookings.index') }}" class="quick-action-item">
                        <div class="action-icon gradient-info">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <div class="action-content">
                            <div class="action-title">Bookings</div>
                            <div class="action-subtitle">Manage reservations</div>
                        </div>
                    </a>
                    <a href="{{ route('admin.settings') }}" class="quick-action-item">
                        <div class="action-icon gradient-warning">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="action-content">
                            <div class="action-title">Settings</div>
                            <div class="action-subtitle">System config</div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Modern Chart Configuration
    Chart.defaults.font.family = "'Inter', sans-serif";
    Chart.defaults.color = '#64748b';
    Chart.defaults.borderColor = '#e2e8f0';
    Chart.defaults.backgroundColor = 'rgba(99, 102, 241, 0.1)';

    // Main Analytics Chart
    const mainCtx = document.getElementById('mainAnalyticsChart').getContext('2d');
    const mainChart = new Chart(mainCtx, {
        type: 'line',
        data: {
            labels: [
                @foreach($userRegistrations as $registration)
                    '{{ \Carbon\Carbon::parse($registration->date)->format('M d') }}',
                @endforeach
            ],
            datasets: [{
                label: 'User Registrations',
                data: [
                    @foreach($userRegistrations as $registration)
                        {{ $registration->count }},
                    @endforeach
                ],
                borderColor: '#6366f1',
                backgroundColor: 'rgba(99, 102, 241, 0.1)',
                borderWidth: 3,
                tension: 0.4,
                fill: true,
                pointBackgroundColor: '#6366f1',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8
            }, {
                label: 'Bookings',
                data: [12, 19, 15, 25, 22, 30, 28],
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                borderWidth: 3,
                tension: 0.4,
                fill: true,
                pointBackgroundColor: '#10b981',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top',
                    align: 'end',
                    labels: {
                        usePointStyle: true,
                        padding: 20,
                        font: {
                            size: 12,
                            weight: '500'
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#374151',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: true,
                    padding: 12
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    border: {
                        display: false
                    },
                    ticks: {
                        font: {
                            size: 11,
                            weight: '500'
                        }
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: '#f1f5f9'
                    },
                    border: {
                        display: false
                    },
                    ticks: {
                        stepSize: 5,
                        font: {
                            size: 11,
                            weight: '500'
                        }
                    }
                }
            }
        }
    });

    // Mini Charts for Stats Cards
    function createMiniChart(canvasId, data, color) {
        const ctx = document.getElementById(canvasId).getContext('2d');
        return new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['', '', '', '', '', '', ''],
                datasets: [{
                    data: data,
                    borderColor: color,
                    backgroundColor: 'transparent',
                    borderWidth: 2,
                    tension: 0.4,
                    pointRadius: 0,
                    pointHoverRadius: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: { enabled: false }
                },
                scales: {
                    x: { display: false },
                    y: { display: false }
                },
                elements: {
                    point: { radius: 0 }
                }
            }
        });
    }

    // Initialize mini charts
    createMiniChart('usersChart', [10, 15, 12, 18, 22, 25, 30], '#ffffff');
    createMiniChart('flightsChart', [5, 8, 12, 15, 18, 20, 25], '#ffffff');
    createMiniChart('bookingsChart', [20, 25, 30, 28, 35, 40, 45], '#ffffff');
    createMiniChart('revenueChart', [100, 120, 110, 140, 160, 180, 200], '#ffffff');

    // Performance Circles
    function initPerformanceCircles() {
        document.querySelectorAll('.progress-circle').forEach(circle => {
            const percentage = circle.dataset.percentage;
            const circumference = 2 * Math.PI * 20;
            const offset = circumference - (percentage / 100) * circumference;

            circle.style.background = `conic-gradient(#6366f1 ${percentage * 3.6}deg, #e5e7eb 0deg)`;
        });
    }

    // Dashboard Functions
    function refreshDashboard() {
        // Add loading state
        const refreshBtn = document.querySelector('[onclick="refreshDashboard()"]');
        const originalText = refreshBtn.innerHTML;
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Refreshing...';
        refreshBtn.disabled = true;

        // Simulate refresh
        setTimeout(() => {
            location.reload();
        }, 1000);
    }

    function changePeriod(period) {
        console.log('Changing period to:', period);
        // Here you would typically make an AJAX call to update the data
    }

    // Chart type toggle
    document.querySelectorAll('input[name="chartType"]').forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.id === 'barChart') {
                mainChart.config.type = 'bar';
            } else {
                mainChart.config.type = 'line';
            }
            mainChart.update();
        });
    });

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        initPerformanceCircles();

        // Animate stats numbers
        document.querySelectorAll('.stats-number-modern').forEach(element => {
            const finalValue = element.textContent.replace(/[^0-9]/g, '');
            if (finalValue) {
                animateNumber(element, 0, parseInt(finalValue), 2000);
            }
        });
    });

    function animateNumber(element, start, end, duration) {
        const range = end - start;
        const increment = range / (duration / 16);
        let current = start;

        const timer = setInterval(() => {
            current += increment;
            if (current >= end) {
                current = end;
                clearInterval(timer);
            }

            const formatted = element.textContent.includes('$')
                ? '$' + Math.floor(current).toLocaleString()
                : Math.floor(current).toLocaleString();
            element.textContent = formatted;
        }, 16);
    }
</script>
@endpush
