<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\FlightApiService;
use App\Models\Flight;
use App\Models\Airline;
use App\Models\Airport;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Log;
use Exception;

class FlightApiController extends Controller
{
    protected FlightApiService $flightApiService;

    public function __construct(FlightApiService $flightApiService)
    {
        $this->flightApiService = $flightApiService;
    }

    /**
     * Show the flight API management page
     */
    public function index(): View
    {
        $providers = $this->flightApiService->getAvailableProviders();
        $recentImports = Flight::where('source', '!=', 'manual')
            ->latest()
            ->take(10)
            ->get();

        return view('admin.flight-api.index', compact('providers', 'recentImports'));
    }

    /**
     * Search flights using external APIs
     */
    public function search(Request $request): JsonResponse
    {
        $request->validate([
            'departure_airport' => 'required|string|size:3',
            'arrival_airport' => 'required|string|size:3',
            'departure_date' => 'required|date|after_or_equal:today',
            'return_date' => 'nullable|date|after:departure_date',
            'adults' => 'integer|min:1|max:9',
            'children' => 'integer|min:0|max:9',
            'infants' => 'integer|min:0|max:9',
            'cabin_class' => 'in:economy,premium_economy,business,first',
            'currency' => 'string|size:3',
            'max_results' => 'integer|min:1|max:100',
        ]);

        try {
            $searchParams = [
                'departure_airport' => $request->departure_airport,
                'arrival_airport' => $request->arrival_airport,
                'departure_date' => $request->departure_date,
                'return_date' => $request->return_date,
                'adults' => $request->adults ?? 1,
                'children' => $request->children ?? 0,
                'infants' => $request->infants ?? 0,
                'cabin_class' => $request->cabin_class ?? 'economy',
                'currency' => $request->currency ?? 'USD',
                'max_results' => $request->max_results ?? 50,
            ];

            $flights = $this->flightApiService->searchFlights($searchParams);

            return response()->json([
                'success' => true,
                'data' => $flights,
                'count' => count($flights),
                'search_params' => $searchParams,
            ]);
        } catch (Exception $e) {
            Log::error('Flight API search failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Flight search failed: ' . $e->getMessage(),
                'data' => [],
            ], 500);
        }
    }

    /**
     * Import selected flights into the database
     */
    public function import(Request $request): JsonResponse
    {
        $request->validate([
            'flights' => 'required|array',
            'flights.*.id' => 'required|string',
            'flights.*.provider' => 'required|string',
        ]);

        try {
            $imported = 0;
            $errors = [];

            foreach ($request->flights as $flightData) {
                try {
                    $this->importSingleFlight($flightData);
                    $imported++;
                } catch (Exception $e) {
                    $errors[] = "Flight {$flightData['id']}: " . $e->getMessage();
                }
            }

            return response()->json([
                'success' => true,
                'imported' => $imported,
                'errors' => $errors,
                'message' => "Successfully imported {$imported} flights.",
            ]);
        } catch (Exception $e) {
            Log::error('Flight import failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Flight import failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Import a single flight into the database
     */
    protected function importSingleFlight(array $flightData): Flight
    {
        // Get or create airline
        $airline = Airline::firstOrCreate(
            ['iata_code' => $flightData['airline']],
            [
                'name' => $flightData['airline'] . ' Airlines',
                'icao_code' => $flightData['airline'],
                'country' => 'Unknown',
                'status' => 'Active',
            ]
        );

        // Get or create airports
        $departureAirport = Airport::firstOrCreate(
            ['code' => $flightData['departure_airport']],
            [
                'name' => $flightData['departure_airport'] . ' Airport',
                'city' => 'Unknown',
                'country' => 'Unknown',
                'timezone' => 'UTC',
                'status' => 'Active',
            ]
        );

        $arrivalAirport = Airport::firstOrCreate(
            ['code' => $flightData['arrival_airport']],
            [
                'name' => $flightData['arrival_airport'] . ' Airport',
                'city' => 'Unknown',
                'country' => 'Unknown',
                'timezone' => 'UTC',
                'status' => 'Active',
            ]
        );

        // Create or update flight
        return Flight::updateOrCreate(
            [
                'flight_number' => $flightData['flight_number'],
                'departure_date' => date('Y-m-d', strtotime($flightData['departure_time'])),
            ],
            [
                'airline_id' => $airline->id,
                'departure_airport_id' => $departureAirport->id,
                'arrival_airport_id' => $arrivalAirport->id,
                'departure_time' => $flightData['departure_time'],
                'arrival_time' => $flightData['arrival_time'],
                'duration' => $flightData['duration'],
                'aircraft_type' => 'Unknown',
                'total_seats' => $flightData['available_seats'] ?? 180,
                'available_seats' => $flightData['available_seats'] ?? 180,
                'base_price' => $flightData['price'],
                'currency' => $flightData['currency'],
                'flight_type' => $this->determineFlightType($departureAirport->country, $arrivalAirport->country),
                'status' => 'Scheduled',
                'source' => 'api',
                'api_provider' => $flightData['provider'],
                'api_flight_id' => $flightData['id'],
                'api_data' => json_encode($flightData['raw_data'] ?? []),
            ]
        );
    }

    /**
     * Determine flight type based on airports
     */
    protected function determineFlightType(string $departureCountry, string $arrivalCountry): string
    {
        return $departureCountry === $arrivalCountry ? 'Domestic' : 'International';
    }

    /**
     * Search airports using external APIs
     */
    public function searchAirports(Request $request): JsonResponse
    {
        $request->validate([
            'query' => 'required|string|min:2',
        ]);

        try {
            $airports = $this->flightApiService->getAirports($request->query);

            return response()->json([
                'success' => true,
                'data' => $airports,
            ]);
        } catch (Exception $e) {
            Log::error('Airport search failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Airport search failed: ' . $e->getMessage(),
                'data' => [],
            ], 500);
        }
    }

    /**
     * Get real-time flight prices
     */
    public function getPrices(Request $request): JsonResponse
    {
        $request->validate([
            'flight_ids' => 'required|array',
            'flight_ids.*' => 'string',
        ]);

        try {
            $prices = $this->flightApiService->getFlightPrices($request->flight_ids);

            return response()->json([
                'success' => true,
                'data' => $prices,
            ]);
        } catch (Exception $e) {
            Log::error('Price check failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Price check failed: ' . $e->getMessage(),
                'data' => [],
            ], 500);
        }
    }

    /**
     * Sync existing flights with API data
     */
    public function sync(Request $request): JsonResponse
    {
        try {
            $flights = Flight::where('source', 'api')
                ->whereNotNull('api_flight_id')
                ->get();

            $updated = 0;
            $errors = [];

            foreach ($flights as $flight) {
                try {
                    $apiData = $this->flightApiService->getFlightDetails(
                        $flight->api_flight_id,
                        $flight->api_provider
                    );

                    if ($apiData) {
                        $flight->update([
                            'base_price' => $apiData['price'],
                            'available_seats' => $apiData['available_seats'],
                            'status' => $this->mapApiStatus($apiData['status'] ?? 'scheduled'),
                            'api_data' => json_encode($apiData['raw_data'] ?? []),
                            'updated_at' => now(),
                        ]);
                        $updated++;
                    }
                } catch (Exception $e) {
                    $errors[] = "Flight {$flight->flight_number}: " . $e->getMessage();
                }
            }

            return response()->json([
                'success' => true,
                'updated' => $updated,
                'errors' => $errors,
                'message' => "Successfully updated {$updated} flights.",
            ]);
        } catch (Exception $e) {
            Log::error('Flight sync failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Flight sync failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Map API status to internal status
     */
    protected function mapApiStatus(string $apiStatus): string
    {
        return match (strtolower($apiStatus)) {
            'scheduled', 'active' => 'Scheduled',
            'delayed' => 'Delayed',
            'cancelled' => 'Cancelled',
            'completed', 'landed' => 'Completed',
            default => 'Scheduled',
        };
    }
}
