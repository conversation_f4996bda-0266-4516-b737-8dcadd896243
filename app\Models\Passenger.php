<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Passenger extends Model
{
    protected $fillable = [
        'booking_id',
        'title',
        'first_name',
        'last_name',
        'date_of_birth',
        'gender',
        'passport_number',
        'passport_expiry',
        'nationality',
        'seat_number',
        'passenger_type',
        'type',
        'special_needs',
        'special_assistance',
        'meal_preference',
        'is_frequent_flyer',
        'frequent_flyer_number',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'passport_expiry' => 'date',
        'is_frequent_flyer' => 'boolean',
    ];

    /**
     * Get the booking this passenger belongs to
     */
    public function booking(): BelongsTo
    {
        return $this->belongsTo(Booking::class);
    }

    /**
     * Get full name attribute
     */
    public function getFullNameAttribute(): string
    {
        return "{$this->title} {$this->first_name} {$this->last_name}";
    }

    /**
     * Get age from date of birth
     */
    public function getAgeAttribute(): int
    {
        return $this->date_of_birth->age;
    }

    /**
     * Check if passenger is adult
     */
    public function getIsAdultAttribute(): bool
    {
        return $this->passenger_type === 'Adult';
    }

    /**
     * Check if passenger has special needs
     */
    public function getHasSpecialNeedsAttribute(): bool
    {
        return !empty($this->special_needs);
    }

    /**
     * Get the passenger type display name
     */
    public function getTypeDisplayAttribute(): string
    {
        $type = $this->type ?? $this->passenger_type;
        return match(strtolower($type)) {
            'adult' => 'Adult',
            'child' => 'Child',
            'infant' => 'Infant',
            default => ucfirst($type)
        };
    }

    /**
     * Check if passenger is adult
     */
    public function isAdult(): bool
    {
        $type = $this->type ?? $this->passenger_type;
        return strtolower($type) === 'adult';
    }

    /**
     * Check if passenger is child
     */
    public function isChild(): bool
    {
        $type = $this->type ?? $this->passenger_type;
        return strtolower($type) === 'child';
    }

    /**
     * Check if passenger is infant
     */
    public function isInfant(): bool
    {
        $type = $this->type ?? $this->passenger_type;
        return strtolower($type) === 'infant';
    }

    /**
     * Scope for filtering by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where(function($q) use ($type) {
            $q->where('type', $type)->orWhere('passenger_type', ucfirst($type));
        });
    }

    /**
     * Scope for adults
     */
    public function scopeAdults($query)
    {
        return $query->byType('adult');
    }

    /**
     * Scope for children
     */
    public function scopeChildren($query)
    {
        return $query->byType('child');
    }

    /**
     * Scope for infants
     */
    public function scopeInfants($query)
    {
        return $query->byType('infant');
    }
}
