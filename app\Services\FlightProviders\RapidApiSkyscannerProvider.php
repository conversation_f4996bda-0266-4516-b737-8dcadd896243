<?php

namespace App\Services\FlightProviders;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class RapidApiSkyscannerProvider implements FlightProviderInterface
{
    protected array $config;

    public function __construct(array $config)
    {
        $this->config = $config;
    }

    public function isEnabled(): bool
    {
        return $this->config['enabled'] ?? false;
    }

    /**
     * Make request to RapidAPI Skyscanner
     */
    protected function makeRequest(string $endpoint, array $params = []): array
    {
        $response = Http::withHeaders([
            'X-RapidAPI-Key' => $this->config['api_key'],
            'X-RapidAPI-Host' => 'skyscanner80.p.rapidapi.com',
        ])->timeout($this->config['timeout'])->get($this->config['base_url'] . $endpoint, $params);

        if ($response->successful()) {
            return $response->json();
        }

        throw new Exception('RapidAPI Skyscanner request failed: ' . $response->body());
    }

    /**
     * Search for flights
     */
    public function searchFlights(array $searchParams): array
    {
        try {
            $params = [
                'fromEntityId' => $this->getEntityId($searchParams['departure_airport']),
                'toEntityId' => $this->getEntityId($searchParams['arrival_airport']),
                'departDate' => $searchParams['departure_date'],
                'adults' => $searchParams['adults'] ?? 1,
                'currency' => $searchParams['currency'] ?? 'USD',
                'market' => 'US',
                'locale' => 'en-US',
            ];

            if (!empty($searchParams['return_date'])) {
                $params['returnDate'] = $searchParams['return_date'];
            }

            if (!empty($searchParams['children'])) {
                $params['children'] = $searchParams['children'];
            }

            if (!empty($searchParams['cabin_class'])) {
                $params['cabinClass'] = $this->mapCabinClass($searchParams['cabin_class']);
            }

            $response = $this->makeRequest('/api/v1/flights/search', $params);
            
            return $this->transformFlightResults($response);
        } catch (Exception $e) {
            Log::error('RapidAPI Skyscanner flight search failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Transform Skyscanner flight results to standard format
     */
    protected function transformFlightResults(array $response): array
    {
        $flights = [];
        $itineraries = $response['data']['itineraries'] ?? [];
        $legs = $response['data']['legs'] ?? [];
        $segments = $response['data']['segments'] ?? [];
        $carriers = $response['data']['carriers'] ?? [];

        foreach ($itineraries as $itinerary) {
            $pricingOptions = $itinerary['pricingOptions'] ?? [];
            
            foreach ($pricingOptions as $pricing) {
                $legIds = $itinerary['legIds'] ?? [];
                $outboundLeg = $this->findLegById($legs, $legIds[0] ?? '');
                
                if (!$outboundLeg) continue;

                $segmentIds = $outboundLeg['segmentIds'] ?? [];
                $firstSegment = $this->findSegmentById($segments, $segmentIds[0] ?? '');
                $lastSegment = $this->findSegmentById($segments, end($segmentIds));

                if (!$firstSegment || !$lastSegment) continue;

                $carrier = $this->findCarrierById($carriers, $firstSegment['marketingCarrierId'] ?? '');

                $flights[] = [
                    'id' => $itinerary['id'] . '_' . $pricing['id'],
                    'airline' => $carrier['name'] ?? '',
                    'flight_number' => ($carrier['iata'] ?? '') . ($firstSegment['flightNumber'] ?? ''),
                    'departure_airport' => $outboundLeg['originPlaceId'] ?? '',
                    'arrival_airport' => $outboundLeg['destinationPlaceId'] ?? '',
                    'departure_time' => $outboundLeg['departureDateTime'] ?? '',
                    'arrival_time' => $outboundLeg['arrivalDateTime'] ?? '',
                    'duration' => $this->formatDuration($outboundLeg['durationInMinutes'] ?? 0),
                    'stops' => $outboundLeg['stopCount'] ?? 0,
                    'price' => (float) ($pricing['price']['amount'] ?? 0) / 1000, // Convert from cents
                    'currency' => $pricing['price']['unit'] ?? 'USD',
                    'cabin_class' => $this->mapCabinClassBack($firstSegment['cabinClass'] ?? ''),
                    'available_seats' => 9, // Skyscanner doesn't provide exact seat count
                    'baggage_info' => $this->extractBaggageInfo($pricing),
                    'cancellation_policy' => 'Please check with airline for cancellation policy',
                    'raw_data' => [
                        'itinerary' => $itinerary,
                        'pricing' => $pricing,
                        'leg' => $outboundLeg,
                    ],
                ];
            }
        }

        return $flights;
    }

    /**
     * Get flight details by ID
     */
    public function getFlightDetails(string $flightId): ?array
    {
        // Skyscanner doesn't have a direct flight details endpoint
        // This would require storing the original search results
        return null;
    }

    /**
     * Get real-time flight prices
     */
    public function getFlightPrices(array $flightIds): array
    {
        // Would require re-searching with original parameters
        return [];
    }

    /**
     * Book a flight
     */
    public function bookFlight(array $bookingData): array
    {
        throw new Exception('Direct booking not supported by Skyscanner API - redirects to airline/OTA');
    }

    /**
     * Search airports
     */
    public function searchAirports(string $query): array
    {
        try {
            $params = [
                'query' => $query,
                'locale' => 'en-US',
                'market' => 'US',
            ];

            $response = $this->makeRequest('/api/v1/flights/searchAirport', $params);
            
            $places = $response['data'] ?? [];
            
            return array_map(function ($place) {
                return [
                    'code' => $place['iata'] ?? $place['skyId'] ?? '',
                    'name' => $place['name'] ?? '',
                    'city' => $place['cityName'] ?? '',
                    'country' => $place['countryName'] ?? '',
                    'timezone' => '',
                ];
            }, $places);
        } catch (Exception $e) {
            Log::error('RapidAPI Skyscanner airport search failed: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get airline information
     */
    public function getAirlineInfo(string $airlineCode): ?array
    {
        // Not directly supported by this API
        return null;
    }

    /**
     * Check flight availability
     */
    public function checkAvailability(string $flightId): bool
    {
        return true; // Assume available if returned in search
    }

    /**
     * Get booking status
     */
    public function getBookingStatus(string $bookingReference): ?array
    {
        throw new Exception('Booking status not supported by Skyscanner API');
    }

    /**
     * Helper methods
     */
    protected function getEntityId(string $airportCode): string
    {
        // This would typically require a separate API call to get entity IDs
        // For now, return the airport code as-is
        return $airportCode;
    }

    protected function mapCabinClass(string $class): string
    {
        return match (strtolower($class)) {
            'economy' => 'economy',
            'premium_economy' => 'premium_economy',
            'business' => 'business',
            'first' => 'first',
            default => 'economy',
        };
    }

    protected function mapCabinClassBack(string $class): string
    {
        return strtolower($class);
    }

    protected function formatDuration(int $minutes): string
    {
        $hours = intval($minutes / 60);
        $mins = $minutes % 60;
        return sprintf('PT%dH%dM', $hours, $mins);
    }

    protected function findLegById(array $legs, string $id): ?array
    {
        foreach ($legs as $leg) {
            if ($leg['id'] === $id) {
                return $leg;
            }
        }
        return null;
    }

    protected function findSegmentById(array $segments, string $id): ?array
    {
        foreach ($segments as $segment) {
            if ($segment['id'] === $id) {
                return $segment;
            }
        }
        return null;
    }

    protected function findCarrierById(array $carriers, string $id): ?array
    {
        foreach ($carriers as $carrier) {
            if ($carrier['id'] === $id) {
                return $carrier;
            }
        }
        return null;
    }

    protected function extractBaggageInfo(array $pricing): array
    {
        return [
            'carry_on' => '1 piece',
            'checked' => 'Varies by airline',
        ];
    }
}
