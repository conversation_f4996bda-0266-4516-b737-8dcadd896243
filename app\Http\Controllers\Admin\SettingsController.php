<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;

class SettingsController extends Controller
{
    /**
     * Display the settings page.
     */
    public function index()
    {
        // Get current settings from cache or default values
        $settings = [
            'general' => [
                'site_name' => Cache::get('settings.site_name', config('app.name', 'FlyNow Airlines')),
                'site_description' => Cache::get('settings.site_description', 'Your trusted airline booking partner'),
                'contact_email' => Cache::get('settings.contact_email', '<EMAIL>'),
                'contact_phone' => Cache::get('settings.contact_phone', '+****************'),
                'timezone' => Cache::get('settings.timezone', 'UTC'),
                'date_format' => Cache::get('settings.date_format', 'Y-m-d'),
                'time_format' => Cache::get('settings.time_format', 'H:i'),
            ],
            'booking' => [
                'booking_window_days' => Cache::get('settings.booking_window_days', 365),
                'cancellation_window_hours' => Cache::get('settings.cancellation_window_hours', 24),
                'max_passengers_per_booking' => Cache::get('settings.max_passengers_per_booking', 9),
                'require_passport_info' => Cache::get('settings.require_passport_info', false),
                'auto_confirm_bookings' => Cache::get('settings.auto_confirm_bookings', true),
                'send_confirmation_email' => Cache::get('settings.send_confirmation_email', true),
            ],
            'email' => [
                'smtp_host' => Cache::get('settings.smtp_host', 'smtp.gmail.com'),
                'smtp_port' => Cache::get('settings.smtp_port', 587),
                'smtp_username' => Cache::get('settings.smtp_username', ''),
                'smtp_password' => Cache::get('settings.smtp_password', ''),
                'smtp_encryption' => Cache::get('settings.smtp_encryption', 'tls'),
                'from_email' => Cache::get('settings.from_email', '<EMAIL>'),
                'from_name' => Cache::get('settings.from_name', 'FlyNow Airlines'),
            ],
            'payment' => [
                'currency' => Cache::get('settings.currency', 'USD'),
                'payment_methods' => Cache::get('settings.payment_methods', ['credit_card', 'paypal']),
                'stripe_publishable_key' => Cache::get('settings.stripe_publishable_key', ''),
                'stripe_secret_key' => Cache::get('settings.stripe_secret_key', ''),
                'paypal_client_id' => Cache::get('settings.paypal_client_id', ''),
                'paypal_client_secret' => Cache::get('settings.paypal_client_secret', ''),
                'payment_timeout_minutes' => Cache::get('settings.payment_timeout_minutes', 15),
            ],
            'security' => [
                'enable_2fa' => Cache::get('settings.enable_2fa', false),
                'session_timeout_minutes' => Cache::get('settings.session_timeout_minutes', 120),
                'max_login_attempts' => Cache::get('settings.max_login_attempts', 5),
                'lockout_duration_minutes' => Cache::get('settings.lockout_duration_minutes', 15),
                'password_min_length' => Cache::get('settings.password_min_length', 8),
                'require_password_confirmation' => Cache::get('settings.require_password_confirmation', true),
            ],
            'maintenance' => [
                'maintenance_mode' => Cache::get('settings.maintenance_mode', false),
                'maintenance_message' => Cache::get('settings.maintenance_message', 'We are currently performing scheduled maintenance. Please check back soon.'),
                'backup_frequency' => Cache::get('settings.backup_frequency', 'daily'),
                'log_retention_days' => Cache::get('settings.log_retention_days', 30),
                'enable_debug_mode' => Cache::get('settings.enable_debug_mode', false),
            ],
        ];

        return view('admin.settings', compact('settings'));
    }

    /**
     * Update settings.
     */
    public function update(Request $request)
    {
        // Debug: Log the incoming request data
        \Log::info('Settings update request:', $request->all());

        $validator = Validator::make($request->all(), [
            // General settings validation
            'site_name' => 'required|string|max:255',
            'site_description' => 'required|string|max:500',
            'contact_email' => 'required|email|max:255',
            'contact_phone' => 'required|string|max:20',
            'timezone' => 'required|string|max:50',
            'date_format' => 'required|string|max:20',
            'time_format' => 'required|string|max:20',

            // Booking settings validation
            'booking_window_days' => 'required|integer|min:1|max:730',
            'cancellation_window_hours' => 'required|integer|min:1|max:168',
            'max_passengers_per_booking' => 'required|integer|min:1|max:20',
            'require_passport_info' => 'boolean',
            'auto_confirm_bookings' => 'boolean',
            'send_confirmation_email' => 'boolean',

            // Email settings validation
            'smtp_host' => 'required|string|max:255',
            'smtp_port' => 'required|integer|min:1|max:65535',
            'smtp_username' => 'nullable|string|max:255',
            'smtp_password' => 'nullable|string|max:255',
            'smtp_encryption' => 'required|in:tls,ssl,none',
            'from_email' => 'required|email|max:255',
            'from_name' => 'required|string|max:255',

            // Payment settings validation
            'currency' => 'required|string|size:3',
            'payment_methods' => 'required|array',
            'payment_methods.*' => 'in:credit_card,paypal,bank_transfer',
            'stripe_publishable_key' => 'nullable|string|max:255',
            'stripe_secret_key' => 'nullable|string|max:255',
            'paypal_client_id' => 'nullable|string|max:255',
            'paypal_client_secret' => 'nullable|string|max:255',
            'payment_timeout_minutes' => 'required|integer|min:5|max:60',

            // Security settings validation
            'enable_2fa' => 'boolean',
            'session_timeout_minutes' => 'required|integer|min:30|max:480',
            'max_login_attempts' => 'required|integer|min:3|max:10',
            'lockout_duration_minutes' => 'required|integer|min:5|max:60',
            'password_min_length' => 'required|integer|min:6|max:20',
            'require_password_confirmation' => 'boolean',

            // Maintenance settings validation
            'maintenance_mode' => 'boolean',
            'maintenance_message' => 'required|string|max:1000',
            'backup_frequency' => 'required|in:hourly,daily,weekly,monthly',
            'log_retention_days' => 'required|integer|min:7|max:365',
            'enable_debug_mode' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Save all settings to cache
            $settings = $request->all();

            foreach ($settings as $key => $value) {
                // Convert boolean strings to actual booleans
                if (in_array($value, ['true', 'false'])) {
                    $value = $value === 'true';
                }

                Cache::put("settings.{$key}", $value, now()->addYears(1));
            }

            // Log the settings update
            activity()
                ->causedBy(auth()->user())
                ->log('System settings updated');

            return response()->json([
                'success' => true,
                'message' => 'Settings saved successfully!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save settings: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reset settings to default values.
     */
    public function reset()
    {
        try {
            // Clear all settings from cache
            $settingKeys = [
                'site_name', 'site_description', 'contact_email', 'contact_phone', 'timezone', 'date_format', 'time_format',
                'booking_window_days', 'cancellation_window_hours', 'max_passengers_per_booking', 'require_passport_info',
                'auto_confirm_bookings', 'send_confirmation_email',
                'smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_encryption', 'from_email', 'from_name',
                'currency', 'payment_methods', 'stripe_publishable_key', 'stripe_secret_key', 'paypal_client_id',
                'paypal_client_secret', 'payment_timeout_minutes',
                'enable_2fa', 'session_timeout_minutes', 'max_login_attempts', 'lockout_duration_minutes',
                'password_min_length', 'require_password_confirmation',
                'maintenance_mode', 'maintenance_message', 'backup_frequency', 'log_retention_days', 'enable_debug_mode'
            ];

            foreach ($settingKeys as $key) {
                Cache::forget("settings.{$key}");
            }

            // Log the settings reset
            activity()
                ->causedBy(auth()->user())
                ->log('System settings reset to defaults');

            return response()->json([
                'success' => true,
                'message' => 'Settings reset to default values successfully!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reset settings: ' . $e->getMessage()
            ], 500);
        }
    }
}
