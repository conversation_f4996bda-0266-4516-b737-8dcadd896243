@extends('layouts.admin')

@section('title', 'Profile Settings')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-user me-2"></i>Profile Settings
            </h1>
            <p class="text-muted mb-0">Manage your account information and preferences</p>
        </div>
    </div>

    <div class="row">
        <!-- Profile Information -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user-edit me-2"></i>Profile Information
                    </h6>
                </div>
                <div class="card-body">
                    <form>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="firstName" class="form-label">First Name</label>
                                <input type="text" class="form-control" id="firstName" value="{{ explode(' ', Auth::user()->name)[0] ?? '' }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="lastName" class="form-label">Last Name</label>
                                <input type="text" class="form-control" id="lastName" value="{{ explode(' ', Auth::user()->name)[1] ?? '' }}">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="email" value="{{ Auth::user()->email }}" readonly>
                                <small class="form-text text-muted">Email cannot be changed from this interface</small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" placeholder="+****************">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="department" class="form-label">Department</label>
                                <select class="form-select" id="department">
                                    <option value="">Select Department</option>
                                    <option value="administration">Administration</option>
                                    <option value="operations">Operations</option>
                                    <option value="customer-service">Customer Service</option>
                                    <option value="finance">Finance</option>
                                    <option value="it">IT Support</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="jobTitle" class="form-label">Job Title</label>
                                <input type="text" class="form-control" id="jobTitle" placeholder="e.g., System Administrator">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="bio" class="form-label">Bio</label>
                            <textarea class="form-control" id="bio" rows="3" placeholder="Tell us about yourself..."></textarea>
                        </div>
                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-secondary me-2">Cancel</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Update Profile
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Change Password -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-lock me-2"></i>Change Password
                    </h6>
                </div>
                <div class="card-body">
                    <form>
                        <div class="mb-3">
                            <label for="currentPassword" class="form-label">Current Password</label>
                            <input type="password" class="form-control" id="currentPassword">
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="newPassword" class="form-label">New Password</label>
                                <input type="password" class="form-control" id="newPassword">
                                <div class="form-text">
                                    <small>Password must be at least 8 characters long</small>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="confirmPassword" class="form-label">Confirm New Password</label>
                                <input type="password" class="form-control" id="confirmPassword">
                            </div>
                        </div>
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-key me-1"></i>Change Password
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Profile Sidebar -->
        <div class="col-xl-4 col-lg-5">
            <!-- Profile Picture -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-camera me-2"></i>Profile Picture
                    </h6>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <img src="https://ui-avatars.com/api/?name={{ urlencode(Auth::user()->name) }}&size=150&background=4e73df&color=ffffff" 
                             class="rounded-circle" width="150" height="150" alt="Profile Picture">
                    </div>
                    <div class="mb-3">
                        <input type="file" class="form-control" id="profilePicture" accept="image/*">
                    </div>
                    <button type="button" class="btn btn-primary btn-sm">
                        <i class="fas fa-upload me-1"></i>Upload New Picture
                    </button>
                </div>
            </div>

            <!-- Account Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle me-2"></i>Account Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>User ID:</strong><br>
                        <code>{{ Auth::user()->id }}</code>
                    </div>
                    <div class="mb-3">
                        <strong>Member Since:</strong><br>
                        {{ Auth::user()->created_at->format('F j, Y') }}
                    </div>
                    <div class="mb-3">
                        <strong>Last Updated:</strong><br>
                        {{ Auth::user()->updated_at->format('F j, Y g:i A') }}
                    </div>
                    <div class="mb-3">
                        <strong>Email Verified:</strong><br>
                        @if(Auth::user()->email_verified_at)
                            <span class="badge bg-success">
                                <i class="fas fa-check me-1"></i>Verified
                            </span>
                            <br><small class="text-muted">{{ Auth::user()->email_verified_at->format('M j, Y') }}</small>
                        @else
                            <span class="badge bg-warning">
                                <i class="fas fa-exclamation-triangle me-1"></i>Not Verified
                            </span>
                        @endif
                    </div>
                    <div class="mb-3">
                        <strong>Current Role:</strong><br>
                        @if(Auth::user()->hasRole('Admin'))
                            <span class="badge bg-danger">
                                <i class="fas fa-crown me-1"></i>Administrator
                            </span>
                        @elseif(Auth::user()->hasRole('Manager'))
                            <span class="badge bg-primary">
                                <i class="fas fa-user-tie me-1"></i>Manager
                            </span>
                        @elseif(Auth::user()->hasRole('Staff'))
                            <span class="badge bg-info">
                                <i class="fas fa-user me-1"></i>Staff
                            </span>
                        @else
                            <span class="badge bg-secondary">
                                <i class="fas fa-eye me-1"></i>Viewer
                            </span>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-download me-1"></i>Download My Data
                        </button>
                        <button class="btn btn-outline-info btn-sm">
                            <i class="fas fa-history me-1"></i>View My Activity
                        </button>
                        <button class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-shield-alt me-1"></i>Security Settings
                        </button>
                        <button class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-cog me-1"></i>Preferences
                        </button>
                    </div>
                </div>
            </div>

            <!-- Two-Factor Authentication -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-shield-alt me-2"></i>Two-Factor Authentication
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enable2FA">
                            <label class="form-check-label" for="enable2FA">
                                Enable 2FA
                            </label>
                        </div>
                        <small class="form-text text-muted">
                            Add an extra layer of security to your account
                        </small>
                    </div>
                    <button type="button" class="btn btn-warning btn-sm" disabled>
                        <i class="fas fa-qrcode me-1"></i>Setup 2FA
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
// Profile picture preview
document.getElementById('profilePicture').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.querySelector('.rounded-circle').src = e.target.result;
        };
        reader.readAsDataURL(file);
    }
});

// Password strength indicator
document.getElementById('newPassword').addEventListener('input', function(e) {
    const password = e.target.value;
    const strength = calculatePasswordStrength(password);
    // Add visual feedback for password strength
});

function calculatePasswordStrength(password) {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    return strength;
}

// 2FA toggle
document.getElementById('enable2FA').addEventListener('change', function(e) {
    const setupButton = document.querySelector('button[disabled]');
    setupButton.disabled = !e.target.checked;
});
</script>
@endpush
