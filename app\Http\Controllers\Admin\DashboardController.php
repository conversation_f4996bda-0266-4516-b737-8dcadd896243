<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Spatie\Activitylog\Models\Activity;

class DashboardController extends Controller
{
    public function index()
    {
        // Simple dashboard with basic stats
        $stats = [
            'total_users' => User::count(),
            'active_users' => User::where('email_verified_at', '!=', null)->count(),
            'new_users_today' => User::whereDate('created_at', today())->count(),
            'total_activities' => 0,
            'total_flights' => 0,
            'total_bookings' => 0,
            'total_revenue' => 0,
            'active_flights' => 0,
            'pending_bookings' => 0,
            'monthly_revenue' => 0,
        ];

        // Empty collections for now
        $recentActivities = collect();

        // Simple user registration data
        $userRegistrations = collect();
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $userRegistrations->push((object)[
                'date' => $date,
                'count' => 0
            ]);
        }

        return view('admin.dashboard', compact('stats', 'recentActivities', 'userRegistrations'));
    }

}
