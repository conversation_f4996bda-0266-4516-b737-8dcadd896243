<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Flight;
use App\Models\Booking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Spatie\Activitylog\Models\Activity;

class DashboardController extends Controller
{
    public function index()
    {
        $user = auth()->user();

        // Check if user has Manager role
        if ($user->hasRole('Manager')) {
            return $this->managerDashboard();
        }

        // Default admin dashboard
        return $this->adminDashboard();
    }

    private function adminDashboard()
    {
        // Admin dashboard with basic stats
        $stats = [
            'total_users' => User::count(),
            'active_users' => User::where('email_verified_at', '!=', null)->count(),
            'new_users_today' => User::whereDate('created_at', today())->count(),
            'total_activities' => 0,
            'total_flights' => Flight::count(),
            'total_bookings' => Booking::count(),
            'total_revenue' => Booking::where('payment_status', 'Paid')->sum('total_amount'),
            'active_flights' => Flight::where('status', 'Scheduled')->count(),
            'pending_bookings' => Booking::where('booking_status', 'Pending')->count(),
            'monthly_revenue' => Booking::where('payment_status', 'Paid')
                                     ->whereMonth('created_at', now()->month)
                                     ->sum('total_amount'),
        ];

        // Empty collections for now
        $recentActivities = collect();

        // Simple user registration data
        $userRegistrations = collect();
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $userRegistrations->push((object)[
                'date' => $date,
                'count' => User::whereDate('created_at', $date)->count()
            ]);
        }

        return view('admin.dashboard', compact('stats', 'recentActivities', 'userRegistrations'));
    }

    private function managerDashboard()
    {
        // Manager-specific statistics
        $stats = [
            'total_bookings' => Booking::count(),
            'bookings_today' => Booking::whereDate('created_at', today())->count(),
            'pending_bookings' => Booking::where('booking_status', 'Pending')->count(),
            'confirmed_bookings' => Booking::where('booking_status', 'Confirmed')->count(),
            'total_revenue' => Booking::where('payment_status', 'Paid')->sum('total_amount'),
            'monthly_revenue' => Booking::where('payment_status', 'Paid')
                                      ->whereMonth('created_at', now()->month)
                                      ->sum('total_amount'),
            'total_flights' => Flight::count(),
            'active_flights' => Flight::where('status', 'Scheduled')->count(),
            'total_passengers' => Booking::sum('total_passengers'),
            'avg_booking_value' => Booking::where('payment_status', 'Paid')->avg('total_amount'),
        ];

        // Get flights with relationships for the datatable
        $flights = Flight::with(['airline', 'departureAirport', 'arrivalAirport', 'bookings'])
                        ->orderBy('departure_time', 'desc')
                        ->paginate(20);

        // Get bookings for additional context
        $recentBookings = Booking::with(['flight', 'user'])
                                ->orderBy('created_at', 'desc')
                                ->limit(10)
                                ->get();

        return view('admin.manager-dashboard', compact('stats', 'flights', 'recentBookings'));
    }

}
