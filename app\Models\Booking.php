<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Booking extends Model
{
    protected $fillable = [
        'booking_reference',
        'user_id',
        'flight_id',
        'pnr_id',
        'departure_airport',
        'arrival_airport',
        'departure_date',
        'departure_time',
        'arrival_time',
        'airline_code',
        'flight_number',
        'passenger_count',
        'total_passengers',
        'total_amount',
        'currency',
        'booking_status',
        'status',
        'payment_status',
        'payment_method',
        'payment_reference',
        'booking_date',
        'contact_email',
        'contact_phone',
        'special_requests',
        'notes',
        'is_checked_in',
        'check_in_time',
    ];

    protected $casts = [
        'total_amount' => 'decimal:2',
        'booking_date' => 'datetime',
        'departure_date' => 'date',
        'check_in_time' => 'datetime',
        'is_checked_in' => 'boolean',
        'passenger_count' => 'integer',
        'total_passengers' => 'integer',
    ];

    /**
     * Boot method to generate booking reference
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($booking) {
            if (!$booking->booking_reference) {
                $booking->booking_reference = static::generateBookingReference();
            }
            if (!$booking->booking_date) {
                $booking->booking_date = now();
            }
        });
    }

    /**
     * Generate unique booking reference
     */
    public static function generateBookingReference(): string
    {
        do {
            $reference = strtoupper(Str::random(6));
        } while (static::where('booking_reference', $reference)->exists());

        return $reference;
    }

    /**
     * Get the user who made this booking
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the flight for this booking
     */
    public function flight(): BelongsTo
    {
        return $this->belongsTo(Flight::class);
    }

    /**
     * Get the PNR for this booking
     */
    public function pnr(): BelongsTo
    {
        return $this->belongsTo(Pnr::class);
    }

    /**
     * Get all passengers for this booking
     */
    public function passengers(): HasMany
    {
        return $this->hasMany(Passenger::class);
    }

    /**
     * Check if booking can be cancelled
     */
    public function getCanBeCancelledAttribute(): bool
    {
        return $this->booking_status === 'Confirmed' &&
               $this->flight->departure_time > now()->addHours(24);
    }

    /**
     * Check if check-in is available
     */
    public function getCanCheckInAttribute(): bool
    {
        return $this->booking_status === 'Confirmed' &&
               !$this->is_checked_in &&
               $this->flight->departure_time > now() &&
               $this->flight->departure_time <= now()->addHours(24);
    }

    /**
     * Scope for filtering by status
     */
    public function scopeByStatus($query, $status)
    {
        if ($status) {
            return $query->where('booking_status', $status);
        }
        return $query;
    }

    /**
     * Scope for searching bookings
     */
    public function scopeSearch($query, $search)
    {
        if ($search) {
            return $query->where(function ($q) use ($search) {
                $q->where('booking_reference', 'like', "%{$search}%")
                  ->orWhere('contact_email', 'like', "%{$search}%")
                  ->orWhere('flight_number', 'like', "%{$search}%")
                  ->orWhereHas('passengers', function ($pq) use ($search) {
                      $pq->where('first_name', 'like', "%{$search}%")
                        ->orWhere('last_name', 'like', "%{$search}%");
                  });
            });
        }
        return $query;
    }

    /**
     * Get the status badge color
     */
    public function getStatusBadgeAttribute()
    {
        $status = $this->status ?? $this->booking_status;
        return match(strtolower($status)) {
            'pending' => 'warning',
            'confirmed' => 'success',
            'cancelled' => 'danger',
            'completed' => 'info',
            default => 'secondary'
        };
    }

    /**
     * Get passenger count by type
     */
    public function getPassengerCountByType($type)
    {
        return $this->passengers()->where('type', $type)->count();
    }

    /**
     * Get adult passengers count
     */
    public function getAdultCountAttribute()
    {
        return $this->getPassengerCountByType('adult');
    }

    /**
     * Get child passengers count
     */
    public function getChildCountAttribute()
    {
        return $this->getPassengerCountByType('child');
    }

    /**
     * Get infant passengers count
     */
    public function getInfantCountAttribute()
    {
        return $this->getPassengerCountByType('infant');
    }

    /**
     * Check if booking is active
     */
    public function isActive()
    {
        $status = $this->status ?? $this->booking_status;
        return in_array(strtolower($status), ['pending', 'confirmed']);
    }

    /**
     * Scope for active bookings
     */
    public function scopeActive($query)
    {
        return $query->where(function($q) {
            $q->whereIn('status', ['pending', 'confirmed'])
              ->orWhereIn('booking_status', ['Pending', 'Confirmed']);
        });
    }
}
