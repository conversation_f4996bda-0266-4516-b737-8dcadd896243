<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Pnr;
use App\Models\Flight;
use App\Models\SeatLock;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class PnrController extends Controller
{
    /**
     * Display a listing of PNRs
     */
    public function index(Request $request): View
    {
        $query = Pnr::with(['flight.airline', 'flight.departureAirport', 'flight.arrivalAirport']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('flight_id')) {
            $query->where('flight_id', $request->flight_id);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('pnr_code', 'like', "%{$search}%")
                  ->orWhereHas('flight', function($fq) use ($search) {
                      $fq->where('flight_number', 'like', "%{$search}%");
                  });
            });
        }

        $pnrs = $query->orderBy('created_at', 'desc')->paginate(20);
        $flights = Flight::with(['airline', 'departureAirport', 'arrivalAirport'])
                        ->orderBy('departure_time', 'desc')
                        ->get();

        // Statistics
        $stats = [
            'total_pnrs' => Pnr::count(),
            'available_pnrs' => Pnr::available()->count(),
            'sold_out_pnrs' => Pnr::soldOut()->count(),
            'inventory_only_pnrs' => Pnr::inventoryOnly()->count(),
            'total_inventory_seats' => Pnr::sum('total_inventory_seats'),
            'total_live_seats' => Pnr::sum('live_seats'),
            'total_booked_seats' => Pnr::sum('booked_seats'),
        ];

        return view('admin.pnr.index', compact('pnrs', 'flights', 'stats'));
    }

    /**
     * Show the form for creating a new PNR
     */
    public function create(): View
    {
        $flights = Flight::with(['airline', 'departureAirport', 'arrivalAirport'])
                        ->where('departure_time', '>', now())
                        ->orderBy('departure_time')
                        ->get();

        return view('admin.pnr.create', compact('flights'));
    }

    /**
     * Store a newly created PNR
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'flight_id' => 'required|exists:flights,id',
            'total_inventory_seats' => 'required|integer|min:1|max:500',
            'live_seats' => 'required|integer|min:0',
            'base_price' => 'required|numeric|min:0',
            'currency' => 'required|string|size:3',
            'notes' => 'nullable|string|max:1000',
        ]);

        // Validate that live_seats doesn't exceed total_inventory_seats
        if ($request->live_seats > $request->total_inventory_seats) {
            return back()->withErrors(['live_seats' => 'Live seats cannot exceed total inventory seats.']);
        }

        $pnr = Pnr::create([
            'flight_id' => $request->flight_id,
            'total_inventory_seats' => $request->total_inventory_seats,
            'live_seats' => $request->live_seats,
            'base_price' => $request->base_price,
            'currency' => $request->currency,
            'notes' => $request->notes,
            'is_active' => true,
        ]);

        // Set live date if seats are made live
        if ($request->live_seats > 0) {
            $pnr->live_date = now();
            $pnr->save();
        }

        return redirect()->route('admin.pnr.index')
                        ->with('success', "PNR {$pnr->pnr_code} created successfully!");
    }

    /**
     * Display the specified PNR
     */
    public function show(Pnr $pnr): View
    {
        $pnr->load([
            'flight.airline',
            'flight.departureAirport',
            'flight.arrivalAirport',
            'bookings.passengers',
            'seatLocks' => function($query) {
                $query->orderBy('created_at', 'desc');
            }
        ]);

        return view('admin.pnr.show', compact('pnr'));
    }

    /**
     * Show the form for editing the specified PNR
     */
    public function edit(Pnr $pnr): View
    {
        $flights = Flight::with(['airline', 'departureAirport', 'arrivalAirport'])
                        ->orderBy('departure_time', 'desc')
                        ->get();

        return view('admin.pnr.edit', compact('pnr', 'flights'));
    }

    /**
     * Update the specified PNR
     */
    public function update(Request $request, Pnr $pnr): RedirectResponse
    {
        $request->validate([
            'flight_id' => 'required|exists:flights,id',
            'total_inventory_seats' => 'required|integer|min:1|max:500',
            'live_seats' => 'required|integer|min:0',
            'base_price' => 'required|numeric|min:0',
            'currency' => 'required|string|size:3',
            'notes' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
        ]);

        // Validate that live_seats doesn't exceed total_inventory_seats
        if ($request->live_seats > $request->total_inventory_seats) {
            return back()->withErrors(['live_seats' => 'Live seats cannot exceed total inventory seats.']);
        }

        // Validate that live_seats doesn't go below booked_seats
        if ($request->live_seats < $pnr->booked_seats) {
            return back()->withErrors(['live_seats' => 'Live seats cannot be less than already booked seats.']);
        }

        $wasInventoryOnly = $pnr->status === 'inventory_only';

        $pnr->update([
            'flight_id' => $request->flight_id,
            'total_inventory_seats' => $request->total_inventory_seats,
            'live_seats' => $request->live_seats,
            'base_price' => $request->base_price,
            'currency' => $request->currency,
            'notes' => $request->notes,
            'is_active' => $request->boolean('is_active', true),
        ]);

        // Set live date if seats are made live for the first time
        if ($wasInventoryOnly && $request->live_seats > 0 && !$pnr->live_date) {
            $pnr->live_date = now();
            $pnr->save();
        }

        return redirect()->route('admin.pnr.index')
                        ->with('success', "PNR {$pnr->pnr_code} updated successfully!");
    }

    /**
     * Remove the specified PNR
     */
    public function destroy(Pnr $pnr): RedirectResponse
    {
        if ($pnr->bookings()->count() > 0) {
            return back()->withErrors(['error' => 'Cannot delete PNR with existing bookings.']);
        }

        $pnrCode = $pnr->pnr_code;
        $pnr->delete();

        return redirect()->route('admin.pnr.index')
                        ->with('success', "PNR {$pnrCode} deleted successfully!");
    }

    /**
     * Make seats live via AJAX
     */
    public function makeSeatsLive(Request $request, Pnr $pnr): JsonResponse
    {
        $request->validate([
            'seats' => 'required|integer|min:0',
        ]);

        $seats = $request->seats;

        if ($seats > $pnr->total_inventory_seats) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot make more seats live than total inventory.'
            ], 400);
        }

        if ($seats < $pnr->booked_seats) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot make fewer seats live than already booked.'
            ], 400);
        }

        $success = $pnr->makeSeatsLive($seats);

        if ($success) {
            return response()->json([
                'success' => true,
                'message' => "Successfully made {$seats} seats live for PNR {$pnr->pnr_code}",
                'pnr' => [
                    'live_seats' => $pnr->live_seats,
                    'available_seats' => $pnr->available_seats,
                    'status' => $pnr->status,
                    'status_display' => $pnr->status_display,
                    'status_color' => $pnr->status_color,
                ]
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to make seats live.'
        ], 500);
    }

    /**
     * Get real-time seat availability
     */
    public function getSeatAvailability(Pnr $pnr): JsonResponse
    {
        // Clean up expired locks first
        SeatLock::cleanupExpired();

        return response()->json([
            'pnr_code' => $pnr->pnr_code,
            'total_inventory_seats' => $pnr->total_inventory_seats,
            'live_seats' => $pnr->live_seats,
            'booked_seats' => $pnr->booked_seats,
            'locked_seats' => $pnr->locked_seats,
            'available_seats' => $pnr->available_seats,
            'status' => $pnr->status,
            'status_display' => $pnr->status_display,
            'status_color' => $pnr->status_color,
            'is_available' => $pnr->isAvailable(),
        ]);
    }

    /**
     * Lock seats for booking
     */
    public function lockSeats(Request $request, Pnr $pnr): JsonResponse
    {
        $request->validate([
            'seats' => 'required|integer|min:1|max:9',
        ]);

        $seats = $request->seats;
        $sessionId = session()->getId();
        $userIp = $request->ip();

        // Clean up expired locks first
        SeatLock::cleanupExpired();

        $lock = $pnr->lockSeats($seats, $sessionId, $userIp);

        if ($lock) {
            return response()->json([
                'success' => true,
                'message' => "Successfully locked {$seats} seats for 55 seconds",
                'lock' => [
                    'id' => $lock->id,
                    'seats_locked' => $lock->seats_locked,
                    'expires_at' => $lock->expires_at->toISOString(),
                    'remaining_time' => $lock->remaining_time,
                ],
                'pnr' => [
                    'available_seats' => $pnr->fresh()->available_seats,
                ]
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Not enough seats available or seats already locked.',
            'available_seats' => $pnr->available_seats,
        ], 400);
    }

    /**
     * Release seat lock
     */
    public function releaseLock(Request $request, Pnr $pnr): JsonResponse
    {
        $sessionId = session()->getId();

        $released = $pnr->seatLocks()
                       ->where('session_id', $sessionId)
                       ->where('status', 'active')
                       ->update(['status' => 'cancelled']);

        return response()->json([
            'success' => true,
            'message' => 'Seat lock released successfully',
            'released_locks' => $released,
            'available_seats' => $pnr->fresh()->available_seats,
        ]);
    }
}
