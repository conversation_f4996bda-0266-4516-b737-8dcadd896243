@extends('layouts.app')

@section('title', 'Booking Details')

@section('content')
<div class="container">
    <!-- Booking Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-1">
                                <i class="fas fa-ticket-alt text-primary me-2"></i>
                                Booking Confirmation
                            </h4>
                            <p class="text-muted mb-0">
                                Booking Reference: <strong class="text-primary">{{ $booking->booking_reference }}</strong>
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge bg-{{ $booking->status_badge }} fs-6 px-3 py-2">
                                {{ $booking->status_display ?? ucfirst($booking->status ?? $booking->booking_status) }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Flight Information -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="fas fa-plane me-2"></i>Flight Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="flight-info-card p-3 border rounded">
                                <div class="row align-items-center">
                                    <div class="col-md-2 text-center">
                                        <img src="https://via.placeholder.com/50x35" alt="Airline" class="mb-2">
                                        <div class="small text-muted">{{ $booking->airline_code ?? 'Fly Jinnah' }}</div>
                                        <div class="small fw-bold">{{ $booking->flight_number ?? 'PK 764' }}</div>
                                    </div>
                                    <div class="col-md-10">
                                        <div class="row align-items-center">
                                            <div class="col-4 text-center">
                                                <div class="h5 mb-0">{{ \Carbon\Carbon::parse($booking->departure_time ?? '15:35')->format('H:i') }}</div>
                                                <div class="text-muted">{{ $booking->departure_airport ?? 'ISB-BAH' }}</div>
                                                <div class="small text-muted">{{ \Carbon\Carbon::parse($booking->departure_date ?? now())->format('M d, Y') }}</div>
                                            </div>
                                            <div class="col-4 text-center">
                                                <div class="text-muted small">2h 30m</div>
                                                <div class="position-relative">
                                                    <hr class="my-1">
                                                    <i class="fas fa-plane position-absolute top-50 start-50 translate-middle bg-white px-2 text-primary"></i>
                                                </div>
                                                <div class="text-muted small">Baggage: 20+7 KG</div>
                                            </div>
                                            <div class="col-4 text-center">
                                                <div class="h5 mb-0">{{ \Carbon\Carbon::parse($booking->arrival_time ?? '18:05')->format('H:i') }}</div>
                                                <div class="text-muted">{{ $booking->arrival_airport ?? 'BAH' }}</div>
                                                <div class="small text-muted">{{ \Carbon\Carbon::parse($booking->departure_date ?? now())->format('M d, Y') }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Passenger Details -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-users me-2"></i>Passengers</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-borderless">
                            <thead class="table-light">
                                <tr>
                                    <th>Passenger</th>
                                    <th>Type</th>
                                    <th>Age/Price</th>
                                    <th>Seat</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($booking->passengers as $index => $passenger)
                                <tr>
                                    <td>
                                        <div class="fw-bold">{{ $passenger->full_name }}</div>
                                        <div class="small text-muted">{{ $passenger->nationality ?? 'N/A' }}</div>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">{{ $passenger->type_display }}</span>
                                    </td>
                                    <td>
                                        <div>{{ $passenger->age ?? 'N/A' }} years</div>
                                        <div class="small text-muted">PKR {{ number_format($booking->total_amount / $booking->total_passengers) }}</div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ $passenger->seat_number ?? 'Not Assigned' }}</span>
                                    </td>
                                    <td>
                                        <div class="fw-bold">PKR {{ number_format($booking->total_amount / $booking->total_passengers) }}</div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="5" class="text-center text-muted">No passengers found</td>
                                </tr>
                                @endforelse
                                <tr class="table-light">
                                    <td colspan="4" class="fw-bold">Total</td>
                                    <td class="fw-bold text-primary">PKR {{ number_format($booking->total_amount) }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Booking Summary -->
        <div class="col-lg-4">
            <!-- Trip Details -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Trip Details</h6>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-6">
                            <div class="small text-muted">Date</div>
                            <div class="fw-bold">{{ \Carbon\Carbon::parse($booking->departure_date ?? now())->format('M d, Y') }}</div>
                        </div>
                        <div class="col-6">
                            <div class="small text-muted">Meal</div>
                            <div class="fw-bold">No</div>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-6">
                            <div class="small text-muted">Adult Price</div>
                            <div class="fw-bold">PKR {{ number_format($booking->total_amount / $booking->total_passengers) }}</div>
                        </div>
                        <div class="col-6">
                            <div class="small text-muted">Child</div>
                            <div class="fw-bold">N/A</div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="small text-muted">Infant</div>
                            <div class="fw-bold">Price On Call</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-address-book me-2"></i>Contact Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="small text-muted">Email</div>
                        <div class="fw-bold">{{ $booking->contact_email }}</div>
                    </div>
                    <div class="mb-3">
                        <div class="small text-muted">Phone</div>
                        <div class="fw-bold">{{ $booking->contact_phone }}</div>
                    </div>
                    <div>
                        <div class="small text-muted">Booking Date</div>
                        <div class="fw-bold">{{ $booking->booking_date->format('M d, Y H:i') }}</div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    @if($booking->canBeCancelled())
                    <button class="btn btn-outline-danger w-100 mb-2" onclick="cancelBooking()">
                        <i class="fas fa-times me-2"></i>Cancel Booking
                    </button>
                    @endif
                    
                    <button class="btn btn-outline-primary w-100 mb-2" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>Print Ticket
                    </button>
                    
                    <button class="btn btn-outline-info w-100" onclick="downloadTicket()">
                        <i class="fas fa-download me-2"></i>Download PDF
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function cancelBooking() {
    if (confirm('Are you sure you want to cancel this booking? This action cannot be undone.')) {
        fetch(`/booking/{{ $booking->booking_reference }}/cancel`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Failed to cancel booking. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred. Please try again.');
        });
    }
}

function downloadTicket() {
    window.open(`/booking/{{ $booking->booking_reference }}/pdf`, '_blank');
}
</script>
@endpush
@endsection
