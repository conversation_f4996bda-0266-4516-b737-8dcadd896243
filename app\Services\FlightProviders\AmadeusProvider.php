<?php

namespace App\Services\FlightProviders;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Exception;

class AmadeusProvider implements FlightProviderInterface
{
    protected array $config;
    protected ?string $accessToken = null;

    public function __construct(array $config)
    {
        $this->config = $config;
    }

    public function isEnabled(): bool
    {
        return $this->config['enabled'] ?? false;
    }

    /**
     * Get access token for Amadeus API
     */
    protected function getAccessToken(): string
    {
        if ($this->accessToken) {
            return $this->accessToken;
        }

        $cacheKey = 'amadeus_access_token';
        
        if (Cache::has($cacheKey)) {
            $this->accessToken = Cache::get($cacheKey);
            return $this->accessToken;
        }

        try {
            $response = Http::asForm()->post($this->config['base_url'] . '/v1/security/oauth2/token', [
                'grant_type' => 'client_credentials',
                'client_id' => $this->config['client_id'],
                'client_secret' => $this->config['client_secret'],
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $this->accessToken = $data['access_token'];
                
                // Cache token for slightly less than its expiry time
                $expiresIn = ($data['expires_in'] ?? 1799) - 60;
                Cache::put($cacheKey, $this->accessToken, $expiresIn);
                
                return $this->accessToken;
            }

            throw new Exception('Failed to get Amadeus access token: ' . $response->body());
        } catch (Exception $e) {
            Log::error('Amadeus authentication failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Make authenticated request to Amadeus API
     */
    protected function makeRequest(string $endpoint, array $params = []): array
    {
        $token = $this->getAccessToken();
        
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $token,
            'Content-Type' => 'application/json',
        ])->timeout($this->config['timeout'])->get($this->config['base_url'] . $endpoint, $params);

        if ($response->successful()) {
            return $response->json();
        }

        throw new Exception('Amadeus API request failed: ' . $response->body());
    }

    /**
     * Search for flights
     */
    public function searchFlights(array $searchParams): array
    {
        try {
            $params = [
                'originLocationCode' => $searchParams['departure_airport'],
                'destinationLocationCode' => $searchParams['arrival_airport'],
                'departureDate' => $searchParams['departure_date'],
                'adults' => $searchParams['adults'] ?? 1,
                'max' => $searchParams['max_results'] ?? 50,
                'currencyCode' => $searchParams['currency'] ?? 'USD',
            ];

            if (!empty($searchParams['return_date'])) {
                $params['returnDate'] = $searchParams['return_date'];
            }

            if (!empty($searchParams['children'])) {
                $params['children'] = $searchParams['children'];
            }

            if (!empty($searchParams['infants'])) {
                $params['infants'] = $searchParams['infants'];
            }

            if (!empty($searchParams['cabin_class'])) {
                $params['travelClass'] = strtoupper($searchParams['cabin_class']);
            }

            $response = $this->makeRequest('/v2/shopping/flight-offers', $params);
            
            return $this->transformFlightOffers($response['data'] ?? []);
        } catch (Exception $e) {
            Log::error('Amadeus flight search failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Transform Amadeus flight offers to standard format
     */
    protected function transformFlightOffers(array $offers): array
    {
        $flights = [];

        foreach ($offers as $offer) {
            foreach ($offer['itineraries'] as $itinerary) {
                $segments = $itinerary['segments'];
                $firstSegment = $segments[0];
                $lastSegment = end($segments);

                $flights[] = [
                    'id' => $offer['id'],
                    'airline' => $firstSegment['carrierCode'],
                    'flight_number' => $firstSegment['carrierCode'] . $firstSegment['number'],
                    'departure_airport' => $firstSegment['departure']['iataCode'],
                    'arrival_airport' => $lastSegment['arrival']['iataCode'],
                    'departure_time' => $firstSegment['departure']['at'],
                    'arrival_time' => $lastSegment['arrival']['at'],
                    'duration' => $itinerary['duration'],
                    'stops' => count($segments) - 1,
                    'price' => (float) $offer['price']['total'],
                    'currency' => $offer['price']['currency'],
                    'cabin_class' => $firstSegment['cabin'] ?? 'ECONOMY',
                    'available_seats' => $firstSegment['numberOfBookableSeats'] ?? 0,
                    'baggage_info' => $this->extractBaggageInfo($offer),
                    'cancellation_policy' => $this->extractCancellationPolicy($offer),
                    'raw_data' => $offer,
                ];
            }
        }

        return $flights;
    }

    /**
     * Get flight details by ID
     */
    public function getFlightDetails(string $flightId): ?array
    {
        try {
            $response = $this->makeRequest("/v1/shopping/flight-offers/{$flightId}");
            
            if (empty($response['data'])) {
                return null;
            }

            $offers = $this->transformFlightOffers([$response['data']]);
            return $offers[0] ?? null;
        } catch (Exception $e) {
            Log::error('Amadeus flight details failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get real-time flight prices
     */
    public function getFlightPrices(array $flightIds): array
    {
        $prices = [];
        
        foreach ($flightIds as $flightId) {
            try {
                $flight = $this->getFlightDetails($flightId);
                if ($flight) {
                    $prices[$flightId] = [
                        'price' => $flight['price'],
                        'currency' => $flight['currency'],
                        'updated_at' => now()->toISOString(),
                    ];
                }
            } catch (Exception $e) {
                Log::warning("Failed to get price for flight {$flightId}: " . $e->getMessage());
            }
        }

        return $prices;
    }

    /**
     * Book a flight (placeholder - requires additional implementation)
     */
    public function bookFlight(array $bookingData): array
    {
        // Note: Amadeus booking requires additional setup and certification
        // This is a placeholder implementation
        throw new Exception('Flight booking not yet implemented for Amadeus provider');
    }

    /**
     * Search airports
     */
    public function searchAirports(string $query): array
    {
        try {
            $params = [
                'keyword' => $query,
                'max' => 20,
            ];

            $response = $this->makeRequest('/v1/reference-data/locations', $params);
            
            return array_map(function ($location) {
                return [
                    'code' => $location['iataCode'],
                    'name' => $location['name'],
                    'city' => $location['address']['cityName'] ?? '',
                    'country' => $location['address']['countryName'] ?? '',
                    'timezone' => $location['timeZone']['name'] ?? '',
                ];
            }, $response['data'] ?? []);
        } catch (Exception $e) {
            Log::error('Amadeus airport search failed: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get airline information
     */
    public function getAirlineInfo(string $airlineCode): ?array
    {
        try {
            $response = $this->makeRequest('/v1/reference-data/airlines', [
                'airlineCodes' => $airlineCode
            ]);

            $airline = $response['data'][0] ?? null;
            
            if (!$airline) {
                return null;
            }

            return [
                'code' => $airline['iataCode'],
                'name' => $airline['businessName'],
                'country' => $airline['commonName'] ?? '',
            ];
        } catch (Exception $e) {
            Log::error('Amadeus airline info failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Check flight availability
     */
    public function checkAvailability(string $flightId): bool
    {
        $flight = $this->getFlightDetails($flightId);
        return $flight && ($flight['available_seats'] > 0);
    }

    /**
     * Get booking status
     */
    public function getBookingStatus(string $bookingReference): ?array
    {
        // Placeholder implementation
        throw new Exception('Booking status check not yet implemented for Amadeus provider');
    }

    /**
     * Extract baggage information from offer
     */
    protected function extractBaggageInfo(array $offer): array
    {
        // Simplified baggage info extraction
        return [
            'carry_on' => '1 piece',
            'checked' => '1 piece (23kg)',
        ];
    }

    /**
     * Extract cancellation policy from offer
     */
    protected function extractCancellationPolicy(array $offer): string
    {
        // Simplified cancellation policy
        return 'Cancellation fees may apply. Please check with airline.';
    }
}
