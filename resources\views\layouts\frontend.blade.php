<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="@stack('description'){{ config('app.name', 'Sky Avenue') }} - Professional Airline Booking System">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@stack('title'){{ config('app.name', 'Sky Avenue') }} | Professional Airline Booking System</title>

    <!-- Favicon -->
    <link rel="shortcut icon" type="image/x-icon" href="{{ asset('assets/media/favicon.png') }}">

    <!-- All CSS files -->
    <link rel="stylesheet" href="{{ asset('assets/css/vendor/font-awesome.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/fonts/icomoon/style.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/vendor/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/vendor/slick.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/vendor/slick-theme.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/vendor/datetime.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/vendor/calendar/classic.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/vendor/calendar/classic.date.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/vendor/jquery.timepicker.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/vendor/ui-autocomplete.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/vendor/sal.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
    
    @stack('styles')
</head>

<body class="@stack('body-class')">
    <!-- Preloader-->
    <div id="preloader">
        <div class="loader">
            <div class="plane">
                <img src="{{ asset('assets/media/plane.gif') }}" class="plane-img" alt="">
            </div>
            <div class="earth-wrapper">
                <div class="earth"></div>
            </div>
        </div>
    </div>
    
    <!-- Main Wrapper Start -->
    <div id="main-wrapper" class="main-wrapper mr-4">

        <!-- Header Area Start -->
        <header>
            <nav class="main-menu">
                <div class="container-fluid">
                    <div class="main-menu__block">
                        <div class="main-menu__left">

                            <div class="main-menu__logo">
                                <a href="{{ route('home') }}">
                                    <img src="{{ asset('assets/media/logo.png') }}" alt="{{ config('app.name', 'Sky Avenue') }}">
                                </a>
                            </div>

                            <div class="main-menu__nav">
                                <ul class="main-menu__list">
                                    <li><a href="{{ route('home') }}" class="{{ request()->routeIs('home') ? 'active' : '' }}">Home</a></li>
                                    <li><a href="{{ route('book-tickets') }}" class="{{ request()->routeIs('book-tickets') ? 'active' : '' }}">Book Tickets</a></li>
                                    <li class="dropdown">
                                        <a href="javascript:void(0);">Flight</a>
                                        <ul class="sub-menu">
                                            <li><a href="{{ route('flights.listing') }}">Flight Listing</a></li>
                                            <li><a href="{{ route('flights.booking') }}">Flight Booking</a></li>
                                            <li><a href="{{ route('flight-search.index') }}">Search Flights</a></li>
                                        </ul>
                                    </li>
                                    <li class="dropdown">
                                        <a href="javascript:void(0);">Car</a>
                                        <ul class="sub-menu">
                                            <li><a href="{{ route('cars.listing') }}">Car Listing</a></li>
                                            <li><a href="{{ route('cars.booking') }}">Car Booking</a></li>
                                            <li><a href="{{ route('cars.detail') }}">Car Detail</a></li>
                                        </ul>
                                    </li>
                                    <li class="dropdown">
                                        <a href="javascript:void(0);">Hotel</a>
                                        <ul class="sub-menu">
                                            <li><a href="{{ route('hotels.listing') }}">Hotel Listing</a></li>
                                            <li><a href="{{ route('hotels.booking') }}">Hotel Booking</a></li>
                                            <li><a href="{{ route('hotels.detail') }}">Hotel Detail</a></li>
                                        </ul>
                                    </li>
                                    <li class="dropdown">
                                        <a href="javascript:void(0);">Tour</a>
                                        <ul class="sub-menu">
                                            <li><a href="{{ route('tours.packages') }}">Tour Packages</a></li>
                                            <li><a href="{{ route('tours.detail') }}">Tour Detail</a></li>
                                        </ul>
                                    </li>
                                    <li class="dropdown">
                                        <a href="javascript:void(0);">Pages</a>
                                        <ul class="sub-menu">
                                            <li><a href="{{ route('about') }}">About</a></li>
                                            <li><a href="{{ route('contact') }}">Contact</a></li>
                                            <li><a href="{{ route('privacy') }}">Privacy Policy</a></li>
                                            @guest
                                                <li><a href="{{ route('login') }}">Login</a></li>
                                                <li><a href="{{ route('register') }}">Signup</a></li>
                                            @else
                                                <li><a href="{{ route('admin.dashboard') }}">Admin Dashboard</a></li>
                                                <li><a href="{{ route('admin.bookings.index') }}">Manage Bookings</a></li>
                                            @endguest
                                        </ul>
                                    </li>
                                    <li class="dropdown">
                                        <a href="javascript:void(0);">News</a>
                                        <ul>
                                            <li><a href="{{ route('blog.listing') }}">Blog Listing</a></li>
                                            <li><a href="{{ route('blog.detail') }}">Blog Detail</a></li>
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="main-menu__right">
                            <a href="#" class="main-menu__search search-toggler d-xl-flex d-none">
                                <i class="fal fa-search"></i>
                            </a>
                            
                            @guest
                                <div class="main-menu-signup__login d-xl-flex d-none">
                                    <a href="{{ route('login') }}" class="main-menu__login">
                                        Login
                                    </a>
                                    <div class="center_slach d-xl-flex d-none">/</div>
                                    <a href="{{ route('register') }}" class="main-menu__login">
                                        Signup
                                    </a>
                                </div>
                            @else
                                <div class="main-menu-signup__login d-xl-flex d-none">
                                    <div class="dropdown">
                                        <a href="#" class="main-menu__login dropdown-toggle" data-bs-toggle="dropdown">
                                            {{ Auth::user()->name }}
                                        </a>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                                            <li><a class="dropdown-item" href="{{ route('profile.edit') }}">Profile</a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <form method="POST" action="{{ route('logout') }}">
                                                    @csrf
                                                    <button type="submit" class="dropdown-item">Logout</button>
                                                </form>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            @endguest
                            
                            <a href="#" class="main-menu__toggler mobile-nav__toggler">
                                <i class="fa fa-bars"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </nav>
        </header>
        <!-- Header Area End -->

        @stack('content')

        @include('partials.footer')
        
    </div>
    <!-- Main Wrapper End -->

    <!-- All JS files -->
    <script src="{{ asset('assets/js/vendor/jquery-3.6.3.min.js') }}"></script>
    <script src="{{ asset('assets/js/vendor/bootstrap.min.js') }}"></script>
    <script src="{{ asset('assets/js/vendor/slick.min.js') }}"></script>
    <script src="{{ asset('assets/js/vendor/jquery.timepicker.min.js') }}"></script>
    <script src="{{ asset('assets/js/vendor/tilt.jquery.js') }}"></script>
    <script src="{{ asset('assets/js/vendor/jquery-appear.js') }}"></script>
    <script src="{{ asset('assets/js/vendor/sal.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}"></script>

    <!-- Initialize animations -->
    <script>
        $(document).ready(function() {
            // Initialize SAL (Scroll Animation Library)
            if (typeof sal !== 'undefined') {
                sal({
                    threshold: 0.1,
                    once: true,
                });
            }

            // Initialize Tilt effects
            if (typeof $.fn.tilt !== 'undefined') {
                $('.flynow-tilt').each(function() {
                    let options = $(this).data('tilt-options');
                    if (options) {
                        $(this).tilt(typeof options === 'object' ? options : JSON.parse(options));
                    }
                });
            }
        });
    </script>

    @stack('scripts')
</body>
</html>
