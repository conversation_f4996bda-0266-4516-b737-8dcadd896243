<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Flight;
use App\Models\Airline;
use App\Models\Airport;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class FlightController extends Controller
{
    /**
     * Display a listing of flights with filtering and search
     */
    public function index(Request $request): View
    {
        $query = Flight::with(['airline', 'departureAirport', 'arrivalAirport', 'bookings']);

        // Apply filters
        if ($request->filled('flight_type') && $request->flight_type !== 'All Types') {
            $query->where('flight_type', $request->flight_type);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('airline_id')) {
            $query->where('airline_id', $request->airline_id);
        }

        if ($request->filled('departure_airport')) {
            $query->whereHas('departureAirport', function ($q) use ($request) {
                $q->where('code', $request->departure_airport);
            });
        }

        if ($request->filled('arrival_airport')) {
            $query->whereHas('arrivalAirport', function ($q) use ($request) {
                $q->where('code', $request->arrival_airport);
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('departure_time', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('departure_time', '<=', $request->date_to);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('flight_number', 'like', "%{$search}%")
                  ->orWhereHas('departureAirport', function ($q) use ($search) {
                      $q->where('code', 'like', "%{$search}%")
                        ->orWhere('city', 'like', "%{$search}%");
                  })
                  ->orWhereHas('arrivalAirport', function ($q) use ($search) {
                      $q->where('code', 'like', "%{$search}%")
                        ->orWhere('city', 'like', "%{$search}%");
                  });
            });
        }

        // Sort by departure time by default
        $query->orderBy('departure_time', 'asc');

        // Handle export request
        if ($request->get('export') === 'csv') {
            return $this->exportFlights($query);
        }

        $flights = $query->paginate(20)->withQueryString();

        // Get filter options
        $airlines = Airline::active()->orderBy('name')->get();
        $airports = Airport::active()->orderBy('name')->get();
        $flightTypes = ['All Types', 'Domestic', 'International'];
        $statuses = ['Scheduled', 'Delayed', 'Cancelled', 'Completed'];

        return view('admin.flights.index', compact(
            'flights',
            'airlines',
            'airports',
            'flightTypes',
            'statuses'
        ));
    }

    /**
     * Show the form for creating a new flight
     */
    public function create(): View
    {
        $airlines = Airline::active()->orderBy('name')->get();
        $airports = Airport::active()->orderBy('name')->get();

        return view('admin.flights.create', compact('airlines', 'airports'));
    }

    /**
     * Store a newly created flight
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'flight_number' => 'required|string|max:10',
            'airline_id' => 'required|exists:airlines,id',
            'departure_airport_id' => 'required|exists:airports,id',
            'arrival_airport_id' => 'required|exists:airports,id|different:departure_airport_id',
            'departure_time' => 'required|date|after:now',
            'arrival_time' => 'required|date|after:departure_time',
            'aircraft_type' => 'required|string',
            'total_seats' => 'required|integer|min:1',
            'base_price' => 'required|numeric|min:0',
            'flight_type' => 'required|in:Domestic,International',
            'class_type' => 'required|in:Economy,Business,First',
            'gate' => 'nullable|string|max:10',
            'notes' => 'nullable|string',
        ]);

        // Calculate duration
        $departure = new \DateTime($validated['departure_time']);
        $arrival = new \DateTime($validated['arrival_time']);
        $validated['duration_minutes'] = $arrival->diff($departure)->h * 60 + $arrival->diff($departure)->i;
        $validated['available_seats'] = $validated['total_seats'];

        Flight::create($validated);

        return redirect()->route('admin.flights.index')
            ->with('success', 'Flight created successfully.');
    }

    /**
     * Display the specified flight
     */
    public function show(Flight $flight): View
    {
        $flight->load(['airline', 'departureAirport', 'arrivalAirport', 'bookings.passengers']);

        return view('admin.flights.show', compact('flight'));
    }

    /**
     * Show the form for editing the specified flight
     */
    public function edit(Flight $flight): View
    {
        $airlines = Airline::active()->orderBy('name')->get();
        $airports = Airport::active()->orderBy('name')->get();

        return view('admin.flights.edit', compact('flight', 'airlines', 'airports'));
    }

    /**
     * Update the specified flight
     */
    public function update(Request $request, Flight $flight): RedirectResponse
    {
        $validated = $request->validate([
            'flight_number' => 'required|string|max:10',
            'airline_id' => 'required|exists:airlines,id',
            'departure_airport_id' => 'required|exists:airports,id',
            'arrival_airport_id' => 'required|exists:airports,id|different:departure_airport_id',
            'departure_time' => 'required|date',
            'arrival_time' => 'required|date|after:departure_time',
            'aircraft_type' => 'required|string',
            'total_seats' => 'required|integer|min:1',
            'base_price' => 'required|numeric|min:0',
            'flight_type' => 'required|in:Domestic,International',
            'class_type' => 'required|in:Economy,Business,First',
            'status' => 'required|in:Scheduled,Delayed,Cancelled,Completed',
            'gate' => 'nullable|string|max:10',
            'notes' => 'nullable|string',
        ]);

        // Calculate duration
        $departure = new \DateTime($validated['departure_time']);
        $arrival = new \DateTime($validated['arrival_time']);
        $validated['duration_minutes'] = $arrival->diff($departure)->h * 60 + $arrival->diff($departure)->i;

        $flight->update($validated);

        return redirect()->route('admin.flights.index')
            ->with('success', 'Flight updated successfully.');
    }

    /**
     * Remove the specified flight
     */
    public function destroy(Flight $flight): RedirectResponse
    {
        if ($flight->bookings()->count() > 0) {
            return redirect()->route('admin.flights.index')
                ->with('error', 'Cannot delete flight with existing bookings.');
        }

        $flight->delete();

        return redirect()->route('admin.flights.index')
            ->with('success', 'Flight deleted successfully.');
    }

    /**
     * Export flights to CSV
     */
    private function exportFlights($query)
    {
        $flights = $query->with(['airline', 'departureAirport', 'arrivalAirport'])->get();

        $filename = 'flights_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($flights) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Flight Number',
                'Airline',
                'Departure Airport',
                'Arrival Airport',
                'Departure Time',
                'Arrival Time',
                'Duration (minutes)',
                'Aircraft Type',
                'Total Seats',
                'Available Seats',
                'Base Price',
                'Flight Type',
                'Class Type',
                'Status',
                'Gate'
            ]);

            // CSV data
            foreach ($flights as $flight) {
                fputcsv($file, [
                    $flight->flight_number,
                    $flight->airline->name,
                    $flight->departureAirport->code . ' - ' . $flight->departureAirport->city,
                    $flight->arrivalAirport->code . ' - ' . $flight->arrivalAirport->city,
                    $flight->departure_time->format('Y-m-d H:i:s'),
                    $flight->arrival_time->format('Y-m-d H:i:s'),
                    $flight->duration_minutes,
                    $flight->aircraft_type,
                    $flight->total_seats,
                    $flight->available_seats,
                    $flight->base_price,
                    $flight->flight_type,
                    $flight->class_type,
                    $flight->status,
                    $flight->gate
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
