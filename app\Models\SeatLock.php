<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class SeatLock extends Model
{
    protected $fillable = [
        'pnr_id',
        'session_id',
        'user_ip',
        'seats_locked',
        'locked_at',
        'expires_at',
        'status',
        'booking_reference',
    ];

    protected $casts = [
        'seats_locked' => 'integer',
        'locked_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($lock) {
            if (!$lock->locked_at) {
                $lock->locked_at = now();
            }
            if (!$lock->expires_at) {
                $lock->expires_at = now()->addSeconds(55);
            }
        });
    }

    /**
     * Get the PNR for this lock
     */
    public function pnr(): BelongsTo
    {
        return $this->belongsTo(Pnr::class);
    }

    /**
     * Check if lock is active
     */
    public function isActive(): bool
    {
        return $this->status === 'active' && $this->expires_at > now();
    }

    /**
     * Check if lock is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at <= now();
    }

    /**
     * Get remaining time in seconds
     */
    public function getRemainingTimeAttribute(): int
    {
        if ($this->isExpired()) {
            return 0;
        }

        return max(0, $this->expires_at->diffInSeconds(now()));
    }

    /**
     * Extend lock duration
     */
    public function extend(int $seconds = 55): bool
    {
        if (!$this->isActive()) {
            return false;
        }

        $this->expires_at = now()->addSeconds($seconds);
        return $this->save();
    }

    /**
     * Complete the lock (booking was successful)
     */
    public function complete(string $bookingReference): bool
    {
        $this->status = 'completed';
        $this->booking_reference = $bookingReference;
        return $this->save();
    }

    /**
     * Cancel the lock
     */
    public function cancel(): bool
    {
        $this->status = 'cancelled';
        return $this->save();
    }

    /**
     * Expire the lock
     */
    public function expire(): bool
    {
        $this->status = 'expired';
        return $this->save();
    }

    /**
     * Scope for active locks
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where('expires_at', '>', now());
    }

    /**
     * Scope for expired locks
     */
    public function scopeExpired($query)
    {
        return $query->where('status', 'active')
                    ->where('expires_at', '<=', now());
    }

    /**
     * Scope for locks by session
     */
    public function scopeBySession($query, string $sessionId)
    {
        return $query->where('session_id', $sessionId);
    }

    /**
     * Clean up expired locks
     */
    public static function cleanupExpired(): int
    {
        return static::expired()->update(['status' => 'expired']);
    }
}
