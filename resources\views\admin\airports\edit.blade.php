@extends('layouts.admin')

@section('title', 'Edit Airport - ' . $airport->code)

@section('content')
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="page-title mb-2">Edit Airport - {{ $airport->code }}</h1>
                <p class="page-subtitle mb-0">Update airport information</p>
            </div>
            <a href="{{ route('admin.airports.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Airports
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-xl-8 col-lg-10">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Airport Information</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.airports.update', $airport) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="code" class="form-label">Airport Code (IATA) <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('code') is-invalid @enderror" 
                                       id="code" name="code" value="{{ old('code', $airport->code) }}" 
                                       placeholder="e.g., JFK" maxlength="3" style="text-transform: uppercase;" required>
                                @error('code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">3-letter IATA airport code</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Airport Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name', $airport->name) }}" 
                                       placeholder="e.g., John F. Kennedy International Airport" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="city" class="form-label">City <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('city') is-invalid @enderror" 
                                       id="city" name="city" value="{{ old('city', $airport->city) }}" 
                                       placeholder="e.g., New York" required>
                                @error('city')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="country" class="form-label">Country <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('country') is-invalid @enderror" 
                                       id="country" name="country" value="{{ old('country', $airport->country) }}" 
                                       placeholder="e.g., United States" required>
                                @error('country')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="latitude" class="form-label">Latitude</label>
                                <input type="number" class="form-control @error('latitude') is-invalid @enderror" 
                                       id="latitude" name="latitude" value="{{ old('latitude', $airport->latitude) }}" 
                                       step="0.00000001" min="-90" max="90"
                                       placeholder="e.g., 40.6413">
                                @error('latitude')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Decimal degrees (-90 to 90)</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="longitude" class="form-label">Longitude</label>
                                <input type="number" class="form-control @error('longitude') is-invalid @enderror" 
                                       id="longitude" name="longitude" value="{{ old('longitude', $airport->longitude) }}" 
                                       step="0.00000001" min="-180" max="180"
                                       placeholder="e.g., -73.7781">
                                @error('longitude')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Decimal degrees (-180 to 180)</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="timezone" class="form-label">Timezone</label>
                                <input type="text" class="form-control @error('timezone') is-invalid @enderror" 
                                       id="timezone" name="timezone" value="{{ old('timezone', $airport->timezone) }}" 
                                       placeholder="e.g., America/New_York">
                                @error('timezone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">IANA timezone identifier</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check form-switch mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_active" 
                                           name="is_active" {{ old('is_active', $airport->is_active) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        Active Airport
                                    </label>
                                </div>
                                <div class="form-text">Only active airports appear in autocomplete</div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.airports.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Update Airport
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-xl-4 col-lg-2">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">Airport Details</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Full Display:</strong><br>
                    <span class="text-muted">{{ $airport->code }} - {{ $airport->name }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Location:</strong><br>
                    <span class="text-muted">{{ $airport->city }}, {{ $airport->country }}</span>
                </div>
                
                @if($airport->latitude && $airport->longitude)
                <div class="mb-3">
                    <strong>Coordinates:</strong><br>
                    <span class="text-muted">{{ $airport->latitude }}, {{ $airport->longitude }}</span>
                </div>
                @endif
                
                @if($airport->timezone)
                <div class="mb-3">
                    <strong>Timezone:</strong><br>
                    <span class="text-muted">{{ $airport->timezone }}</span>
                </div>
                @endif
                
                <div class="mb-3">
                    <strong>Status:</strong><br>
                    <span class="badge {{ $airport->is_active ? 'bg-success' : 'bg-warning' }}">
                        {{ $airport->is_active ? 'Active' : 'Inactive' }}
                    </span>
                </div>
                
                <div class="mb-3">
                    <strong>Created:</strong><br>
                    <span class="text-muted">{{ $airport->created_at->format('M d, Y H:i') }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Updated:</strong><br>
                    <span class="text-muted">{{ $airport->updated_at->format('M d, Y H:i') }}</span>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">Test Autocomplete</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="testSearch" class="form-label">Search Test</label>
                    <input type="text" class="form-control" id="testSearch" 
                           placeholder="Type to test autocomplete...">
                </div>
                <div id="testResults" class="small text-muted"></div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Auto-uppercase airport code
    $('#code').on('input', function() {
        this.value = this.value.toUpperCase();
    });
    
    // Test autocomplete functionality
    let searchTimeout;
    $('#testSearch').on('input', function() {
        const query = $(this).val();
        
        clearTimeout(searchTimeout);
        
        if (query.length < 1) {
            $('#testResults').html('');
            return;
        }
        
        searchTimeout = setTimeout(function() {
            $.get('/api/airports/autocomplete', { query: query })
                .done(function(response) {
                    if (response.success && response.data.length > 0) {
                        let html = '<strong>Results:</strong><br>';
                        response.data.forEach(function(airport) {
                            html += `<div class="mb-1">
                                <span class="badge bg-primary">${airport.code}</span> 
                                ${airport.name}<br>
                                <small class="text-muted">${airport.city}, ${airport.country}</small>
                            </div>`;
                        });
                        $('#testResults').html(html);
                    } else {
                        $('#testResults').html('<em>No results found</em>');
                    }
                })
                .fail(function() {
                    $('#testResults').html('<em class="text-danger">Search failed</em>');
                });
        }, 300);
    });
});
</script>
@endpush
