@extends('layouts.frontend')

@push('title')
    Hotels - {{ config('app.name', 'Sky Avenue') }} |
@endpush

@push('description')
    Find and book the best hotels worldwide with {{ config('app.name', 'Sky Avenue') }}. Compare prices and amenities.
@endpush

@push('content')
    <!-- Hero Banner start -->
    <section class="hero-banner-1">
        <div class="container-fluid">
            <div class="content">
                <div class="vector-image">
                    <svg xmlns="http://www.w3.org/2000/svg" width="1414" height="319" viewBox="0 0 1414 319" fill="none">
                        <path class="path"
                            d="M-0.5 215C62.4302 220.095 287 228 373 143.5C444.974 72.7818 368.5 -3.73136 320.5 1.99997C269.5 8.08952 231.721 43.5 253.5 119C275.279 194.5 367 248.212 541.5 207.325C675.76 175.867 795.5 82.7122 913 76.7122C967.429 73.9328 1072.05 88.6813 1085 207.325C1100 344.712 882 340.212 922.5 207.325C964.415 69.7967 1354 151.5 1479 183.5"
                            stroke="#ECECF2" stroke-width="6" stroke-linecap="round" stroke-dasharray="round" />
                    </svg>
                </div>
                <div class="row">
                    <div class="col-lg-6">
                        <div class="banner-content">
                            <h1 class="banner-title">Find Your Perfect Hotel</h1>
                            <p class="banner-text">Discover amazing hotels worldwide with the best prices and amenities for your perfect stay.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Hero Banner end -->

    <!-- Hotels Section start -->
    <section class="available-flights mb-20 mt-5">
        <div class="container-fluid">
            <div class="content">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center flex-wrap">
                            <h3 class="lightest-black h3 bold mb-0 px-4">Hotel Search & Booking</h3>
                            <div class="d-flex gap-3 flex-wrap">
                                <div class="professional-search-wrapper">
                                    <div class="search-input-container">
                                        <i class="fas fa-search search-icon"></i>
                                        <input type="text" class="professional-search-input" id="hotelSearch" placeholder="Search hotels, destinations...">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12">
                                <div class="text-center py-5">
                                    <i class="fas fa-hotel fa-4x text-primary mb-4"></i>
                                    <h4 class="text-muted mb-3">Hotel Booking Coming Soon</h4>
                                    <p class="text-muted">We're working hard to bring you the best hotel booking experience. Stay tuned!</p>
                                    <a href="{{ route('home') }}" class="cus-btn mt-3">
                                        <i class="fas fa-arrow-left me-2"></i>Back to Home
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Hotels Section end -->
@endpush

@push('styles')
    <style>
        .hero-banner-1 {
            background: linear-gradient(135deg, #4D73FC 0%, #6B8AFF 100%);
            color: white;
            padding: 100px 0;
            position: relative;
            overflow: hidden;
        }
        
        .banner-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            font-family: "Inter", sans-serif;
        }
        
        .banner-text {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }
        
        .vector-image {
            position: absolute;
            top: 0;
            right: 0;
            opacity: 0.1;
        }
        
        .professional-search-wrapper {
            min-width: 280px;
        }
        
        .search-input-container {
            position: relative;
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, #FFFFFF 0%, #F8F8FF 100%);
            border: 2px solid #ECECF2;
            border-radius: 12px;
            padding: 0;
            transition: all 0.3s ease;
            box-shadow: 0px 2px 8px rgba(77, 115, 252, 0.08);
        }
        
        .search-input-container:hover {
            border-color: #4D73FC;
            box-shadow: 0px 4px 15px rgba(77, 115, 252, 0.15);
        }
        
        .search-icon {
            position: absolute;
            left: 1rem;
            color: #4D73FC;
            font-size: 0.875rem;
            z-index: 2;
        }
        
        .professional-search-input {
            width: 100%;
            border: none;
            background: transparent;
            padding: 0.875rem 2.5rem 0.875rem 2.75rem;
            font-family: "Inter", sans-serif;
            font-size: 0.875rem;
            color: #16191A;
            outline: none;
            border-radius: 12px;
        }
        
        .professional-search-input::placeholder {
            color: #9CA3AF;
            font-style: italic;
        }
        
        @media (max-width: 768px) {
            .banner-title {
                font-size: 2rem;
            }
            
            .professional-search-wrapper {
                min-width: 100%;
                margin-bottom: 0.75rem;
            }
        }
    </style>
@endpush
