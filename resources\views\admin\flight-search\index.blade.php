@extends('layouts.admin')

@section('title', 'Flight Search Management')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="page-title mb-2">Flight Search Management</h1>
                <p class="page-subtitle mb-0">Monitor and manage frontend flight search functionality</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-primary" onclick="testApiConnection()">
                    <i class="fas fa-plug me-1"></i>Test API Connection
                </button>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#testSearchModal">
                    <i class="fas fa-search me-1"></i>Test Search
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ $searchStats['today'] ?? 0 }}</h4>
                        <p class="mb-0">Searches Today</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-search fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ $searchStats['week'] ?? 0 }}</h4>
                        <p class="mb-0">This Week</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ $apiStatus['active_providers'] ?? 0 }}</h4>
                        <p class="mb-0">Active Providers</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-plug fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ $searchStats['avg_response_time'] ?? 0 }}ms</h4>
                        <p class="mb-0">Avg Response Time</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- API Providers Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">API Providers Status</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Provider</th>
                                <th>Status</th>
                                <th>Last Check</th>
                                <th>Response Time</th>
                                <th>Success Rate</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($providers ?? [] as $provider)
                            <tr>
                                <td>
                                    <strong>{{ ucfirst($provider['name']) }}</strong>
                                    <br><small class="text-muted">{{ $provider['description'] ?? '' }}</small>
                                </td>
                                <td>
                                    @if($provider['status'] === 'active')
                                        <span class="badge bg-success">Active</span>
                                    @elseif($provider['status'] === 'error')
                                        <span class="badge bg-danger">Error</span>
                                    @else
                                        <span class="badge bg-secondary">Inactive</span>
                                    @endif
                                </td>
                                <td>{{ $provider['last_check'] ?? 'Never' }}</td>
                                <td>{{ $provider['response_time'] ?? 'N/A' }}</td>
                                <td>{{ $provider['success_rate'] ?? 'N/A' }}</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="testProvider('{{ $provider['name'] }}')">
                                        <i class="fas fa-play"></i> Test
                                    </button>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="6" class="text-center">No providers configured</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Search Logs -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Recent Search Logs</h5>
                <button class="btn btn-sm btn-outline-secondary" onclick="refreshLogs()">
                    <i class="fas fa-refresh"></i> Refresh
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>Route</th>
                                <th>Passengers</th>
                                <th>Results</th>
                                <th>Response Time</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody id="searchLogsTable">
                            @forelse($recentSearches ?? [] as $search)
                            <tr>
                                <td>{{ $search['created_at'] ?? 'N/A' }}</td>
                                <td>{{ $search['route'] ?? 'N/A' }}</td>
                                <td>{{ $search['passengers'] ?? 'N/A' }}</td>
                                <td>{{ $search['results_count'] ?? 0 }}</td>
                                <td>{{ $search['response_time'] ?? 'N/A' }}</td>
                                <td>
                                    @if($search['status'] === 'success')
                                        <span class="badge bg-success">Success</span>
                                    @else
                                        <span class="badge bg-danger">Failed</span>
                                    @endif
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="6" class="text-center">No recent searches</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Search Modal -->
<div class="modal fade" id="testSearchModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Test Flight Search</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="testSearchForm">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">From Airport Code</label>
                            <input type="text" class="form-control" id="test_departure" placeholder="e.g., JFK" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">To Airport Code</label>
                            <input type="text" class="form-control" id="test_arrival" placeholder="e.g., LAX" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Departure Date</label>
                            <input type="date" class="form-control" id="test_departure_date" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Return Date (Optional)</label>
                            <input type="date" class="form-control" id="test_return_date">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Adults</label>
                            <select class="form-select" id="test_adults">
                                <option value="1" selected>1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Children</label>
                            <select class="form-select" id="test_children">
                                <option value="0" selected>0</option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Class</label>
                            <select class="form-select" id="test_class">
                                <option value="economy" selected>Economy</option>
                                <option value="business">Business</option>
                                <option value="first">First</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="runTestSearch()">
                    <i class="fas fa-search me-1"></i>Test Search
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Test Results Modal -->
<div class="modal fade" id="testResultsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Test Search Results</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="testResultsContent">
                    <!-- Results will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Set minimum date to today
    const today = new Date().toISOString().split('T')[0];
    $('#test_departure_date').attr('min', today).val(today);
    $('#test_return_date').attr('min', today);
});

// Test API Connection
function testApiConnection() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Testing...';
    btn.disabled = true;

    fetch('{{ route("admin.flight-api.index") }}', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'API connection successful');
        } else {
            showAlert('danger', 'API connection failed: ' + data.message);
        }
    })
    .catch(error => {
        showAlert('danger', 'API connection test failed');
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// Test individual provider
function testProvider(providerName) {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    btn.disabled = true;

    fetch('{{ route("admin.flight-api.search") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            departure_airport: 'JFK',
            arrival_airport: 'LAX',
            departure_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            adults: 1,
            cabin_class: 'economy',
            max_results: 5,
            provider: providerName
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', `${providerName} test successful: ${data.count} flights found`);
        } else {
            showAlert('danger', `${providerName} test failed: ${data.message}`);
        }
    })
    .catch(error => {
        showAlert('danger', `${providerName} test failed`);
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// Run test search
function runTestSearch() {
    const formData = {
        departure_airport: $('#test_departure').val(),
        arrival_airport: $('#test_arrival').val(),
        departure_date: $('#test_departure_date').val(),
        return_date: $('#test_return_date').val(),
        adults: $('#test_adults').val(),
        children: $('#test_children').val(),
        cabin_class: $('#test_class').val(),
        max_results: 10
    };

    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Searching...';
    btn.disabled = true;

    fetch('{{ route("admin.flight-api.search") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        displayTestResults(data);
        $('#testSearchModal').modal('hide');
        $('#testResultsModal').modal('show');
    })
    .catch(error => {
        showAlert('danger', 'Test search failed');
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// Display test results
function displayTestResults(data) {
    let html = '';

    if (data.success) {
        html += `<div class="alert alert-success">
                    <strong>Search Successful!</strong><br>
                    Found ${data.count} flights in ${data.response_time || 'N/A'}ms
                 </div>`;

        if (data.data && data.data.length > 0) {
            html += '<div class="table-responsive"><table class="table table-striped">';
            html += '<thead><tr><th>Airline</th><th>Route</th><th>Departure</th><th>Arrival</th><th>Duration</th><th>Price</th><th>Provider</th></tr></thead><tbody>';

            data.data.forEach(flight => {
                html += `<tr>
                    <td>${flight.airline || 'N/A'}</td>
                    <td>${flight.departure_airport} → ${flight.arrival_airport}</td>
                    <td>${flight.departure_time || 'N/A'}</td>
                    <td>${flight.arrival_time || 'N/A'}</td>
                    <td>${flight.duration || 'N/A'}</td>
                    <td>$${flight.price || 'N/A'}</td>
                    <td>${flight.provider || 'N/A'}</td>
                </tr>`;
            });

            html += '</tbody></table></div>';
        }
    } else {
        html += `<div class="alert alert-danger">
                    <strong>Search Failed!</strong><br>
                    ${data.message || 'Unknown error occurred'}
                 </div>`;
    }

    $('#testResultsContent').html(html);
}

// Refresh logs
function refreshLogs() {
    location.reload();
}

// Show alert function
function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // Remove existing alerts
    $('.alert').remove();

    // Add new alert at the top of the page
    $('main .container-fluid').prepend(alertHtml);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}
</script>
@endpush
