<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Airport;
use App\Services\AirportAutocompleteService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Validator;

class AirportController extends Controller
{
    protected AirportAutocompleteService $autocompleteService;

    public function __construct(AirportAutocompleteService $autocompleteService)
    {
        $this->autocompleteService = $autocompleteService;
    }

    /**
     * Display airport management page
     */
    public function index(Request $request): View
    {
        $query = $request->get('search');
        $perPage = $request->get('per_page', 15);

        $airports = Airport::query()
            ->when($query, function ($q) use ($query) {
                $q->where('code', 'LIKE', "%{$query}%")
                  ->orWhere('name', 'LIKE', "%{$query}%")
                  ->orWhere('city', 'LIKE', "%{$query}%")
                  ->orWhere('country', 'LIKE', "%{$query}%");
            })
            ->orderBy('code')
            ->paginate($perPage);

        $stats = [
            'total' => Airport::count(),
            'active' => Airport::active()->count(),
            'inactive' => Airport::where('is_active', false)->count(),
        ];

        return view('admin.airports.index', compact('airports', 'stats', 'query'));
    }

    /**
     * Show create airport form
     */
    public function create(): View
    {
        return view('admin.airports.create');
    }

    /**
     * Store new airport
     */
    public function store(Request $request): RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required|string|size:3|unique:airports,code',
            'name' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'country' => 'required|string|max:255',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'timezone' => 'nullable|string|max:50',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $data = $request->all();
            $data['code'] = strtoupper($data['code']);
            $data['is_active'] = $request->has('is_active');

            Airport::create($data);

            // Clear cache
            $this->autocompleteService->clearCache();

            return redirect()->route('admin.airports.index')
                ->with('success', 'Airport created successfully');

        } catch (\Exception $e) {
            \Log::error('Failed to create airport', [
                'data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return redirect()->back()
                ->with('error', 'Failed to create airport: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Show edit airport form
     */
    public function edit(Airport $airport): View
    {
        return view('admin.airports.edit', compact('airport'));
    }

    /**
     * Update airport
     */
    public function update(Request $request, Airport $airport): RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required|string|size:3|unique:airports,code,' . $airport->id,
            'name' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'country' => 'required|string|max:255',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'timezone' => 'nullable|string|max:50',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $data = $request->all();
            $data['code'] = strtoupper($data['code']);
            $data['is_active'] = $request->has('is_active');

            $airport->update($data);

            // Clear cache
            $this->autocompleteService->clearCache();

            return redirect()->route('admin.airports.index')
                ->with('success', 'Airport updated successfully');

        } catch (\Exception $e) {
            \Log::error('Failed to update airport', [
                'airport_id' => $airport->id,
                'data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return redirect()->back()
                ->with('error', 'Failed to update airport: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Delete airport
     */
    public function destroy(Airport $airport): RedirectResponse
    {
        try {
            $airport->delete();

            // Clear cache
            $this->autocompleteService->clearCache();

            return redirect()->route('admin.airports.index')
                ->with('success', 'Airport deleted successfully');

        } catch (\Exception $e) {
            \Log::error('Failed to delete airport', [
                'airport_id' => $airport->id,
                'error' => $e->getMessage()
            ]);

            return redirect()->back()
                ->with('error', 'Failed to delete airport: ' . $e->getMessage());
        }
    }

    /**
     * Bulk import airports
     */
    public function bulkImport(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|mimes:csv,txt|max:2048'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid file',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $file = $request->file('file');
            $csvData = array_map('str_getcsv', file($file->getPathname()));
            $header = array_shift($csvData);

            $imported = 0;
            $errors = [];

            foreach ($csvData as $row) {
                if (count($row) !== count($header)) {
                    continue;
                }

                $airportData = array_combine($header, $row);
                
                try {
                    Airport::updateOrCreate(
                        ['code' => strtoupper($airportData['code'])],
                        [
                            'name' => $airportData['name'],
                            'city' => $airportData['city'],
                            'country' => $airportData['country'],
                            'latitude' => $airportData['latitude'] ?? null,
                            'longitude' => $airportData['longitude'] ?? null,
                            'timezone' => $airportData['timezone'] ?? null,
                            'is_active' => ($airportData['is_active'] ?? 'true') === 'true'
                        ]
                    );
                    $imported++;
                } catch (\Exception $e) {
                    $errors[] = "Row {$imported}: " . $e->getMessage();
                }
            }

            // Clear cache
            $this->autocompleteService->clearCache();

            return response()->json([
                'success' => true,
                'message' => "Successfully imported {$imported} airports",
                'data' => [
                    'imported' => $imported,
                    'errors' => $errors
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Bulk import failed', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Import failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export airports to CSV
     */
    public function export(): \Symfony\Component\HttpFoundation\StreamedResponse
    {
        $fileName = 'airports_' . date('Y-m-d_H-i-s') . '.csv';

        return response()->streamDownload(function () {
            $handle = fopen('php://output', 'w');
            
            // CSV Header
            fputcsv($handle, ['code', 'name', 'city', 'country', 'latitude', 'longitude', 'timezone', 'is_active']);

            // Data
            Airport::chunk(100, function ($airports) use ($handle) {
                foreach ($airports as $airport) {
                    fputcsv($handle, [
                        $airport->code,
                        $airport->name,
                        $airport->city,
                        $airport->country,
                        $airport->latitude,
                        $airport->longitude,
                        $airport->timezone,
                        $airport->is_active ? 'true' : 'false'
                    ]);
                }
            });

            fclose($handle);
        }, $fileName, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $fileName . '"'
        ]);
    }

    /**
     * Toggle airport status
     */
    public function toggleStatus(Airport $airport): JsonResponse
    {
        try {
            $airport->update(['is_active' => !$airport->is_active]);

            // Clear cache
            $this->autocompleteService->clearCache();

            return response()->json([
                'success' => true,
                'message' => 'Airport status updated',
                'is_active' => $airport->is_active
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update status'
            ], 500);
        }
    }
}
