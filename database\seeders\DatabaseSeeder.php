<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user if it doesn't exist
        if (!User::where('email', '<EMAIL>')->exists()) {
            User::factory()->create([
                'name' => 'Admin User',
                'email' => '<EMAIL>',
            ]);
        }

        // Create additional test users
        User::factory(10)->create();

        // Seed flight booking system data
        $this->call([
            AirlineSeeder::class,
            AirportSeeder::class,
            FlightSeeder::class,
            BookingSeeder::class,
        ]);
    }
}
