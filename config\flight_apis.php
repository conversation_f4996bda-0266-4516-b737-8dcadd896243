<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Flight API Provider
    |--------------------------------------------------------------------------
    |
    | This option controls the default flight API provider that will be used
    | by the flight search service. You may set this to any of the providers
    | defined in the "providers" array below.
    |
    */

    'default' => env('FLIGHT_API_DEFAULT', 'amadeus'),

    /*
    |--------------------------------------------------------------------------
    | Flight API Providers
    |--------------------------------------------------------------------------
    |
    | Here you may configure the flight API providers for your application.
    | Multiple providers are supported with automatic fallback capabilities.
    |
    */

    'providers' => [
        'amadeus' => [
            'enabled' => env('AMADEUS_ENABLED', true),
            'client_id' => env('AMADEUS_CLIENT_ID'),
            'client_secret' => env('AMADEUS_CLIENT_SECRET'),
            'base_url' => env('AMADEUS_BASE_URL', 'https://api.amadeus.com'),
            'test_mode' => env('AMADEUS_TEST_MODE', true),
            'rate_limit' => 10, // requests per second
            'timeout' => 30, // seconds
        ],

        'rapidapi_skyscanner' => [
            'enabled' => env('RAPIDAPI_SKYSCANNER_ENABLED', true),
            'api_key' => env('RAPIDAPI_SKYSCANNER_KEY'),
            'base_url' => 'https://skyscanner80.p.rapidapi.com',
            'rate_limit' => 5,
            'timeout' => 30,
        ],

        'travelpayouts' => [
            'enabled' => env('TRAVELPAYOUTS_ENABLED', false),
            'api_key' => env('TRAVELPAYOUTS_API_KEY'),
            'marker' => env('TRAVELPAYOUTS_MARKER'),
            'base_url' => 'https://api.travelpayouts.com',
            'rate_limit' => 100,
            'timeout' => 30,
        ],

        'aviationstack' => [
            'enabled' => env('AVIATIONSTACK_ENABLED', false),
            'api_key' => env('AVIATIONSTACK_API_KEY'),
            'base_url' => 'https://api.aviationstack.com/v1',
            'rate_limit' => 100,
            'timeout' => 30,
        ],

        // Free Mock Provider for Development/Demo
        'mock_provider' => [
            'enabled' => env('MOCK_PROVIDER_ENABLED', true),
            'timeout' => 5,
            'max_results' => 20,
        ],

        // Free Airlabs API
        'airlabs' => [
            'enabled' => env('AIRLABS_ENABLED', true),
            'api_key' => env('AIRLABS_API_KEY', 'demo-key'),
            'base_url' => 'https://airlabs.co/api/v9',
            'rate_limit' => 100,
            'timeout' => 30,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Fallback Configuration
    |--------------------------------------------------------------------------
    |
    | Configure the fallback behavior when primary providers fail.
    |
    */

    'fallback' => [
        'enabled' => true,
        'order' => ['mock_provider', 'airlabs', 'amadeus', 'rapidapi_skyscanner', 'travelpayouts'],
        'max_retries' => 3,
        'retry_delay' => 2, // seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Configuration
    |--------------------------------------------------------------------------
    |
    | Configure caching for flight search results to improve performance
    | and reduce API calls.
    |
    */

    'cache' => [
        'enabled' => env('FLIGHT_CACHE_ENABLED', true),
        'ttl' => env('FLIGHT_CACHE_TTL', 300), // 5 minutes
        'prefix' => 'flight_search',
    ],

    /*
    |--------------------------------------------------------------------------
    | Search Configuration
    |--------------------------------------------------------------------------
    |
    | Default search parameters and limits.
    |
    */

    'search' => [
        'max_results' => 50,
        'default_adults' => 1,
        'default_children' => 0,
        'default_infants' => 0,
        'default_cabin_class' => 'economy',
        'max_stops' => 2,
        'currency' => 'USD',
        'locale' => 'en-US',
    ],

    /*
    |--------------------------------------------------------------------------
    | Booking Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for flight booking functionality.
    |
    */

    'booking' => [
        'enabled' => env('FLIGHT_BOOKING_ENABLED', true),
        'test_mode' => env('FLIGHT_BOOKING_TEST_MODE', true),
        'confirmation_timeout' => 1800, // 30 minutes
        'payment_timeout' => 900, // 15 minutes
    ],

    /*
    |--------------------------------------------------------------------------
    | Webhook Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for receiving real-time updates from flight APIs.
    |
    */

    'webhooks' => [
        'enabled' => env('FLIGHT_WEBHOOKS_ENABLED', false),
        'secret' => env('FLIGHT_WEBHOOK_SECRET'),
        'endpoints' => [
            'price_updates' => '/webhooks/flight/price-updates',
            'booking_updates' => '/webhooks/flight/booking-updates',
            'schedule_changes' => '/webhooks/flight/schedule-changes',
        ],
    ],
];
