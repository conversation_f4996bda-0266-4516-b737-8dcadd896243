<?php

namespace Database\Seeders;

use App\Models\Booking;
use App\Models\Passenger;
use App\Models\Flight;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class BookingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $flights = Flight::all();
        $users = User::all();

        $bookingStatuses = ['Pending', 'Confirmed', 'Cancelled', 'Completed'];
        $paymentStatuses = ['Pending', 'Paid', 'Failed', 'Refunded'];
        $paymentMethods = ['Credit Card', 'PayPal', 'Bank Transfer', 'Debit Card'];

        $titles = ['Mr', 'Mrs', 'Ms', 'Miss', 'Dr'];
        $genders = ['Male', 'Female'];
        $passengerTypes = ['Adult', 'Child', 'Infant'];

        $firstNames = [
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
        ];

        $lastNames = [
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
        ];

        // Create sample bookings without requiring flights table
        for ($i = 0; $i < 50; $i++) {
            $passengerCount = rand(1, 4);
            $user = $users->is<PERSON>otEmpty() ? $users->random() : null;

            // Sample flight data
            $airports = ['ISB', 'BAH', 'SHJ', 'DXB', 'LHE', 'KHI'];
            $airlines = ['PK', 'PA', 'G9', 'EK'];
            $departureAirport = $airports[array_rand($airports)];
            $arrivalAirport = $airports[array_rand($airports)];
            while ($arrivalAirport === $departureAirport) {
                $arrivalAirport = $airports[array_rand($airports)];
            }

            $departureDate = Carbon::now()->addDays(rand(-30, 60));
            $departureTime = sprintf('%02d:%02d', rand(6, 22), rand(0, 59));
            $arrivalTime = sprintf('%02d:%02d', rand(6, 22), rand(0, 59));

            // Determine booking status
            $bookingStatus = $bookingStatuses[array_rand($bookingStatuses)];
            $paymentStatus = $paymentStatuses[array_rand($paymentStatuses)];
            $status = strtolower($bookingStatus);

            $totalAmount = rand(50000, 200000); // PKR

            $booking = Booking::create([
                'user_id' => $user ? $user->id : null,
                'flight_id' => rand(0, 1) ? rand(1, 2) : null, // Use existing flight IDs or null
                'departure_airport' => $departureAirport,
                'arrival_airport' => $arrivalAirport,
                'departure_date' => $departureDate->format('Y-m-d'),
                'departure_time' => $departureTime,
                'arrival_time' => $arrivalTime,
                'airline_code' => $airlines[array_rand($airlines)],
                'flight_number' => $airlines[array_rand($airlines)] . rand(100, 999),
                'passenger_count' => $passengerCount,
                'total_passengers' => $passengerCount,
                'total_amount' => $totalAmount,
                'currency' => 'PKR',
                'booking_status' => $bookingStatus,
                'status' => $status,
                'payment_status' => $paymentStatus,
                'payment_method' => $paymentMethods[array_rand($paymentMethods)],
                'payment_reference' => 'PAY' . strtoupper(uniqid()),
                'booking_date' => $departureDate->subDays(rand(1, 30)),
                'contact_email' => $user ? $user->email : 'customer' . $i . '@example.com',
                'contact_phone' => '+92' . rand(1000000000, 9999999999),
                'is_checked_in' => $departureDate->isPast() ? (bool)rand(0, 1) : false,
                'check_in_time' => $departureDate->isPast() && rand(0, 1) ?
                    $departureDate->subHours(rand(2, 24)) : null,
            ]);

            // Create passengers for this booking
            for ($j = 0; $j < $passengerCount; $j++) {
                $firstName = $firstNames[array_rand($firstNames)];
                $lastName = $lastNames[array_rand($lastNames)];
                $gender = $genders[array_rand($genders)];
                $title = $titles[array_rand($titles)];

                // Adjust title based on gender
                if ($gender === 'Male') {
                    $title = in_array($title, ['Mrs', 'Ms', 'Miss']) ? 'Mr' : $title;
                } else {
                    $title = $title === 'Mr' ? 'Ms' : $title;
                }

                $passengerType = $passengerTypes[array_rand($passengerTypes)];
                $type = strtolower($passengerType);

                Passenger::create([
                    'booking_id' => $booking->id,
                    'title' => $title,
                    'first_name' => $firstName,
                    'last_name' => $lastName,
                    'date_of_birth' => Carbon::now()->subYears(rand(18, 70))->subDays(rand(0, 365)),
                    'gender' => $gender,
                    'passport_number' => strtoupper(substr($lastName, 0, 2)) . rand(1000000, 9999999),
                    'nationality' => 'PK',
                    'seat_number' => $this->generateSeatNumber(),
                    'passenger_type' => $passengerType,
                    'type' => $type,
                    'is_frequent_flyer' => (bool)rand(0, 1),
                    'frequent_flyer_number' => rand(0, 1) ? 'FF' . rand(100000, 999999) : null,
                ]);
            }
        }
    }

    private function generateSeatNumber(): string
    {
        $rows = range(1, 50);
        $seats = ['A', 'B', 'C', 'D', 'E', 'F'];

        return $rows[array_rand($rows)] . $seats[array_rand($seats)];
    }
}
