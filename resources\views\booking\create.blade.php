@extends('layouts.app')

@section('title', 'Book Flight')

@section('content')
<div class="container">
    <!-- Flight Summary -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-plane me-2"></i>Flight Details</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="row align-items-center">
                                <div class="col-md-3 text-center">
                                    <img src="https://via.placeholder.com/60x40" alt="{{ $flight['airline_name'] }}" class="mb-2">
                                    <div class="small text-muted">{{ $flight['airline_name'] }}</div>
                                    <div class="small">{{ $flight['flight_number'] }}</div>
                                </div>
                                <div class="col-md-9">
                                    <div class="row align-items-center">
                                        <div class="col-4 text-center">
                                            <div class="h4 mb-0">{{ \Carbon\Carbon::parse($flight['departure_time'])->format('H:i') }}</div>
                                            <div class="text-muted">{{ $flight['departure_airport'] }}</div>
                                            <div class="small text-muted">{{ \Carbon\Carbon::parse($flight['departure_date'])->format('M d, Y') }}</div>
                                        </div>
                                        <div class="col-4 text-center">
                                            <div class="text-muted small">{{ $flight['duration'] }}</div>
                                            <div class="position-relative">
                                                <hr class="my-1">
                                                <i class="fas fa-plane position-absolute top-50 start-50 translate-middle bg-white px-2 text-primary"></i>
                                            </div>
                                            <div class="text-muted small">{{ $flight['stops'] == 0 ? 'Non-stop' : $flight['stops'] . ' stop(s)' }}</div>
                                        </div>
                                        <div class="col-4 text-center">
                                            <div class="h4 mb-0">{{ \Carbon\Carbon::parse($flight['arrival_time'])->format('H:i') }}</div>
                                            <div class="text-muted">{{ $flight['arrival_airport'] }}</div>
                                            <div class="small text-muted">{{ \Carbon\Carbon::parse($flight['departure_date'])->format('M d, Y') }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="h3 text-primary mb-1">PKR {{ number_format($flight['price']) }}</div>
                            <div class="text-muted">per person</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Booking Form -->
    <form action="{{ route('booking.store') }}" method="POST" id="booking-form">
        @csrf
        <input type="hidden" name="flight_id" value="{{ $flight['id'] }}">
        
        <div class="row">
            <div class="col-lg-8">
                <!-- Contact Information -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-user me-2"></i>Contact Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact_email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control @error('contact_email') is-invalid @enderror" 
                                           id="contact_email" name="contact_email" value="{{ old('contact_email') }}" required>
                                    @error('contact_email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact_phone" class="form-label">Phone Number *</label>
                                    <input type="tel" class="form-control @error('contact_phone') is-invalid @enderror" 
                                           id="contact_phone" name="contact_phone" value="{{ old('contact_phone') }}" required>
                                    @error('contact_phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Passenger Information -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-users me-2"></i>Passenger Information</h6>
                    </div>
                    <div class="card-body">
                        <div id="passengers-container">
                            @for($i = 0; $i < ($searchParams['passengers'] ?? 1); $i++)
                            <div class="passenger-form border rounded p-3 mb-3" data-passenger="{{ $i }}">
                                <h6 class="text-primary mb-3">Passenger {{ $i + 1 }}</h6>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="passengers_{{ $i }}_first_name" class="form-label">First Name *</label>
                                            <input type="text" class="form-control @error('passengers.'.$i.'.first_name') is-invalid @enderror" 
                                                   id="passengers_{{ $i }}_first_name" name="passengers[{{ $i }}][first_name]" 
                                                   value="{{ old('passengers.'.$i.'.first_name') }}" required>
                                            @error('passengers.'.$i.'.first_name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="passengers_{{ $i }}_last_name" class="form-label">Last Name *</label>
                                            <input type="text" class="form-control @error('passengers.'.$i.'.last_name') is-invalid @enderror" 
                                                   id="passengers_{{ $i }}_last_name" name="passengers[{{ $i }}][last_name]" 
                                                   value="{{ old('passengers.'.$i.'.last_name') }}" required>
                                            @error('passengers.'.$i.'.last_name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="passengers_{{ $i }}_date_of_birth" class="form-label">Date of Birth *</label>
                                            <input type="date" class="form-control @error('passengers.'.$i.'.date_of_birth') is-invalid @enderror" 
                                                   id="passengers_{{ $i }}_date_of_birth" name="passengers[{{ $i }}][date_of_birth]" 
                                                   value="{{ old('passengers.'.$i.'.date_of_birth') }}" required>
                                            @error('passengers.'.$i.'.date_of_birth')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="passengers_{{ $i }}_nationality" class="form-label">Nationality *</label>
                                            <select class="form-select @error('passengers.'.$i.'.nationality') is-invalid @enderror" 
                                                    id="passengers_{{ $i }}_nationality" name="passengers[{{ $i }}][nationality]" required>
                                                <option value="">Select Nationality</option>
                                                <option value="Pakistani" {{ old('passengers.'.$i.'.nationality') == 'Pakistani' ? 'selected' : '' }}>Pakistani</option>
                                                <option value="Indian" {{ old('passengers.'.$i.'.nationality') == 'Indian' ? 'selected' : '' }}>Indian</option>
                                                <option value="British" {{ old('passengers.'.$i.'.nationality') == 'British' ? 'selected' : '' }}>British</option>
                                                <option value="American" {{ old('passengers.'.$i.'.nationality') == 'American' ? 'selected' : '' }}>American</option>
                                                <option value="Canadian" {{ old('passengers.'.$i.'.nationality') == 'Canadian' ? 'selected' : '' }}>Canadian</option>
                                            </select>
                                            @error('passengers.'.$i.'.nationality')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="passengers_{{ $i }}_type" class="form-label">Passenger Type *</label>
                                            <select class="form-select @error('passengers.'.$i.'.type') is-invalid @enderror" 
                                                    id="passengers_{{ $i }}_type" name="passengers[{{ $i }}][type]" required>
                                                <option value="adult" {{ old('passengers.'.$i.'.type') == 'adult' ? 'selected' : '' }}>Adult</option>
                                                <option value="child" {{ old('passengers.'.$i.'.type') == 'child' ? 'selected' : '' }}>Child</option>
                                                <option value="infant" {{ old('passengers.'.$i.'.type') == 'infant' ? 'selected' : '' }}>Infant</option>
                                            </select>
                                            @error('passengers.'.$i.'.type')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="passengers_{{ $i }}_passport_number" class="form-label">Passport Number</label>
                                            <input type="text" class="form-control @error('passengers.'.$i.'.passport_number') is-invalid @enderror" 
                                                   id="passengers_{{ $i }}_passport_number" name="passengers[{{ $i }}][passport_number]" 
                                                   value="{{ old('passengers.'.$i.'.passport_number') }}">
                                            @error('passengers.'.$i.'.passport_number')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endfor
                        </div>
                    </div>
                </div>
            </div>

            <!-- Booking Summary -->
            <div class="col-lg-4">
                <div class="card border-0 shadow-sm position-sticky" style="top: 20px;">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-receipt me-2"></i>Booking Summary</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Base Fare ({{ $searchParams['passengers'] ?? 1 }} {{ Str::plural('passenger', $searchParams['passengers'] ?? 1) }})</span>
                            <span>PKR {{ number_format($flight['price'] * ($searchParams['passengers'] ?? 1)) }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Taxes & Fees</span>
                            <span>PKR {{ number_format(($flight['price'] * ($searchParams['passengers'] ?? 1)) * 0.15) }}</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between h5">
                            <strong>Total Amount</strong>
                            <strong class="text-primary">PKR {{ number_format(($flight['price'] * ($searchParams['passengers'] ?? 1)) * 1.15) }}</strong>
                        </div>
                        
                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary w-100 btn-lg">
                                <i class="fas fa-credit-card me-2"></i>
                                Proceed to Payment
                            </button>
                        </div>
                        
                        <div class="mt-3 text-center">
                            <small class="text-muted">
                                <i class="fas fa-shield-alt me-1"></i>
                                Your booking is protected by our secure payment system
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

@push('scripts')
<script>
document.getElementById('booking-form').addEventListener('submit', function(e) {
    // Add any client-side validation here
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
});

// Auto-calculate passenger type based on age
document.querySelectorAll('input[type="date"][name*="date_of_birth"]').forEach(input => {
    input.addEventListener('change', function() {
        const passengerIndex = this.name.match(/\[(\d+)\]/)[1];
        const typeSelect = document.querySelector(`select[name="passengers[${passengerIndex}][type]"]`);

        if (this.value) {
            const birthDate = new Date(this.value);
            const today = new Date();
            const age = Math.floor((today - birthDate) / (365.25 * 24 * 60 * 60 * 1000));

            if (age < 2) {
                typeSelect.value = 'infant';
            } else if (age < 12) {
                typeSelect.value = 'child';
            } else {
                typeSelect.value = 'adult';
            }
        }
    });
});
</script>
@endpush
@endsection
