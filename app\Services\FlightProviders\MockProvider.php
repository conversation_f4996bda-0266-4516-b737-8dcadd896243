<?php

namespace App\Services\FlightProviders;

use Carbon\Carbon;
use Exception;

class MockProvider implements FlightProviderInterface
{
    protected array $config;
    protected array $airlines;
    protected array $airports;

    public function __construct(array $config)
    {
        $this->config = $config;
        $this->initializeData();
    }

    protected function initializeData(): void
    {
        $this->airlines = [
            'PK' => ['name' => 'Fly Jinnah', 'logo' => 'https://via.placeholder.com/40x30/1e40af/ffffff?text=PK', 'color' => '#1e40af'],
            'PA' => ['name' => 'Serene Airline', 'logo' => 'https://via.placeholder.com/40x30/059669/ffffff?text=PA', 'color' => '#059669'],
            'G9' => ['name' => 'Air Arabia', 'logo' => 'https://via.placeholder.com/40x30/dc2626/ffffff?text=G9', 'color' => '#dc2626'],
            'EK' => ['name' => 'Emirates', 'logo' => 'https://via.placeholder.com/40x30/b91c1c/ffffff?text=EK', 'color' => '#b91c1c'],
            'QR' => ['name' => 'Qatar Airways', 'logo' => 'https://via.placeholder.com/40x30/7c2d12/ffffff?text=QR', 'color' => '#7c2d12'],
            'TK' => ['name' => 'Turkish Airlines', 'logo' => 'https://via.placeholder.com/40x30/991b1b/ffffff?text=TK', 'color' => '#991b1b'],
            'SV' => ['name' => 'Saudi Arabian Airlines', 'logo' => 'https://via.placeholder.com/40x30/166534/ffffff?text=SV', 'color' => '#166534'],
            'GF' => ['name' => 'Gulf Air', 'logo' => 'https://via.placeholder.com/40x30/1d4ed8/ffffff?text=GF', 'color' => '#1d4ed8'],
        ];

        $this->airports = [
            'ISB' => ['name' => 'Islamabad International Airport', 'city' => 'Islamabad', 'country' => 'Pakistan'],
            'LHE' => ['name' => 'Allama Iqbal International Airport', 'city' => 'Lahore', 'country' => 'Pakistan'],
            'KHI' => ['name' => 'Jinnah International Airport', 'city' => 'Karachi', 'country' => 'Pakistan'],
            'PEW' => ['name' => 'Peshawar International Airport', 'city' => 'Peshawar', 'country' => 'Pakistan'],
            'MUX' => ['name' => 'Multan International Airport', 'city' => 'Multan', 'country' => 'Pakistan'],
            'BAH' => ['name' => 'Bahrain International Airport', 'city' => 'Manama', 'country' => 'Bahrain'],
            'DXB' => ['name' => 'Dubai International Airport', 'city' => 'Dubai', 'country' => 'UAE'],
            'SHJ' => ['name' => 'Sharjah International Airport', 'city' => 'Sharjah', 'country' => 'UAE'],
            'AUH' => ['name' => 'Abu Dhabi International Airport', 'city' => 'Abu Dhabi', 'country' => 'UAE'],
            'DOH' => ['name' => 'Hamad International Airport', 'city' => 'Doha', 'country' => 'Qatar'],
            'KWI' => ['name' => 'Kuwait International Airport', 'city' => 'Kuwait City', 'country' => 'Kuwait'],
            'RUH' => ['name' => 'King Khalid International Airport', 'city' => 'Riyadh', 'country' => 'Saudi Arabia'],
            'JED' => ['name' => 'King Abdulaziz International Airport', 'city' => 'Jeddah', 'country' => 'Saudi Arabia'],
            'DMM' => ['name' => 'King Fahd International Airport', 'city' => 'Dammam', 'country' => 'Saudi Arabia'],
            'MCT' => ['name' => 'Muscat International Airport', 'city' => 'Muscat', 'country' => 'Oman'],
            'IST' => ['name' => 'Istanbul Airport', 'city' => 'Istanbul', 'country' => 'Turkey'],
        ];
    }

    public function isEnabled(): bool
    {
        return $this->config['enabled'] ?? true;
    }

    public function searchFlights(array $params): array
    {
        // Simulate API delay
        usleep(rand(500000, 1500000)); // 0.5-1.5 seconds

        $departureAirport = $params['departure_airport'] ?? 'JFK';
        $arrivalAirport = $params['arrival_airport'] ?? 'LAX';
        $departureDate = $params['departure_date'] ?? date('Y-m-d');
        $adults = $params['adults'] ?? 1;
        $cabinClass = $params['cabin_class'] ?? 'economy';

        $flights = [];
        $numFlights = rand(5, 15);

        for ($i = 0; $i < $numFlights; $i++) {
            $airline = array_rand($this->airlines);
            $flightNumber = $airline . rand(100, 9999);
            
            // Generate realistic flight times
            $departureTime = $this->generateDepartureTime();
            $duration = $this->calculateFlightDuration($departureAirport, $arrivalAirport);
            $arrivalTime = Carbon::parse($departureDate . ' ' . $departureTime)->addMinutes($duration);
            
            $stops = rand(0, 2);
            $price = $this->calculatePrice($departureAirport, $arrivalAirport, $cabinClass, $stops);

            $flights[] = [
                'id' => 'mock_' . uniqid(),
                'flight_number' => $flightNumber,
                'airline' => $this->airlines[$airline]['name'],
                'airline_name' => $this->airlines[$airline]['name'],
                'airline_code' => $airline,
                'airline_logo' => $this->airlines[$airline]['logo'],
                'airline_color' => $this->airlines[$airline]['color'],
                'departure_airport' => $departureAirport,
                'arrival_airport' => $arrivalAirport,
                'departure_time' => $departureTime,
                'arrival_time' => $arrivalTime->format('H:i'),
                'departure_date' => $departureDate,
                'arrival_date' => $arrivalTime->format('Y-m-d'),
                'duration' => $this->formatDuration($duration),
                'duration_minutes' => $duration,
                'stops' => $stops,
                'price' => $price,
                'currency' => 'PKR',
                'cabin_class' => $cabinClass,
                'available_seats' => rand(1, 50),
                'provider' => 'mock_provider',
                'booking_url' => '#',
                'baggage_allowance' => $this->getBaggageAllowance($cabinClass),
                'amenities' => $this->getAmenities($cabinClass),
                'aircraft_type' => $this->getRandomAircraft(),
                'meal' => rand(0, 1) ? 'Yes' : 'No',
                'wifi' => rand(0, 1) ? 'Yes' : 'No',
            ];
        }

        // Sort by price
        usort($flights, function($a, $b) {
            return $a['price'] <=> $b['price'];
        });

        return array_slice($flights, 0, $this->config['max_results'] ?? 20);
    }

    public function searchAirports(string $query): array
    {
        $results = [];
        $query = strtolower($query);

        foreach ($this->airports as $code => $airport) {
            if (
                strpos(strtolower($code), $query) !== false ||
                strpos(strtolower($airport['name']), $query) !== false ||
                strpos(strtolower($airport['city']), $query) !== false
            ) {
                $results[] = [
                    'code' => $code,
                    'name' => $airport['name'],
                    'city' => $airport['city'],
                    'country' => $airport['country'],
                    'provider' => 'mock_provider'
                ];
            }
        }

        return array_slice($results, 0, 10);
    }

    public function getFlightDetails(string $flightId): ?array
    {
        // Mock flight details
        return [
            'id' => $flightId,
            'status' => 'available',
            'price_breakdown' => [
                'base_fare' => 299.00,
                'taxes' => 45.50,
                'fees' => 25.50,
                'total' => 370.00
            ],
            'booking_conditions' => [
                'refundable' => false,
                'changeable' => true,
                'change_fee' => 50.00
            ]
        ];
    }

    protected function generateDepartureTime(): string
    {
        $hours = [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22];
        $minutes = [0, 15, 30, 45];
        
        $hour = $hours[array_rand($hours)];
        $minute = $minutes[array_rand($minutes)];
        
        return sprintf('%02d:%02d', $hour, $minute);
    }

    protected function calculateFlightDuration(string $from, string $to): int
    {
        // Mock duration calculation based on common routes
        $durations = [
            'JFK-LAX' => 360, 'LAX-JFK' => 330,
            'JFK-LHR' => 420, 'LHR-JFK' => 480,
            'LAX-NRT' => 660, 'NRT-LAX' => 600,
            'LHR-CDG' => 75, 'CDG-LHR' => 75,
            'DXB-BOM' => 180, 'BOM-DXB' => 180,
        ];

        $route = $from . '-' . $to;
        return $durations[$route] ?? rand(120, 720);
    }

    protected function calculatePrice(string $from, string $to, string $class, int $stops): float
    {
        // Base price in PKR for Pakistani routes
        $basePrice = rand(65000, 120000);

        // Class multipliers
        $classMultipliers = [
            'economy' => 1.0,
            'premium_economy' => 1.5,
            'business' => 3.0,
            'first' => 5.0
        ];

        $price = $basePrice * ($classMultipliers[$class] ?? 1.0);

        // Stops discount
        $price *= (1 - ($stops * 0.15));

        return round($price, 0); // Round to nearest PKR
    }

    protected function formatDuration(int $minutes): string
    {
        $hours = floor($minutes / 60);
        $mins = $minutes % 60;
        return sprintf('%dh %02dm', $hours, $mins);
    }

    protected function getBaggageAllowance(string $class): array
    {
        $allowances = [
            'economy' => ['carry_on' => '7kg', 'checked' => '23kg'],
            'premium_economy' => ['carry_on' => '10kg', 'checked' => '32kg'],
            'business' => ['carry_on' => '12kg', 'checked' => '32kg x2'],
            'first' => ['carry_on' => '15kg', 'checked' => '32kg x3']
        ];

        return $allowances[$class] ?? $allowances['economy'];
    }

    protected function getAmenities(string $class): array
    {
        $amenities = [
            'economy' => ['In-flight entertainment', 'Meal service'],
            'premium_economy' => ['Priority boarding', 'Extra legroom', 'Premium meals', 'In-flight entertainment'],
            'business' => ['Lie-flat seats', 'Priority check-in', 'Lounge access', 'Premium dining', 'Wi-Fi'],
            'first' => ['Private suites', 'Concierge service', 'Fine dining', 'Spa services', 'Chauffeur service']
        ];

        return $amenities[$class] ?? $amenities['economy'];
    }

    protected function getRandomAircraft(): string
    {
        $aircraft = ['Boeing 737', 'Boeing 777', 'Boeing 787', 'Airbus A320', 'Airbus A330', 'Airbus A350', 'Airbus A380'];
        return $aircraft[array_rand($aircraft)];
    }

    /**
     * Get real-time flight prices
     */
    public function getFlightPrices(array $flightIds): array
    {
        $prices = [];
        foreach ($flightIds as $flightId) {
            $prices[$flightId] = [
                'price' => rand(200, 1500),
                'currency' => 'USD',
                'last_updated' => now()->toISOString()
            ];
        }
        return $prices;
    }

    /**
     * Book a flight
     */
    public function bookFlight(array $bookingData): array
    {
        return [
            'success' => true,
            'booking_reference' => 'MOCK' . strtoupper(uniqid()),
            'status' => 'confirmed',
            'total_price' => $bookingData['total_price'] ?? 500.00,
            'currency' => 'USD',
            'booking_date' => now()->toISOString(),
            'message' => 'Mock booking created successfully'
        ];
    }

    /**
     * Get airline information
     */
    public function getAirlineInfo(string $airlineCode): ?array
    {
        if (isset($this->airlines[$airlineCode])) {
            return [
                'code' => $airlineCode,
                'name' => $this->airlines[$airlineCode]['name'],
                'logo' => $this->airlines[$airlineCode]['logo'],
                'country' => 'Various',
                'fleet_size' => rand(50, 500),
                'founded' => rand(1950, 2000)
            ];
        }
        return null;
    }

    /**
     * Check flight availability
     */
    public function checkAvailability(string $flightId): bool
    {
        // Mock availability - randomly return true/false
        return rand(0, 10) > 2; // 80% chance of availability
    }

    /**
     * Get booking status
     */
    public function getBookingStatus(string $bookingReference): ?array
    {
        if (strpos($bookingReference, 'MOCK') === 0) {
            return [
                'booking_reference' => $bookingReference,
                'status' => 'confirmed',
                'passenger_name' => 'John Doe',
                'flight_number' => 'AA' . rand(100, 9999),
                'departure_date' => now()->addDays(rand(1, 30))->format('Y-m-d'),
                'departure_time' => $this->generateDepartureTime(),
                'seat' => rand(1, 30) . ['A', 'B', 'C', 'D', 'E', 'F'][rand(0, 5)]
            ];
        }
        return null;
    }
}
