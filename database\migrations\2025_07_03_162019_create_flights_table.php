<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('flights', function (Blueprint $table) {
            $table->id();
            $table->string('flight_number'); // e.g., 'BA123'
            $table->foreignId('airline_id')->constrained()->onDelete('cascade');
            $table->foreignId('departure_airport_id')->constrained('airports')->onDelete('cascade');
            $table->foreignId('arrival_airport_id')->constrained('airports')->onDelete('cascade');
            $table->dateTime('departure_time');
            $table->dateTime('arrival_time');
            $table->integer('duration_minutes'); // Flight duration in minutes
            $table->enum('aircraft_type', ['Boeing 737', 'Boeing 747', 'Boeing 777', 'Boeing 787', 'Airbus A320', 'Airbus A330', 'Airbus A350', 'Airbus A380']);
            $table->integer('total_seats');
            $table->integer('available_seats');
            $table->decimal('base_price', 10, 2); // Base ticket price
            $table->enum('flight_type', ['Domestic', 'International']);
            $table->enum('class_type', ['Economy', 'Business', 'First']);
            $table->enum('status', ['Scheduled', 'Delayed', 'Cancelled', 'Completed'])->default('Scheduled');
            $table->string('gate')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            // Indexes for better performance
            $table->index(['departure_time', 'arrival_time']);
            $table->index(['departure_airport_id', 'arrival_airport_id']);
            $table->index('flight_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('flights');
    }
};
