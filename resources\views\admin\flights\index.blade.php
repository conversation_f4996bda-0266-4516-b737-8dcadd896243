@extends('layouts.admin')

@section('page-title', 'Flight Management')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="page-title">Flight Management</h1>
            <p class="page-subtitle">Manage all flights, schedules, and availability</p>
        </div>
        <a href="{{ route('admin.flights.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Add New Flight
        </a>
    </div>

    <!-- Filters Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters & Search</h5>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.flights.index') }}" class="row g-3" id="filterForm">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ request('search') }}" placeholder="Flight number, airport...">
                </div>
                
                <div class="col-md-2">
                    <label for="flight_type" class="form-label">Flight Type</label>
                    <select class="form-select" id="flight_type" name="flight_type">
                        @foreach($flightTypes as $type)
                            <option value="{{ $type }}" {{ request('flight_type') == $type ? 'selected' : '' }}>
                                {{ $type }}
                            </option>
                        @endforeach
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        @foreach($statuses as $status)
                            <option value="{{ $status }}" {{ request('status') == $status ? 'selected' : '' }}>
                                {{ $status }}
                            </option>
                        @endforeach
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="airline_id" class="form-label">Airline</label>
                    <select class="form-select" id="airline_id" name="airline_id">
                        <option value="">All Airlines</option>
                        @foreach($airlines as $airline)
                            <option value="{{ $airline->id }}" {{ request('airline_id') == $airline->id ? 'selected' : '' }}>
                                {{ $airline->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="date_from" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="date_from" name="date_from"
                           value="{{ request('date_from') }}">
                </div>

                <div class="col-md-2">
                    <label for="date_to" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="date_to" name="date_to"
                           value="{{ request('date_to') }}">
                </div>

                <div class="col-md-12">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Search
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="clearFilters">
                            <i class="fas fa-times me-2"></i>Clear
                        </button>
                        <button type="button" class="btn btn-outline-success" id="exportFlights">
                            <i class="fas fa-download me-2"></i>Export CSV
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Flights Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Flights ({{ $flights->total() }} total)</h5>
            <div class="d-flex gap-2">
                <span class="badge bg-primary">{{ $flights->where('status', 'Scheduled')->count() }} Scheduled</span>
                <span class="badge bg-warning">{{ $flights->where('status', 'Delayed')->count() }} Delayed</span>
                <span class="badge bg-danger">{{ $flights->where('status', 'Cancelled')->count() }} Cancelled</span>
                <span class="badge bg-success">{{ $flights->where('status', 'Completed')->count() }} Completed</span>
            </div>
        </div>
        <div class="card-body p-0">
            @if($flights->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Flight</th>
                                <th>Route</th>
                                <th>Departure</th>
                                <th>Arrival</th>
                                <th>Aircraft</th>
                                <th>Seats</th>
                                <th>Price</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($flights as $flight)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <div class="fw-bold">{{ $flight->flight_number }}</div>
                                                <small class="text-muted">{{ $flight->airline->name }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <span class="fw-bold">{{ $flight->departureAirport->code }}</span>
                                            <i class="fas fa-arrow-right mx-2 text-muted"></i>
                                            <span class="fw-bold">{{ $flight->arrivalAirport->code }}</span>
                                        </div>
                                        <small class="text-muted">
                                            {{ $flight->departureAirport->city }} → {{ $flight->arrivalAirport->city }}
                                        </small>
                                    </td>
                                    <td>
                                        <div>{{ $flight->departure_time->format('M d, Y') }}</div>
                                        <small class="text-muted">{{ $flight->departure_time->format('H:i') }}</small>
                                    </td>
                                    <td>
                                        <div>{{ $flight->arrival_time->format('M d, Y') }}</div>
                                        <small class="text-muted">{{ $flight->arrival_time->format('H:i') }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">{{ $flight->aircraft_type }}</span>
                                    </td>
                                    <td>
                                        <div>{{ $flight->available_seats }}/{{ $flight->total_seats }}</div>
                                        <small class="text-muted">Available</small>
                                    </td>
                                    <td>
                                        <span class="fw-bold">${{ number_format($flight->base_price, 2) }}</span>
                                    </td>
                                    <td>
                                        @php
                                            $statusClass = match($flight->status) {
                                                'Scheduled' => 'bg-primary',
                                                'Delayed' => 'bg-warning',
                                                'Cancelled' => 'bg-danger',
                                                'Completed' => 'bg-success',
                                                default => 'bg-secondary'
                                            };
                                        @endphp
                                        <span class="badge {{ $statusClass }}">{{ $flight->status }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.flights.show', $flight) }}" 
                                               class="btn btn-sm btn-outline-primary" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.flights.edit', $flight) }}" 
                                               class="btn btn-sm btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="{{ route('admin.flights.destroy', $flight) }}" 
                                                  method="POST" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                        title="Delete" onclick="return confirm('Are you sure?')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center p-3">
                    <div class="text-muted">
                        Showing {{ $flights->firstItem() }} to {{ $flights->lastItem() }} of {{ $flights->total() }} results
                    </div>
                    {{ $flights->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-plane fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No flights found</h5>
                    <p class="text-muted">Try adjusting your search criteria or add a new flight.</p>
                    <a href="{{ route('admin.flights.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add First Flight
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

@if(session('success'))
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div class="toast show" role="alert">
            <div class="toast-header">
                <i class="fas fa-check-circle text-success me-2"></i>
                <strong class="me-auto">Success</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                {{ session('success') }}
            </div>
        </div>
    </div>
@endif

@if(session('error'))
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div class="toast show" role="alert">
            <div class="toast-header">
                <i class="fas fa-exclamation-circle text-danger me-2"></i>
                <strong class="me-auto">Error</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                {{ session('error') }}
            </div>
        </div>
    </div>
@endif
@endsection

@push('scripts')
<script>
    // Auto-hide toasts after 5 seconds
    setTimeout(function() {
        $('.toast').toast('hide');
    }, 5000);

    // Real-time search functionality
    let searchTimeout;
    $('#search').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            $('#filterForm').submit();
        }, 500);
    });

    // Auto-submit form when filters change
    $('.form-select').on('change', function() {
        $('#filterForm').submit();
    });

    // Clear all filters
    $('#clearFilters').on('click', function(e) {
        e.preventDefault();
        $('#filterForm')[0].reset();
        window.location.href = '{{ route("admin.flights.index") }}';
    });

    // Export functionality
    $('#exportFlights').on('click', function() {
        const params = new URLSearchParams(window.location.search);
        params.set('export', 'csv');
        window.location.href = '{{ route("admin.flights.index") }}?' + params.toString();
    });
</script>
@endpush
