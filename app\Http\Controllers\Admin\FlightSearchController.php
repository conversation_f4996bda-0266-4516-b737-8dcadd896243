<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\FlightApiService;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class FlightSearchController extends Controller
{
    protected FlightApiService $flightApiService;

    public function __construct(FlightApiService $flightApiService)
    {
        $this->flightApiService = $flightApiService;
    }

    /**
     * Display the flight search management dashboard
     */
    public function index(): View
    {
        // Get search statistics
        $searchStats = $this->getSearchStatistics();
        
        // Get API providers status
        $providers = $this->getProvidersStatus();
        
        // Get recent searches
        $recentSearches = $this->getRecentSearches();
        
        // Get API status
        $apiStatus = $this->getApiStatus();

        return view('admin.flight-search.index', compact(
            'searchStats',
            'providers', 
            'recentSearches',
            'apiStatus'
        ));
    }

    /**
     * Get search statistics
     */
    protected function getSearchStatistics(): array
    {
        // In a real implementation, you would query your search logs table
        // For now, we'll return mock data
        return [
            'today' => Cache::get('flight_searches_today', 0),
            'week' => Cache::get('flight_searches_week', 0),
            'month' => Cache::get('flight_searches_month', 0),
            'avg_response_time' => Cache::get('avg_response_time', 1200),
        ];
    }

    /**
     * Get providers status
     */
    protected function getProvidersStatus(): array
    {
        $providers = [];
        $availableProviders = $this->flightApiService->getAvailableProviders();

        foreach ($availableProviders as $providerName => $provider) {
            $cacheKey = "provider_status_{$providerName}";
            $status = Cache::get($cacheKey, [
                'name' => $providerName,
                'status' => 'unknown',
                'last_check' => 'Never',
                'response_time' => 'N/A',
                'success_rate' => 'N/A',
                'description' => $this->getProviderDescription($providerName)
            ]);

            $providers[] = $status;
        }

        return $providers;
    }

    /**
     * Get provider description
     */
    protected function getProviderDescription(string $providerName): string
    {
        return match ($providerName) {
            'amadeus' => 'Amadeus Travel API - Global flight data',
            'rapidapi_skyscanner' => 'Skyscanner via RapidAPI - Popular flight search',
            'travelpayouts' => 'TravelPayouts API - Budget flight options',
            default => 'Flight data provider'
        };
    }

    /**
     * Get recent searches
     */
    protected function getRecentSearches(): array
    {
        // In a real implementation, you would query your search logs table
        // For now, we'll return mock data
        return Cache::get('recent_flight_searches', [
            [
                'created_at' => now()->subMinutes(5)->format('Y-m-d H:i:s'),
                'route' => 'JFK → LAX',
                'passengers' => '2 Adults',
                'results_count' => 15,
                'response_time' => '1.2s',
                'status' => 'success'
            ],
            [
                'created_at' => now()->subMinutes(12)->format('Y-m-d H:i:s'),
                'route' => 'LHR → CDG',
                'passengers' => '1 Adult, 1 Child',
                'results_count' => 8,
                'response_time' => '0.9s',
                'status' => 'success'
            ],
            [
                'created_at' => now()->subMinutes(18)->format('Y-m-d H:i:s'),
                'route' => 'DXB → BOM',
                'passengers' => '3 Adults',
                'results_count' => 0,
                'response_time' => '2.1s',
                'status' => 'failed'
            ]
        ]);
    }

    /**
     * Get API status
     */
    protected function getApiStatus(): array
    {
        $providers = $this->flightApiService->getAvailableProviders();
        $activeProviders = 0;

        foreach ($providers as $provider) {
            if ($provider->isEnabled()) {
                $activeProviders++;
            }
        }

        return [
            'active_providers' => $activeProviders,
            'total_providers' => count($providers),
            'last_health_check' => Cache::get('last_api_health_check', 'Never')
        ];
    }

    /**
     * Test API connection
     */
    public function testConnection(Request $request)
    {
        try {
            $providers = $this->flightApiService->getAvailableProviders();
            $results = [];

            foreach ($providers as $providerName => $provider) {
                try {
                    $startTime = microtime(true);
                    
                    // Test with a simple airport search
                    $airports = $this->flightApiService->searchAirports('New York');
                    
                    $responseTime = round((microtime(true) - $startTime) * 1000);
                    
                    $results[$providerName] = [
                        'status' => 'success',
                        'response_time' => $responseTime . 'ms',
                        'airports_found' => count($airports)
                    ];

                    // Cache the status
                    Cache::put("provider_status_{$providerName}", [
                        'name' => $providerName,
                        'status' => 'active',
                        'last_check' => now()->format('Y-m-d H:i:s'),
                        'response_time' => $responseTime . 'ms',
                        'success_rate' => '95%',
                        'description' => $this->getProviderDescription($providerName)
                    ], 3600);

                } catch (\Exception $e) {
                    $results[$providerName] = [
                        'status' => 'error',
                        'error' => $e->getMessage()
                    ];

                    // Cache the error status
                    Cache::put("provider_status_{$providerName}", [
                        'name' => $providerName,
                        'status' => 'error',
                        'last_check' => now()->format('Y-m-d H:i:s'),
                        'response_time' => 'Error',
                        'success_rate' => '0%',
                        'description' => $this->getProviderDescription($providerName)
                    ], 3600);
                }
            }

            Cache::put('last_api_health_check', now()->format('Y-m-d H:i:s'), 3600);

            return response()->json([
                'success' => true,
                'message' => 'API connection test completed',
                'results' => $results
            ]);

        } catch (\Exception $e) {
            Log::error('API connection test failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'API connection test failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get search analytics
     */
    public function analytics(Request $request)
    {
        $period = $request->get('period', '7days');
        
        // In a real implementation, you would query your analytics data
        // For now, we'll return mock data
        $analytics = [
            'searches_by_day' => [
                ['date' => now()->subDays(6)->format('Y-m-d'), 'count' => 45],
                ['date' => now()->subDays(5)->format('Y-m-d'), 'count' => 52],
                ['date' => now()->subDays(4)->format('Y-m-d'), 'count' => 38],
                ['date' => now()->subDays(3)->format('Y-m-d'), 'count' => 61],
                ['date' => now()->subDays(2)->format('Y-m-d'), 'count' => 47],
                ['date' => now()->subDays(1)->format('Y-m-d'), 'count' => 55],
                ['date' => now()->format('Y-m-d'), 'count' => 23],
            ],
            'popular_routes' => [
                ['route' => 'JFK → LAX', 'count' => 89],
                ['route' => 'LHR → CDG', 'count' => 67],
                ['route' => 'DXB → BOM', 'count' => 45],
                ['route' => 'SYD → MEL', 'count' => 34],
                ['route' => 'NRT → ICN', 'count' => 28],
            ],
            'success_rate' => 94.5,
            'avg_response_time' => 1.2
        ];

        return response()->json([
            'success' => true,
            'data' => $analytics
        ]);
    }

    /**
     * Clear search cache
     */
    public function clearCache(Request $request)
    {
        try {
            // Clear flight search related cache
            Cache::forget('flight_searches_today');
            Cache::forget('flight_searches_week');
            Cache::forget('flight_searches_month');
            Cache::forget('avg_response_time');
            Cache::forget('recent_flight_searches');
            Cache::forget('last_api_health_check');

            // Clear provider status cache
            $providers = $this->flightApiService->getAvailableProviders();
            foreach ($providers as $providerName => $provider) {
                Cache::forget("provider_status_{$providerName}");
            }

            return response()->json([
                'success' => true,
                'message' => 'Cache cleared successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear cache: ' . $e->getMessage()
            ], 500);
        }
    }
}
