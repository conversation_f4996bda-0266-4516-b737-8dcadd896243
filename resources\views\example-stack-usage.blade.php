{{-- Example of how to use @push with the new @stack system --}}

@extends('layouts.frontend')

{{-- Push custom title --}}
@push('title')
    Custom Page Title - 
@endpush

{{-- Push custom description --}}
@push('description')
    This is a custom meta description for this specific page. 
@endpush

{{-- Push custom body class --}}
@push('body-class')
    custom-page-class another-class
@endpush

{{-- Push custom styles --}}
@push('styles')
<style>
    .custom-page-class {
        background-color: #f8f9fa;
    }
    .custom-content {
        padding: 2rem;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
</style>
@endpush

{{-- Push main content --}}
@push('content')
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="custom-content">
                <h1>Example Page Using @stack System</h1>
                <p>This page demonstrates how to use the new @push/@stack system instead of @section/@yield.</p>
                
                <h3>Benefits of @stack over @yield:</h3>
                <ul>
                    <li><strong>Multiple pushes:</strong> You can push content from multiple places to the same stack</li>
                    <li><strong>Conditional content:</strong> Easy to conditionally add content</li>
                    <li><strong>Component-friendly:</strong> Works well with Blade components</li>
                    <li><strong>Flexible ordering:</strong> Content is stacked in the order it's pushed</li>
                </ul>

                <h3>How it works:</h3>
                <div class="row">
                    <div class="col-md-6">
                        <h5>In your layout (frontend.blade.php):</h5>
                        <pre><code>&lt;title&gt;@stack('title'){{ config('app.name') }}&lt;/title&gt;
&lt;meta name="description" content="@stack('description')Default description"&gt;
&lt;body class="@stack('body-class')"&gt;
@stack('content')</code></pre>
                    </div>
                    <div class="col-md-6">
                        <h5>In your view files:</h5>
                        <pre><code>@push('title')
    Custom Page Title - 
@endpush

@push('description')
    Custom meta description
@endpush

@push('content')
    &lt;div&gt;Your page content&lt;/div&gt;
@endpush</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endpush

{{-- Push custom scripts --}}
@push('scripts')
<script>
    console.log('Custom script loaded for this page');
    
    // Example of page-specific JavaScript
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Page loaded with @stack system');
    });
</script>
@endpush
