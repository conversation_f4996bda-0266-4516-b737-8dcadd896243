@extends('layouts.frontend')

@section('title', 'Flight Search Results - {{ config('app.name', 'FlyNow Airlines') }}')

@section('content')
<div class="container-fluid" style="background-color: #f8f9fa; min-height: 100vh;">
    <!-- Header with Search Summary -->
    <div class="bg-primary text-white py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h4 class="mb-2">
                        <i class="fas fa-plane-departure me-2"></i>
                        {{ $departureAirport['city'] ?? 'Departure' }} ({{ $departureAirport['code'] ?? 'DEP' }})
                        <i class="fas fa-arrow-right mx-2"></i>
                        {{ $arrivalAirport['city'] ?? 'Arrival' }} ({{ $arrivalAirport['code'] ?? 'ARR' }})
                    </h4>
                    <div class="d-flex flex-wrap gap-3 text-white-50">
                        <span>
                            <i class="fas fa-calendar me-1"></i>
                            {{ isset($searchParams['departure_date']) ? \Carbon\Carbon::parse($searchParams['departure_date'])->format('M d, Y') : 'Date' }}
                            @if(!empty($searchParams['return_date']))
                                - {{ \Carbon\Carbon::parse($searchParams['return_date'])->format('M d, Y') }}
                            @endif
                        </span>
                        <span>
                            <i class="fas fa-users me-1"></i>
                            {{ $searchParams['passengers'] ?? 1 }} {{ Str::plural('Passenger', $searchParams['passengers'] ?? 1) }}
                        </span>
                        <span>
                            <i class="fas fa-chair me-1"></i>
                            {{ ucfirst(str_replace('_', ' ', $searchParams['cabin_class'] ?? 'economy')) }}
                        </span>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{{ route('book-tickets') }}" class="btn btn-light">
                        <i class="fas fa-edit me-1"></i>
                        Modify Search
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container py-4">

        <div class="row">
            <!-- Filters Sidebar -->
            <div class="col-lg-3">
                <!-- Filter Toggle for Mobile -->
                <div class="d-lg-none mb-3">
                    <button class="btn btn-outline-primary w-100" type="button" data-bs-toggle="collapse" data-bs-target="#filtersCollapse">
                        <i class="fas fa-filter me-2"></i>Filters & Sort
                    </button>
                </div>

                <div class="collapse d-lg-block" id="filtersCollapse">
                    <!-- Airlines Filter -->
                    <div class="card border-0 shadow-sm mb-3">
                        <div class="card-header bg-white border-bottom">
                            <h6 class="mb-0 fw-bold">Airlines</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check mb-2">
                                <input class="form-check-input airline-filter" type="checkbox" value="all" id="airline-all" checked>
                                <label class="form-check-label fw-bold" for="airline-all">All Airlines</label>
                            </div>
                            <hr class="my-2">
                            <div class="form-check mb-2">
                                <input class="form-check-input airline-filter" type="checkbox" value="PK" id="airline-pk">
                                <label class="form-check-label d-flex align-items-center" for="airline-pk">
                                    <div class="airline-logo me-2" style="width: 24px; height: 16px; background: #1e40af; border-radius: 2px; display: flex; align-items: center; justify-content: center; color: white; font-size: 8px; font-weight: bold;">PK</div>
                                    Fly Jinnah
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input airline-filter" type="checkbox" value="PA" id="airline-pa">
                                <label class="form-check-label d-flex align-items-center" for="airline-pa">
                                    <div class="airline-logo me-2" style="width: 24px; height: 16px; background: #059669; border-radius: 2px; display: flex; align-items: center; justify-content: center; color: white; font-size: 8px; font-weight: bold;">PA</div>
                                    Serene Airline
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input airline-filter" type="checkbox" value="G9" id="airline-g9">
                                <label class="form-check-label d-flex align-items-center" for="airline-g9">
                                    <div class="airline-logo me-2" style="width: 24px; height: 16px; background: #dc2626; border-radius: 2px; display: flex; align-items: center; justify-content: center; color: white; font-size: 8px; font-weight: bold;">G9</div>
                                    Air Arabia
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input airline-filter" type="checkbox" value="EK" id="airline-ek">
                                <label class="form-check-label d-flex align-items-center" for="airline-ek">
                                    <div class="airline-logo me-2" style="width: 24px; height: 16px; background: #b91c1c; border-radius: 2px; display: flex; align-items: center; justify-content: center; color: white; font-size: 8px; font-weight: bold;">EK</div>
                                    Emirates
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Price Range Filter -->
                    <div class="card border-0 shadow-sm mb-3">
                        <div class="card-header bg-white border-bottom">
                            <h6 class="mb-0 fw-bold">Price Range</h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-2 mb-3">
                                <div class="col-6">
                                    <input type="number" class="form-control form-control-sm" id="min-price" placeholder="Min PKR" value="0">
                                </div>
                                <div class="col-6">
                                    <input type="number" class="form-control form-control-sm" id="max-price" placeholder="Max PKR" value="200000">
                                </div>
                            </div>
                            <button class="btn btn-primary btn-sm w-100" onclick="applyPriceFilter()">
                                <i class="fas fa-filter me-1"></i>Apply Filter
                            </button>
                        </div>
                    </div>

                    <!-- Departure Time Filter -->
                    <div class="card border-0 shadow-sm mb-3">
                        <div class="card-header bg-white border-bottom">
                            <h6 class="mb-0 fw-bold">Departure Time</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check mb-2">
                                <input class="form-check-input time-filter" type="checkbox" value="morning" id="time-morning">
                                <label class="form-check-label" for="time-morning">
                                    <i class="fas fa-sun text-warning me-2"></i>Morning (06:00 - 12:00)
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input time-filter" type="checkbox" value="afternoon" id="time-afternoon">
                                <label class="form-check-label" for="time-afternoon">
                                    <i class="fas fa-sun text-orange me-2"></i>Afternoon (12:00 - 18:00)
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input time-filter" type="checkbox" value="evening" id="time-evening">
                                <label class="form-check-label" for="time-evening">
                                    <i class="fas fa-moon text-primary me-2"></i>Evening (18:00 - 24:00)
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Stops Filter -->
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white border-bottom">
                            <h6 class="mb-0 fw-bold">Stops</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check mb-2">
                                <input class="form-check-input stops-filter" type="checkbox" value="0" id="stops-0">
                                <label class="form-check-label" for="stops-0">Non-stop</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input stops-filter" type="checkbox" value="1" id="stops-1">
                                <label class="form-check-label" for="stops-1">1 Stop</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input stops-filter" type="checkbox" value="2" id="stops-2">
                                <label class="form-check-label" for="stops-2">2+ Stops</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Flight Results -->
            <div class="col-lg-9">
                <!-- Results Header -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <h5 class="mb-1">Available Flights</h5>
                        <p class="text-muted mb-0">{{ count($flights ?? []) }} flights found</p>
                    </div>
                    <div class="d-flex align-items-center gap-3">
                        <div class="d-flex align-items-center">
                            <label class="form-label me-2 mb-0 fw-bold">Sort by:</label>
                            <select class="form-select form-select-sm" style="width: auto;" onchange="sortFlights(this.value)">
                                <option value="price">Price (Low to High)</option>
                                <option value="price_desc">Price (High to Low)</option>
                                <option value="duration">Duration</option>
                                <option value="departure_time">Departure Time</option>
                                <option value="arrival_time">Arrival Time</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Flight Results List -->
                <div id="flight-results">
                    @forelse($flights ?? [] as $flight)
                    <div class="card border-0 shadow-sm mb-3 flight-card"
                         data-airline="{{ $flight['airline_code'] ?? 'Unknown' }}"
                         data-price="{{ $flight['price'] ?? 0 }}"
                         data-duration="{{ $flight['duration_minutes'] ?? 150 }}"
                         data-departure="{{ $flight['departure_time'] ?? '00:00' }}"
                         data-stops="{{ $flight['stops'] ?? 0 }}">
                        <div class="card-body p-4">
                            <div class="row align-items-center">
                                <!-- Airline Info -->
                                <div class="col-lg-2 col-md-3 text-center mb-3 mb-md-0">
                                    <div class="airline-logo mx-auto mb-2" style="width: 48px; height: 32px; background: {{ $flight['airline_color'] ?? '#1e40af' }}; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 12px;">
                                        {{ $flight['airline_code'] ?? 'XX' }}
                                    </div>
                                    <div class="fw-bold small">{{ $flight['airline_name'] ?? 'Unknown Airline' }}</div>
                                    <div class="text-muted small">{{ $flight['flight_number'] ?? 'XX000' }}</div>
                                </div>

                                <!-- Flight Timeline -->
                                <div class="col-lg-6 col-md-5 mb-3 mb-md-0">
                                    <div class="row align-items-center">
                                        <div class="col-4 text-center">
                                            <div class="h4 mb-0 fw-bold">{{ \Carbon\Carbon::parse($flight['departure_time'] ?? '14:30')->format('H:i') }}</div>
                                            <div class="text-muted small fw-bold">{{ $flight['departure_airport'] ?? 'DEP' }}</div>
                                            <div class="text-muted small">{{ \Carbon\Carbon::parse($flight['departure_date'] ?? now())->format('M d') }}</div>
                                        </div>
                                        <div class="col-4 text-center">
                                            <div class="text-muted small mb-1">{{ $flight['duration'] ?? '2h 30m' }}</div>
                                            <div class="position-relative">
                                                <hr class="my-1" style="border-color: #007bff; border-width: 2px;">
                                                <i class="fas fa-plane position-absolute top-50 start-50 translate-middle bg-white px-2 text-primary" style="font-size: 14px;"></i>
                                            </div>
                                            <div class="small">
                                                @if(($flight['stops'] ?? 0) == 0)
                                                    <span class="badge bg-success">Non-stop</span>
                                                @else
                                                    <span class="badge bg-warning">{{ $flight['stops'] }} stop(s)</span>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="col-4 text-center">
                                            <div class="h4 mb-0 fw-bold">{{ \Carbon\Carbon::parse($flight['arrival_time'] ?? '17:00')->format('H:i') }}</div>
                                            <div class="text-muted small fw-bold">{{ $flight['arrival_airport'] ?? 'ARR' }}</div>
                                            <div class="text-muted small">{{ \Carbon\Carbon::parse($flight['departure_date'] ?? now())->format('M d') }}</div>
                                        </div>
                                    </div>

                                    <!-- Additional Info -->
                                    <div class="row mt-2">
                                        <div class="col-12 text-center">
                                            <small class="text-muted">
                                                <i class="fas fa-suitcase me-1"></i>20+7 KG
                                                <span class="mx-2">•</span>
                                                <i class="fas fa-utensils me-1"></i>{{ $flight['meal'] ?? 'No' }}
                                                <span class="mx-2">•</span>
                                                <i class="fas fa-wifi me-1"></i>{{ $flight['wifi'] ?? 'No' }}
                                            </small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Price and Action -->
                                <div class="col-lg-4 col-md-4 text-center text-md-end">
                                    <div class="mb-2">
                                        <div class="h3 text-primary mb-0 fw-bold">PKR {{ number_format($flight['price'] ?? 84000) }}</div>
                                        <div class="text-muted small">per person</div>
                                    </div>
                                    <button class="btn btn-primary px-4 py-2 fw-bold" onclick="bookFlight('{{ $flight['id'] ?? 'flight-' . $loop->index }}')">
                                        <i class="fas fa-ticket-alt me-2"></i>Book Now
                                    </button>
                                    <div class="mt-2">
                                        <small class="text-success">
                                            <i class="fas fa-check-circle me-1"></i>Instant Confirmation
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @empty
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-plane-slash fa-3x text-muted mb-3"></i>
                            <h4>No flights found</h4>
                            <p class="text-muted mb-4">We couldn't find any flights matching your search criteria.</p>
                            <div class="d-flex justify-content-center gap-2">
                                <a href="{{ route('book-tickets') }}" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>New Search
                                </a>
                                <button class="btn btn-outline-primary" onclick="clearFilters()">
                                    <i class="fas fa-times me-1"></i>Clear Filters
                                </button>
                            </div>
                        </div>
                    </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function bookFlight(flightId) {
    // Store flight selection in session and redirect to booking
    const searchParams = new URLSearchParams({
        flight_id: flightId,
        departure_airport: '{{ $searchParams["departure_airport"] ?? "" }}',
        arrival_airport: '{{ $searchParams["arrival_airport"] ?? "" }}',
        departure_date: '{{ $searchParams["departure_date"] ?? "" }}',
        return_date: '{{ $searchParams["return_date"] ?? "" }}',
        passengers: '{{ $searchParams["passengers"] ?? 1 }}',
        cabin_class: '{{ $searchParams["cabin_class"] ?? "economy" }}'
    });

    window.location.href = '{{ route("flights.booking") }}?' + searchParams.toString();
}

function sortFlights(sortBy) {
    const container = document.getElementById('flight-results');
    const flights = Array.from(container.querySelectorAll('.flight-card'));

    flights.sort((a, b) => {
        switch(sortBy) {
            case 'price':
                return parseInt(a.dataset.price) - parseInt(b.dataset.price);
            case 'price_desc':
                return parseInt(b.dataset.price) - parseInt(a.dataset.price);
            case 'duration':
                return parseInt(a.dataset.duration) - parseInt(b.dataset.duration);
            case 'departure_time':
                return a.dataset.departure.localeCompare(b.dataset.departure);
            case 'arrival_time':
                // Calculate arrival time based on departure + duration
                const depA = new Date('2023-01-01 ' + a.dataset.departure);
                const depB = new Date('2023-01-01 ' + b.dataset.departure);
                const arrA = new Date(depA.getTime() + parseInt(a.dataset.duration) * 60000);
                const arrB = new Date(depB.getTime() + parseInt(b.dataset.duration) * 60000);
                return arrA - arrB;
            default:
                return 0;
        }
    });

    // Re-append sorted flights
    flights.forEach(flight => container.appendChild(flight));
}

// Enhanced filter functionality
document.addEventListener('DOMContentLoaded', function() {
    // Airline filter
    document.querySelectorAll('.airline-filter').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            if (this.value === 'all') {
                // If "All Airlines" is checked, uncheck others
                if (this.checked) {
                    document.querySelectorAll('.airline-filter:not([value="all"])').forEach(cb => cb.checked = false);
                }
            } else {
                // If any specific airline is checked, uncheck "All Airlines"
                if (this.checked) {
                    document.getElementById('airline-all').checked = false;
                }
            }
            applyFilters();
        });
    });

    // Time filters
    document.querySelectorAll('.time-filter').forEach(checkbox => {
        checkbox.addEventListener('change', applyFilters);
    });

    // Stops filters
    document.querySelectorAll('.stops-filter').forEach(checkbox => {
        checkbox.addEventListener('change', applyFilters);
    });
});

function applyFilters() {
    const flights = document.querySelectorAll('.flight-card');
    let visibleCount = 0;

    flights.forEach(flight => {
        let shouldShow = true;

        // Airline filter
        const selectedAirlines = Array.from(document.querySelectorAll('.airline-filter:checked:not([value="all"])'))
            .map(cb => cb.value);
        const allAirlinesChecked = document.getElementById('airline-all').checked;

        if (!allAirlinesChecked && selectedAirlines.length > 0) {
            const airline = flight.dataset.airline;
            shouldShow = shouldShow && selectedAirlines.includes(airline);
        }

        // Time filter
        const selectedTimes = Array.from(document.querySelectorAll('.time-filter:checked'))
            .map(cb => cb.value);

        if (selectedTimes.length > 0) {
            const departureTime = flight.dataset.departure;
            const hour = parseInt(departureTime.split(':')[0]);
            let timeMatch = false;

            selectedTimes.forEach(timeSlot => {
                switch(timeSlot) {
                    case 'morning':
                        if (hour >= 6 && hour < 12) timeMatch = true;
                        break;
                    case 'afternoon':
                        if (hour >= 12 && hour < 18) timeMatch = true;
                        break;
                    case 'evening':
                        if (hour >= 18 || hour < 6) timeMatch = true;
                        break;
                }
            });

            shouldShow = shouldShow && timeMatch;
        }

        // Stops filter
        const selectedStops = Array.from(document.querySelectorAll('.stops-filter:checked'))
            .map(cb => cb.value);

        if (selectedStops.length > 0) {
            const stops = flight.dataset.stops;
            let stopsMatch = false;

            selectedStops.forEach(stopFilter => {
                if (stopFilter === '2' && parseInt(stops) >= 2) {
                    stopsMatch = true;
                } else if (stops === stopFilter) {
                    stopsMatch = true;
                }
            });

            shouldShow = shouldShow && stopsMatch;
        }

        flight.style.display = shouldShow ? 'block' : 'none';
        if (shouldShow) visibleCount++;
    });

    // Update results count
    const resultsHeader = document.querySelector('h5');
    if (resultsHeader) {
        resultsHeader.textContent = `Available Flights (${visibleCount} shown)`;
    }
}

function applyPriceFilter() {
    const minPrice = parseInt(document.getElementById('min-price').value) || 0;
    const maxPrice = parseInt(document.getElementById('max-price').value) || 999999;

    const flights = document.querySelectorAll('.flight-card');
    let visibleCount = 0;

    flights.forEach(flight => {
        const price = parseInt(flight.dataset.price);
        const shouldShow = price >= minPrice && price <= maxPrice;

        if (shouldShow) {
            flight.style.display = 'block';
            visibleCount++;
        } else {
            flight.style.display = 'none';
        }
    });

    // Update results count
    const resultsHeader = document.querySelector('h5');
    if (resultsHeader) {
        resultsHeader.textContent = `Available Flights (${visibleCount} shown)`;
    }

    // Apply other active filters
    applyFilters();
}

function clearFilters() {
    // Reset all filter checkboxes
    document.querySelectorAll('.airline-filter, .time-filter, .stops-filter').forEach(cb => cb.checked = false);
    document.getElementById('airline-all').checked = true;

    // Reset price inputs
    document.getElementById('min-price').value = '0';
    document.getElementById('max-price').value = '200000';

    // Show all flights
    document.querySelectorAll('.flight-card').forEach(flight => {
        flight.style.display = 'block';
    });

    // Update results count
    const totalFlights = document.querySelectorAll('.flight-card').length;
    const resultsHeader = document.querySelector('h5');
    if (resultsHeader) {
        resultsHeader.textContent = `Available Flights`;
    }
}
</script>
@endpush
@endsection
