@extends('layouts.admin')

@section('title', 'Airport Management')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="page-title mb-2">Airport Management</h1>
                <p class="page-subtitle mb-0">Manage airport database for autocomplete functionality</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#importModal">
                    <i class="fas fa-upload me-1"></i>Import CSV
                </button>
                <a href="{{ route('admin.airports.export') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-download me-1"></i>Export CSV
                </a>
                <a href="{{ route('admin.airports.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>Add Airport
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-4 col-md-6">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ $stats['total'] }}</h4>
                        <p class="mb-0">Total Airports</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-plane fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-4 col-md-6">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ $stats['active'] }}</h4>
                        <p class="mb-0">Active Airports</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-4 col-md-6">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ $stats['inactive'] }}</h4>
                        <p class="mb-0">Inactive Airports</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-pause-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" action="{{ route('admin.airports.index') }}">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <input type="text" class="form-control" name="search" 
                                   value="{{ $query }}" placeholder="Search airports by code, name, city, or country...">
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" name="per_page">
                                <option value="15" {{ request('per_page') == 15 ? 'selected' : '' }}>15 per page</option>
                                <option value="25" {{ request('per_page') == 25 ? 'selected' : '' }}>25 per page</option>
                                <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50 per page</option>
                                <option value="100" {{ request('per_page') == 100 ? 'selected' : '' }}>100 per page</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>Search
                                </button>
                                <a href="{{ route('admin.airports.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>Clear
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Airports Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Airports ({{ $airports->total() }} total)</h5>
            </div>
            <div class="card-body">
                @if($airports->count() > 0)
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Name</th>
                                <th>City</th>
                                <th>Country</th>
                                <th>Coordinates</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($airports as $airport)
                            <tr>
                                <td>
                                    <span class="badge bg-primary">{{ $airport->code }}</span>
                                </td>
                                <td>{{ $airport->name }}</td>
                                <td>{{ $airport->city }}</td>
                                <td>{{ $airport->country }}</td>
                                <td>
                                    @if($airport->latitude && $airport->longitude)
                                        <small class="text-muted">
                                            {{ number_format($airport->latitude, 4) }}, 
                                            {{ number_format($airport->longitude, 4) }}
                                        </small>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input status-toggle" type="checkbox" 
                                               data-airport-id="{{ $airport->id }}"
                                               {{ $airport->is_active ? 'checked' : '' }}>
                                        <label class="form-check-label">
                                            {{ $airport->is_active ? 'Active' : 'Inactive' }}
                                        </label>
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('admin.airports.edit', $airport) }}" 
                                           class="btn btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-outline-danger" 
                                                onclick="deleteAirport({{ $airport->id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>
                        Showing {{ $airports->firstItem() }} to {{ $airports->lastItem() }} 
                        of {{ $airports->total() }} results
                    </div>
                    <div>
                        {{ $airports->appends(request()->query())->links() }}
                    </div>
                </div>
                @else
                <div class="text-center py-5">
                    <i class="fas fa-plane fa-3x text-muted mb-3"></i>
                    <h5>No airports found</h5>
                    <p class="text-muted">
                        @if($query)
                            No airports match your search criteria.
                        @else
                            Start by adding some airports to the database.
                        @endif
                    </p>
                    <a href="{{ route('admin.airports.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Add First Airport
                    </a>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Import Airports from CSV</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="importForm" enctype="multipart/form-data">
                    @csrf
                    <div class="mb-3">
                        <label for="csvFile" class="form-label">CSV File</label>
                        <input type="file" class="form-control" id="csvFile" name="file" 
                               accept=".csv,.txt" required>
                        <div class="form-text">
                            CSV should have columns: code, name, city, country, latitude, longitude, timezone, is_active
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="alert alert-info">
                            <strong>CSV Format Example:</strong><br>
                            <code>code,name,city,country,latitude,longitude,timezone,is_active</code><br>
                            <code>JFK,John F. Kennedy International Airport,New York,United States,40.6413,-73.7781,America/New_York,true</code>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="importAirports()">
                    <i class="fas fa-upload me-1"></i>Import
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this airport? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Delete</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Status toggle functionality
    $('.status-toggle').change(function() {
        const airportId = $(this).data('airport-id');
        const isActive = $(this).is(':checked');

        $.post(`/admin/airports/${airportId}/toggle-status`, {
            _token: '{{ csrf_token() }}'
        })
        .done(function(response) {
            if (response.success) {
                toastr.success('Airport status updated successfully');
            } else {
                toastr.error('Failed to update airport status');
                // Revert toggle
                $(this).prop('checked', !isActive);
            }
        })
        .fail(function() {
            toastr.error('Failed to update airport status');
            // Revert toggle
            $(this).prop('checked', !isActive);
        });
    });
});

// Import airports function
function importAirports() {
    const formData = new FormData($('#importForm')[0]);

    $.ajax({
        url: '{{ route("admin.airports.bulk-import") }}',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        beforeSend: function() {
            $('#importModal .btn-primary').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Importing...');
        },
        success: function(response) {
            if (response.success) {
                toastr.success(response.message);
                $('#importModal').modal('hide');
                location.reload();
            } else {
                toastr.error(response.message);
            }
        },
        error: function(xhr) {
            const response = xhr.responseJSON;
            toastr.error(response ? response.message : 'Import failed');
        },
        complete: function() {
            $('#importModal .btn-primary').prop('disabled', false).html('<i class="fas fa-upload me-1"></i>Import');
        }
    });
}

// Delete airport function
let deleteAirportId = null;

function deleteAirport(airportId) {
    deleteAirportId = airportId;
    $('#deleteModal').modal('show');
}

$('#confirmDelete').click(function() {
    if (deleteAirportId) {
        $.ajax({
            url: `/admin/airports/${deleteAirportId}`,
            type: 'DELETE',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                toastr.success('Airport deleted successfully');
                $('#deleteModal').modal('hide');
                location.reload();
            },
            error: function() {
                toastr.error('Failed to delete airport');
            }
        });
    }
});
</script>
@endpush
