@extends('layouts.admin')

@section('title', 'Flight API Management')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="page-title mb-2">Flight API Management</h1>
                <p class="page-subtitle mb-0">Search and import real-time flight data from external providers</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-primary" onclick="syncFlights()">
                    <i class="fas fa-sync-alt me-1"></i>Sync Existing
                </button>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#searchModal">
                    <i class="fas fa-search me-1"></i>Search Flights
                </button>
            </div>
        </div>
    </div>
</div>

<!-- API Status Cards -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-gradient rounded-3 p-3">
                            <i class="fas fa-plug text-white"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-1">Active Providers</h6>
                        <h4 class="mb-0">{{ count($providers) }}</h4>
                        <small class="text-muted">{{ implode(', ', $providers) }}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-gradient rounded-3 p-3">
                            <i class="fas fa-download text-white"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-1">API Imports</h6>
                        <h4 class="mb-0">{{ $recentImports->count() }}</h4>
                        <small class="text-muted">Recent imports</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-gradient rounded-3 p-3">
                            <i class="fas fa-clock text-white"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-1">Last Sync</h6>
                        <h4 class="mb-0">{{ $recentImports->first()?->updated_at?->diffForHumans() ?? 'Never' }}</h4>
                        <small class="text-muted">Auto-sync enabled</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Imports -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="card-title mb-0">Recent API Imports</h5>
            </div>
            <div class="card-body">
                @if($recentImports->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Flight Number</th>
                                    <th>Route</th>
                                    <th>Provider</th>
                                    <th>Price</th>
                                    <th>Status</th>
                                    <th>Imported</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentImports as $flight)
                                <tr>
                                    <td>
                                        <strong>{{ $flight->flight_number }}</strong>
                                        <br>
                                        <small class="text-muted">{{ $flight->airline->name ?? 'Unknown Airline' }}</small>
                                    </td>
                                    <td>
                                        {{ $flight->departureAirport->code ?? 'N/A' }} 
                                        <i class="fas fa-arrow-right mx-1"></i> 
                                        {{ $flight->arrivalAirport->code ?? 'N/A' }}
                                        <br>
                                        <small class="text-muted">
                                            {{ $flight->departure_time ? \Carbon\Carbon::parse($flight->departure_time)->format('M d, H:i') : 'N/A' }}
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ ucfirst($flight->api_provider ?? 'manual') }}</span>
                                    </td>
                                    <td>
                                        <strong>${{ number_format($flight->base_price, 2) }}</strong>
                                        <br>
                                        <small class="text-muted">{{ $flight->currency }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $flight->status === 'Scheduled' ? 'success' : ($flight->status === 'Delayed' ? 'warning' : 'danger') }}">
                                            {{ $flight->status }}
                                        </span>
                                    </td>
                                    <td>
                                        <small>{{ $flight->created_at->diffForHumans() }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ route('admin.flights.show', $flight) }}" class="btn btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button class="btn btn-outline-success" onclick="refreshPrice('{{ $flight->api_flight_id }}', '{{ $flight->api_provider }}')">
                                                <i class="fas fa-sync-alt"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-plane-departure fa-3x text-muted mb-3"></i>
                        <h5>No API imports yet</h5>
                        <p class="text-muted">Start by searching for flights using the external APIs</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#searchModal">
                            <i class="fas fa-search me-1"></i>Search Flights
                        </button>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Flight Search Modal -->
<div class="modal fade" id="searchModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Search External Flight APIs</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="searchForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Departure Airport</label>
                                <input type="text" class="form-control" id="departure_airport" placeholder="e.g., JFK" maxlength="3" required>
                                <div class="form-text">Enter 3-letter IATA code</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Arrival Airport</label>
                                <input type="text" class="form-control" id="arrival_airport" placeholder="e.g., LAX" maxlength="3" required>
                                <div class="form-text">Enter 3-letter IATA code</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Departure Date</label>
                                <input type="date" class="form-control" id="departure_date" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Return Date (Optional)</label>
                                <input type="date" class="form-control" id="return_date">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Adults</label>
                                <select class="form-select" id="adults">
                                    <option value="1">1</option>
                                    <option value="2">2</option>
                                    <option value="3">3</option>
                                    <option value="4">4</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Children</label>
                                <select class="form-select" id="children">
                                    <option value="0">0</option>
                                    <option value="1">1</option>
                                    <option value="2">2</option>
                                    <option value="3">3</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Cabin Class</label>
                                <select class="form-select" id="cabin_class">
                                    <option value="economy">Economy</option>
                                    <option value="premium_economy">Premium Economy</option>
                                    <option value="business">Business</option>
                                    <option value="first">First</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Max Results</label>
                                <select class="form-select" id="max_results">
                                    <option value="20">20</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
                
                <!-- Search Results -->
                <div id="searchResults" class="mt-4" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6>Search Results</h6>
                        <button class="btn btn-success btn-sm" onclick="importSelected()">
                            <i class="fas fa-download me-1"></i>Import Selected
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th>Flight</th>
                                    <th>Route</th>
                                    <th>Time</th>
                                    <th>Duration</th>
                                    <th>Price</th>
                                    <th>Provider</th>
                                </tr>
                            </thead>
                            <tbody id="resultsTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="searchFlights()">
                    <i class="fas fa-search me-1"></i>Search Flights
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let searchResults = [];

// Set minimum date to today
document.getElementById('departure_date').min = new Date().toISOString().split('T')[0];

// Search flights using external APIs
function searchFlights() {
    const form = document.getElementById('searchForm');
    const formData = new FormData(form);

    // Validate form
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const searchParams = {
        departure_airport: document.getElementById('departure_airport').value.toUpperCase(),
        arrival_airport: document.getElementById('arrival_airport').value.toUpperCase(),
        departure_date: document.getElementById('departure_date').value,
        return_date: document.getElementById('return_date').value,
        adults: document.getElementById('adults').value,
        children: document.getElementById('children').value,
        cabin_class: document.getElementById('cabin_class').value,
        max_results: document.getElementById('max_results').value,
    };

    // Show loading
    const searchBtn = event.target;
    const originalText = searchBtn.innerHTML;
    searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Searching...';
    searchBtn.disabled = true;

    fetch('{{ route("admin.flight-api.search") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify(searchParams)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            searchResults = data.data;
            displaySearchResults(data.data);
            showAlert('success', `Found ${data.count} flights`);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Search error:', error);
        showAlert('danger', 'Search failed. Please try again.');
    })
    .finally(() => {
        searchBtn.innerHTML = originalText;
        searchBtn.disabled = false;
    });
}

// Display search results in table
function displaySearchResults(flights) {
    const tbody = document.getElementById('resultsTableBody');
    const resultsDiv = document.getElementById('searchResults');

    tbody.innerHTML = '';

    flights.forEach((flight, index) => {
        const row = `
            <tr>
                <td>
                    <input type="checkbox" class="flight-checkbox" value="${index}" data-flight='${JSON.stringify(flight)}'>
                </td>
                <td>
                    <strong>${flight.flight_number}</strong><br>
                    <small class="text-muted">${flight.airline}</small>
                </td>
                <td>
                    ${flight.departure_airport} <i class="fas fa-arrow-right"></i> ${flight.arrival_airport}<br>
                    <small class="text-muted">${flight.stops} stop${flight.stops !== 1 ? 's' : ''}</small>
                </td>
                <td>
                    <small>
                        Dep: ${formatTime(flight.departure_time)}<br>
                        Arr: ${formatTime(flight.arrival_time)}
                    </small>
                </td>
                <td>
                    <small>${formatDuration(flight.duration)}</small>
                </td>
                <td>
                    <strong>$${parseFloat(flight.price).toFixed(2)}</strong><br>
                    <small class="text-muted">${flight.currency}</small>
                </td>
                <td>
                    <span class="badge bg-primary">${flight.provider}</span>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });

    resultsDiv.style.display = 'block';
}

// Toggle select all checkboxes
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.flight-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// Import selected flights
function importSelected() {
    const checkboxes = document.querySelectorAll('.flight-checkbox:checked');

    if (checkboxes.length === 0) {
        showAlert('warning', 'Please select at least one flight to import');
        return;
    }

    const flights = Array.from(checkboxes).map(checkbox => {
        return JSON.parse(checkbox.dataset.flight);
    });

    // Show loading
    const importBtn = event.target;
    const originalText = importBtn.innerHTML;
    importBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Importing...';
    importBtn.disabled = true;

    fetch('{{ route("admin.flight-api.import") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({ flights: flights })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            if (data.errors.length > 0) {
                console.warn('Import errors:', data.errors);
            }
            // Refresh page after successful import
            setTimeout(() => location.reload(), 2000);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Import error:', error);
        showAlert('danger', 'Import failed. Please try again.');
    })
    .finally(() => {
        importBtn.innerHTML = originalText;
        importBtn.disabled = false;
    });
}

// Sync existing flights
function syncFlights() {
    if (!confirm('This will update all API-imported flights with latest data. Continue?')) {
        return;
    }

    const syncBtn = event.target;
    const originalText = syncBtn.innerHTML;
    syncBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Syncing...';
    syncBtn.disabled = true;

    fetch('{{ route("admin.flight-api.sync") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            if (data.errors.length > 0) {
                console.warn('Sync errors:', data.errors);
            }
            setTimeout(() => location.reload(), 2000);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Sync error:', error);
        showAlert('danger', 'Sync failed. Please try again.');
    })
    .finally(() => {
        syncBtn.innerHTML = originalText;
        syncBtn.disabled = false;
    });
}

// Refresh individual flight price
function refreshPrice(flightId, provider) {
    fetch('{{ route("admin.flight-api.prices") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({ flight_ids: [flightId] })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.data[flightId]) {
            const price = data.data[flightId];
            showAlert('success', `Price updated: $${price.price} ${price.currency}`);
        } else {
            showAlert('info', 'No price update available');
        }
    })
    .catch(error => {
        console.error('Price refresh error:', error);
        showAlert('danger', 'Failed to refresh price');
    });
}

// Utility functions
function formatTime(timeString) {
    if (!timeString) return 'N/A';
    try {
        return new Date(timeString).toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        });
    } catch (e) {
        return timeString;
    }
}

function formatDuration(duration) {
    if (!duration) return 'N/A';
    // Parse ISO 8601 duration (PT2H30M) or return as-is
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?/);
    if (match) {
        const hours = match[1] || '0';
        const minutes = match[2] || '0';
        return `${hours}h ${minutes}m`;
    }
    return duration;
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert at top of page
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
@endpush
