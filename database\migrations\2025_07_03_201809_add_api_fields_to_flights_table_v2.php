<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('flights', function (Blueprint $table) {
            // Add missing API fields
            $table->string('duration')->nullable()->after('duration_minutes'); // ISO 8601 duration format
            $table->integer('stops')->default(0)->after('duration'); // Number of stops
            $table->json('baggage_info')->nullable()->after('stops'); // Baggage allowance info
            $table->text('cancellation_policy')->nullable()->after('baggage_info'); // Cancellation terms

            // Add indexes for better performance
            $table->index(['source', 'api_provider']);
            $table->index('api_flight_id');
            $table->index('api_last_updated');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('flights', function (Blueprint $table) {
            $table->dropIndex(['source', 'api_provider']);
            $table->dropIndex(['api_flight_id']);
            $table->dropIndex(['api_last_updated']);

            $table->dropColumn([
                'duration',
                'stops',
                'baggage_info',
                'cancellation_policy',
            ]);
        });
    }
};
