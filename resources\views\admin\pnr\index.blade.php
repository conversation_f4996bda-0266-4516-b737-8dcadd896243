@extends('layouts.admin')

@section('title', 'PNR Management')

@section('content')
<div class="admin-main">
    <!-- Page Header -->
    <div class="page-header">
        <div class="page-title-wrapper">
            <h1 class="page-title">PNR Management</h1>
            <p class="page-subtitle">Passenger Name Record inventory and seat management</p>
        </div>
        <div class="page-actions">
            <a href="{{ route('admin.pnr.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Create New PNR
            </a>
        </div>
    </div>

    <!-- PNR Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="modern-stats-card gradient-primary">
                <div class="stats-content">
                    <div class="stats-header">
                        <div class="stats-icon-modern">
                            <i class="fas fa-list-alt"></i>
                        </div>
                        <div class="stats-trend">
                            <span class="trend-value">{{ number_format((($stats['available_pnrs'] / max($stats['total_pnrs'], 1)) * 100), 1) }}%</span>
                            <i class="fas fa-arrow-up"></i>
                        </div>
                    </div>
                    <div class="stats-body">
                        <div class="stats-number">{{ number_format($stats['total_pnrs']) }}</div>
                        <div class="stats-label">Total PNRs</div>
                        <div class="stats-sublabel">{{ $stats['available_pnrs'] }} available</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="modern-stats-card gradient-success">
                <div class="stats-content">
                    <div class="stats-header">
                        <div class="stats-icon-modern">
                            <i class="fas fa-chair"></i>
                        </div>
                        <div class="stats-trend">
                            <span class="trend-value">{{ number_format((($stats['total_live_seats'] / max($stats['total_inventory_seats'], 1)) * 100), 1) }}%</span>
                            <i class="fas fa-arrow-up"></i>
                        </div>
                    </div>
                    <div class="stats-body">
                        <div class="stats-number">{{ number_format($stats['total_inventory_seats']) }}</div>
                        <div class="stats-label">Inventory Seats</div>
                        <div class="stats-sublabel">{{ number_format($stats['total_live_seats']) }} live</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="modern-stats-card gradient-info">
                <div class="stats-content">
                    <div class="stats-header">
                        <div class="stats-icon-modern">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <div class="stats-trend">
                            <span class="trend-value">{{ number_format((($stats['total_booked_seats'] / max($stats['total_live_seats'], 1)) * 100), 1) }}%</span>
                            <i class="fas fa-arrow-up"></i>
                        </div>
                    </div>
                    <div class="stats-body">
                        <div class="stats-number">{{ number_format($stats['total_booked_seats']) }}</div>
                        <div class="stats-label">Booked Seats</div>
                        <div class="stats-sublabel">{{ number_format($stats['total_live_seats'] - $stats['total_booked_seats']) }} available</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="modern-stats-card gradient-warning">
                <div class="stats-content">
                    <div class="stats-header">
                        <div class="stats-icon-modern">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stats-trend">
                            <span class="trend-value">{{ number_format((($stats['sold_out_pnrs'] / max($stats['total_pnrs'], 1)) * 100), 1) }}%</span>
                            <i class="fas fa-arrow-up"></i>
                        </div>
                    </div>
                    <div class="stats-body">
                        <div class="stats-number">{{ number_format($stats['sold_out_pnrs']) }}</div>
                        <div class="stats-label">Sold Out PNRs</div>
                        <div class="stats-sublabel">{{ $stats['inventory_only_pnrs'] }} inventory only</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- PNR Filters -->
    <div class="modern-widget mb-4">
        <div class="widget-header">
            <h3 class="widget-title">
                <i class="fas fa-filter me-2"></i>PNR Filters
            </h3>
        </div>
        <div class="widget-body">
            <form method="GET" action="{{ route('admin.pnr.index') }}" class="pnr-filter-form">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="booking-label">
                            <i class="fas fa-search me-1"></i>Search
                        </label>
                        <input type="text" 
                               class="booking-input" 
                               name="search" 
                               value="{{ request('search') }}"
                               placeholder="PNR code or flight number">
                    </div>

                    <div class="col-md-3">
                        <label class="booking-label">
                            <i class="fas fa-info-circle me-1"></i>Status
                        </label>
                        <select class="booking-select" name="status">
                            <option value="">All Statuses</option>
                            <option value="available" {{ request('status') === 'available' ? 'selected' : '' }}>Available</option>
                            <option value="sold_out" {{ request('status') === 'sold_out' ? 'selected' : '' }}>Sold Out</option>
                            <option value="inventory_only" {{ request('status') === 'inventory_only' ? 'selected' : '' }}>Inventory Only</option>
                            <option value="cancelled" {{ request('status') === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                        </select>
                    </div>

                    <div class="col-md-3">
                        <label class="booking-label">
                            <i class="fas fa-plane me-1"></i>Flight
                        </label>
                        <select class="booking-select" name="flight_id">
                            <option value="">All Flights</option>
                            @foreach($flights as $flight)
                            <option value="{{ $flight->id }}" {{ request('flight_id') == $flight->id ? 'selected' : '' }}>
                                {{ $flight->flight_number }} - {{ $flight->departureAirport->iata_code ?? 'N/A' }} → {{ $flight->arrivalAirport->iata_code ?? 'N/A' }}
                            </option>
                            @endforeach
                        </select>
                    </div>

                    <div class="col-md-3">
                        <label class="booking-label">&nbsp;</label>
                        <div class="d-flex gap-2">
                            <button type="submit" class="booking-search-btn flex-fill">
                                <i class="fas fa-search me-2"></i>Filter
                            </button>
                            <a href="{{ route('admin.pnr.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- PNR Table -->
    <div class="modern-widget">
        <div class="widget-header">
            <h3 class="widget-title">
                <i class="fas fa-list me-2"></i>PNR Records
            </h3>
            <p class="widget-subtitle">Manage passenger name records and seat inventory</p>
        </div>
        <div class="widget-body">
            <div class="table-responsive">
                <table class="table pnr-table" id="pnr-table">
                    <thead>
                        <tr>
                            <th>PNR Code</th>
                            <th>Flight</th>
                            <th>Seat Inventory</th>
                            <th>Availability</th>
                            <th>Price</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($pnrs as $pnr)
                        <tr class="pnr-row" data-pnr-id="{{ $pnr->id }}">
                            <td>
                                <div class="pnr-info">
                                    <div class="pnr-code">{{ $pnr->pnr_code }}</div>
                                    <div class="pnr-created">{{ $pnr->created_at->format('M d, Y') }}</div>
                                </div>
                            </td>
                            <td>
                                <div class="flight-info">
                                    <div class="flight-number">{{ $pnr->flight->flight_number }}</div>
                                    <div class="flight-route">
                                        {{ $pnr->flight->departureAirport->iata_code ?? 'N/A' }} → {{ $pnr->flight->arrivalAirport->iata_code ?? 'N/A' }}
                                    </div>
                                    <div class="flight-date">{{ $pnr->flight->departure_time->format('M d, Y H:i') }}</div>
                                </div>
                            </td>
                            <td>
                                <div class="seat-inventory">
                                    <div class="inventory-total">
                                        <i class="fas fa-chair me-1"></i>
                                        {{ $pnr->total_inventory_seats }} total
                                    </div>
                                    <div class="inventory-live">
                                        <i class="fas fa-eye me-1"></i>
                                        {{ $pnr->live_seats }} live
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="availability-info">
                                    <div class="booked-seats">
                                        <i class="fas fa-ticket-alt me-1"></i>
                                        {{ $pnr->booked_seats }} booked
                                    </div>
                                    <div class="available-seats">
                                        <i class="fas fa-check-circle me-1"></i>
                                        {{ $pnr->available_seats }} available
                                    </div>
                                    @if($pnr->locked_seats > 0)
                                    <div class="locked-seats">
                                        <i class="fas fa-lock me-1"></i>
                                        {{ $pnr->locked_seats }} locked
                                    </div>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div class="price-info">
                                    <div class="base-price">{{ $pnr->currency }} {{ number_format($pnr->base_price, 2) }}</div>
                                    <div class="price-label">per seat</div>
                                </div>
                            </td>
                            <td>
                                <span class="status-badge status-{{ $pnr->status_color }}">
                                    {{ $pnr->status_display }}
                                </span>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="{{ route('admin.pnr.show', $pnr) }}" 
                                       class="btn btn-sm btn-outline-primary" 
                                       title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.pnr.edit', $pnr) }}" 
                                       class="btn btn-sm btn-outline-secondary" 
                                       title="Edit PNR">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    @if($pnr->status === 'inventory_only')
                                    <button class="btn btn-sm btn-outline-success make-live-btn" 
                                            data-pnr-id="{{ $pnr->id }}"
                                            title="Make Seats Live">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    @endif
                                    @if($pnr->bookings()->count() === 0)
                                    <form action="{{ route('admin.pnr.destroy', $pnr) }}" 
                                          method="POST" 
                                          class="d-inline"
                                          onsubmit="return confirm('Are you sure you want to delete this PNR?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" 
                                                class="btn btn-sm btn-outline-danger" 
                                                title="Delete PNR">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <div class="empty-state">
                                    <i class="fas fa-list-alt fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No PNRs found</h5>
                                    <p class="text-muted">Create your first PNR to get started</p>
                                    <a href="{{ route('admin.pnr.create') }}" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>Create PNR
                                    </a>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($pnrs->hasPages())
            <div class="d-flex justify-content-center mt-4">
                {{ $pnrs->appends(request()->query())->links() }}
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Make Seats Live Modal -->
<div class="modal fade" id="makeLiveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Make Seats Live</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="makeLiveForm">
                    <div class="mb-3">
                        <label class="booking-label">Number of seats to make live</label>
                        <input type="number" 
                               class="booking-input" 
                               id="liveSeatsInput" 
                               min="0" 
                               required>
                        <div class="form-text">
                            Maximum: <span id="maxSeats">0</span> seats
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmMakeLive">Make Live</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* PNR Management Styles */
.pnr-filter-form {
    background: linear-gradient(135deg, rgba(248, 248, 255, 0.5) 0%, rgba(255, 255, 255, 0.8) 100%);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    border: 1px solid rgba(77, 115, 252, 0.1);
}

/* PNR Table */
.pnr-table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-secondary);
}

.pnr-table thead {
    background: var(--gradient-blue-white);
    color: white;
}

.pnr-table thead th {
    border: none;
    padding: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

.pnr-table tbody tr {
    border-bottom: 1px solid rgba(77, 115, 252, 0.1);
    transition: all 0.3s ease;
}

.pnr-table tbody tr:hover {
    background: linear-gradient(135deg, rgba(248, 248, 255, 0.5) 0%, rgba(255, 255, 255, 0.8) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(77, 115, 252, 0.1);
}

.pnr-table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border: none;
}

/* PNR Info Styles */
.pnr-info .pnr-code {
    font-weight: 700;
    color: var(--primary-blue);
    font-size: 1.1rem;
    font-family: 'Courier New', monospace;
}

.pnr-info .pnr-created {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.flight-info .flight-number {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.flight-info .flight-route {
    color: var(--primary-blue);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.flight-info .flight-date {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.seat-inventory div {
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}

.seat-inventory .inventory-total {
    color: var(--text-primary);
    font-weight: 600;
}

.seat-inventory .inventory-live {
    color: var(--primary-blue);
}

.availability-info div {
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}

.availability-info .booked-seats {
    color: var(--danger-color);
    font-weight: 600;
}

.availability-info .available-seats {
    color: var(--success-color);
    font-weight: 600;
}

.availability-info .locked-seats {
    color: var(--warning-color);
    font-weight: 600;
}

.price-info .base-price {
    font-weight: 700;
    color: var(--text-primary);
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
}

.price-info .price-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Status Badges */
.status-badge {
    padding: 0.375rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #34d399 100%);
    color: white;
}

.status-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #f87171 100%);
    color: white;
}

.status-secondary {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #94a3b8 100%);
    color: white;
}

.status-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #fbbf24 100%);
    color: white;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.action-buttons .btn {
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.action-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-secondary);
}

/* Make Live Button */
.make-live-btn {
    background: linear-gradient(135deg, var(--success-color) 0%, #34d399 100%);
    border: none;
    color: white;
}

.make-live-btn:hover {
    background: linear-gradient(135deg, #059669 0%, var(--success-color) 100%);
    color: white;
}

/* Empty State */
.empty-state {
    padding: 3rem 2rem;
}

.empty-state i {
    color: var(--text-light);
}

/* Modal Styles */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--shadow-primary);
}

.modal-header {
    background: var(--gradient-blue-white);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .pnr-filter-form .row {
        gap: 1rem;
    }

    .pnr-filter-form .col-md-3 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .pnr-table {
        font-size: 0.875rem;
    }

    .pnr-table thead th,
    .pnr-table tbody td {
        padding: 0.75rem 0.5rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }

    .action-buttons .btn {
        width: 100%;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    let currentPnrId = null;

    // Make Live Modal
    const makeLiveModal = new bootstrap.Modal(document.getElementById('makeLiveModal'));
    const makeLiveForm = document.getElementById('makeLiveForm');
    const liveSeatsInput = document.getElementById('liveSeatsInput');
    const maxSeatsSpan = document.getElementById('maxSeats');
    const confirmMakeLiveBtn = document.getElementById('confirmMakeLive');

    // Make Live Button Click
    document.querySelectorAll('.make-live-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            currentPnrId = this.dataset.pnrId;
            const row = this.closest('.pnr-row');
            const inventorySeats = row.querySelector('.inventory-total').textContent.match(/\d+/)[0];

            maxSeatsSpan.textContent = inventorySeats;
            liveSeatsInput.max = inventorySeats;
            liveSeatsInput.value = '';

            makeLiveModal.show();
        });
    });

    // Confirm Make Live
    confirmMakeLiveBtn.addEventListener('click', function() {
        const seats = parseInt(liveSeatsInput.value);

        if (!seats || seats < 0) {
            alert('Please enter a valid number of seats');
            return;
        }

        if (seats > parseInt(liveSeatsInput.max)) {
            alert('Cannot exceed maximum inventory seats');
            return;
        }

        makeSeatsLive(currentPnrId, seats);
    });

    // Make Seats Live Function
    function makeSeatsLive(pnrId, seats) {
        const btn = confirmMakeLiveBtn;
        const originalText = btn.textContent;

        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';

        fetch(`/admin/pnr/${pnrId}/make-live`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ seats: seats })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the row
                updatePnrRow(pnrId, data.pnr);

                // Show success message
                showAlert('success', data.message);

                // Close modal
                makeLiveModal.hide();

                // Refresh page to update stats
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showAlert('error', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'An error occurred while making seats live');
        })
        .finally(() => {
            btn.disabled = false;
            btn.textContent = originalText;
        });
    }

    // Update PNR Row
    function updatePnrRow(pnrId, pnrData) {
        const row = document.querySelector(`[data-pnr-id="${pnrId}"]`);
        if (!row) return;

        // Update live seats
        const liveSeatsElement = row.querySelector('.inventory-live');
        if (liveSeatsElement) {
            liveSeatsElement.innerHTML = `<i class="fas fa-eye me-1"></i>${pnrData.live_seats} live`;
        }

        // Update available seats
        const availableSeatsElement = row.querySelector('.available-seats');
        if (availableSeatsElement) {
            availableSeatsElement.innerHTML = `<i class="fas fa-check-circle me-1"></i>${pnrData.available_seats} available`;
        }

        // Update status badge
        const statusBadge = row.querySelector('.status-badge');
        if (statusBadge) {
            statusBadge.className = `status-badge status-${pnrData.status_color}`;
            statusBadge.textContent = pnrData.status_display;
        }

        // Remove make live button if status is no longer inventory_only
        if (pnrData.status !== 'inventory_only') {
            const makeLiveBtn = row.querySelector('.make-live-btn');
            if (makeLiveBtn) {
                makeLiveBtn.remove();
            }
        }
    }

    // Show Alert Function
    function showAlert(type, message) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // Insert alert at the top of the page
        const pageHeader = document.querySelector('.page-header');
        pageHeader.insertAdjacentHTML('afterend', alertHtml);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            const alert = document.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }

    // Real-time seat availability updates (every 30 seconds)
    setInterval(function() {
        updateSeatAvailability();
    }, 30000);

    function updateSeatAvailability() {
        const pnrRows = document.querySelectorAll('.pnr-row');

        pnrRows.forEach(row => {
            const pnrId = row.dataset.pnrId;

            fetch(`/admin/pnr/${pnrId}/availability`)
                .then(response => response.json())
                .then(data => {
                    // Update available seats
                    const availableSeatsElement = row.querySelector('.available-seats');
                    if (availableSeatsElement) {
                        availableSeatsElement.innerHTML = `<i class="fas fa-check-circle me-1"></i>${data.available_seats} available`;
                    }

                    // Update locked seats
                    const lockedSeatsElement = row.querySelector('.locked-seats');
                    if (data.locked_seats > 0) {
                        if (!lockedSeatsElement) {
                            const availabilityInfo = row.querySelector('.availability-info');
                            availabilityInfo.insertAdjacentHTML('beforeend', `
                                <div class="locked-seats">
                                    <i class="fas fa-lock me-1"></i>
                                    ${data.locked_seats} locked
                                </div>
                            `);
                        } else {
                            lockedSeatsElement.innerHTML = `<i class="fas fa-lock me-1"></i>${data.locked_seats} locked`;
                        }
                    } else if (lockedSeatsElement) {
                        lockedSeatsElement.remove();
                    }

                    // Update status if changed
                    const statusBadge = row.querySelector('.status-badge');
                    if (statusBadge && statusBadge.textContent.trim() !== data.status_display) {
                        statusBadge.className = `status-badge status-${data.status_color}`;
                        statusBadge.textContent = data.status_display;
                    }
                })
                .catch(error => {
                    console.error('Error updating seat availability:', error);
                });
        });
    }
});
</script>
@endpush
