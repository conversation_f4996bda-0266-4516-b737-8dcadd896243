<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions
        $permissions = [
            'view-dashboard',
            'manage-flights',
            'manage-bookings',
            'manage-airlines',
            'manage-airports',
            'manage-users',
            'manage-roles',
            'view-analytics',
            'manage-content',
            'manage-settings',
            'export-data',
            'view-reports'
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles and assign permissions
        $adminRole = Role::firstOrCreate(['name' => 'Admin']);
        $adminRole->givePermissionTo(Permission::all());

        $managerRole = Role::firstOrCreate(['name' => 'Manager']);
        $managerRole->givePermissionTo([
            'view-dashboard',
            'manage-flights',
            'manage-bookings',
            'view-analytics',
            'view-reports',
            'export-data'
        ]);

        $staffRole = Role::firstOrCreate(['name' => 'Staff']);
        $staffRole->givePermissionTo([
            'view-dashboard',
            'manage-bookings',
            'view-reports'
        ]);

        $viewerRole = Role::firstOrCreate(['name' => 'Viewer']);
        $viewerRole->givePermissionTo([
            'view-dashboard',
            'view-reports'
        ]);

        // Assign admin role to the first user if exists
        $firstUser = User::first();
        if ($firstUser && !$firstUser->hasAnyRole(['Admin', 'Manager', 'Staff', 'Viewer'])) {
            $firstUser->assignRole('Admin');
        }
    }
}
