<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;
use App\Services\FlightApiService;

class BookTicketsController extends Controller
{
    protected $flightApiService;

    public function __construct(FlightApiService $flightApiService)
    {
        $this->flightApiService = $flightApiService;
    }

    /**
     * Display the admin book tickets page
     */
    public function index(Request $request): View
    {
        // Get search parameters
        $searchParams = [
            'departure_airport' => $request->get('departure_airport', 'ISB'),
            'arrival_airport' => $request->get('arrival_airport', 'BAH'),
            'departure_date' => $request->get('departure_date', now()->addDay()->format('Y-m-d')),
            'return_date' => $request->get('return_date'),
            'passengers' => $request->get('passengers', 1),
            'cabin_class' => $request->get('cabin_class', 'economy'),
            'trip_type' => $request->get('trip_type', 'one_way'),
            'airline_filter' => $request->get('airline_filter', []),
            'sector_filter' => $request->get('sector_filter', []),
            'price_min' => $request->get('price_min', 0),
            'price_max' => $request->get('price_max', 200000),
        ];

        // Get flights if search parameters are provided
        $flights = [];
        $departureAirport = null;
        $arrivalAirport = null;

        if ($request->has('search') || $request->has('departure_airport')) {
            try {
                // For demo purposes, generate sample flights
                $flights = $this->generateSampleFlights($searchParams);
                $departureAirport = $this->getAirportInfo($searchParams['departure_airport']);
                $arrivalAirport = $this->getAirportInfo($searchParams['arrival_airport']);
            } catch (\Exception $e) {
                // Log error and continue with empty results
                \Log::error('Flight search error: ' . $e->getMessage());
            }
        }

        // Get available airlines and sectors for filters
        $airlines = $this->getAvailableAirlines();
        $sectors = $this->getAvailableSectors();

        return view('admin.book-tickets.index', compact(
            'flights',
            'searchParams',
            'departureAirport',
            'arrivalAirport',
            'airlines',
            'sectors'
        ));
    }

    /**
     * Search flights via AJAX
     */
    public function searchFlights(Request $request)
    {
        $searchParams = $request->validate([
            'departure_airport' => 'required|string|max:3',
            'arrival_airport' => 'required|string|max:3',
            'departure_date' => 'required|date',
            'return_date' => 'nullable|date|after:departure_date',
            'passengers' => 'required|integer|min:1|max:9',
            'cabin_class' => 'required|in:economy,premium_economy,business,first',
            'trip_type' => 'required|in:one_way,round_trip'
        ]);

        try {
            $results = $this->flightApiService->searchFlights($searchParams);
            return response()->json([
                'success' => true,
                'flights' => $results['flights'] ?? [],
                'departure_airport' => $results['departure_airport'] ?? null,
                'arrival_airport' => $results['arrival_airport'] ?? null,
                'search_params' => $searchParams
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error searching flights: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Book a flight
     */
    public function bookFlight(Request $request)
    {
        $request->validate([
            'flight_id' => 'required|string',
            'passenger_details' => 'required|array',
            'contact_info' => 'required|array'
        ]);

        try {
            // Here you would implement the actual booking logic
            // For now, we'll simulate a successful booking
            
            $bookingReference = 'BK' . strtoupper(uniqid());
            
            return response()->json([
                'success' => true,
                'booking_reference' => $bookingReference,
                'message' => 'Flight booked successfully!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Booking failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get airport suggestions for autocomplete
     */
    public function searchAirports(Request $request)
    {
        $query = $request->get('q', '');
        
        if (strlen($query) < 1) {
            return response()->json([]);
        }

        // Sample airport data - in production, this would come from database
        $airports = [
            ['code' => 'ISB', 'name' => 'Islamabad International Airport', 'city' => 'Islamabad', 'country' => 'Pakistan'],
            ['code' => 'LHE', 'name' => 'Allama Iqbal International Airport', 'city' => 'Lahore', 'country' => 'Pakistan'],
            ['code' => 'KHI', 'name' => 'Jinnah International Airport', 'city' => 'Karachi', 'country' => 'Pakistan'],
            ['code' => 'PEW', 'name' => 'Peshawar International Airport', 'city' => 'Peshawar', 'country' => 'Pakistan'],
            ['code' => 'MUX', 'name' => 'Multan International Airport', 'city' => 'Multan', 'country' => 'Pakistan'],
            ['code' => 'BAH', 'name' => 'Bahrain International Airport', 'city' => 'Manama', 'country' => 'Bahrain'],
            ['code' => 'DXB', 'name' => 'Dubai International Airport', 'city' => 'Dubai', 'country' => 'UAE'],
            ['code' => 'SHJ', 'name' => 'Sharjah International Airport', 'city' => 'Sharjah', 'country' => 'UAE'],
            ['code' => 'AUH', 'name' => 'Abu Dhabi International Airport', 'city' => 'Abu Dhabi', 'country' => 'UAE'],
            ['code' => 'DOH', 'name' => 'Hamad International Airport', 'city' => 'Doha', 'country' => 'Qatar'],
            ['code' => 'KWI', 'name' => 'Kuwait International Airport', 'city' => 'Kuwait City', 'country' => 'Kuwait'],
            ['code' => 'RUH', 'name' => 'King Khalid International Airport', 'city' => 'Riyadh', 'country' => 'Saudi Arabia'],
            ['code' => 'JED', 'name' => 'King Abdulaziz International Airport', 'city' => 'Jeddah', 'country' => 'Saudi Arabia'],
            ['code' => 'MCT', 'name' => 'Muscat International Airport', 'city' => 'Muscat', 'country' => 'Oman'],
        ];

        $query = strtolower($query);
        $matches = array_filter($airports, function($airport) use ($query) {
            return strpos(strtolower($airport['code']), $query) !== false ||
                   strpos(strtolower($airport['name']), $query) !== false ||
                   strpos(strtolower($airport['city']), $query) !== false ||
                   strpos(strtolower($airport['country']), $query) !== false;
        });

        // Limit to 10 results
        $matches = array_slice($matches, 0, 10);

        return response()->json(array_values($matches));
    }

    /**
     * Get available airlines for filtering
     */
    private function getAvailableAirlines()
    {
        return [
            ['code' => 'PK', 'name' => 'Fly Jinnah', 'color' => '#1e40af'],
            ['code' => 'PA', 'name' => 'Serene Airline', 'color' => '#059669'],
            ['code' => 'G9', 'name' => 'Air Arabia', 'color' => '#dc2626'],
            ['code' => 'EK', 'name' => 'Emirates', 'color' => '#b91c1c'],
            ['code' => 'QR', 'name' => 'Qatar Airways', 'color' => '#7c2d12'],
            ['code' => 'TK', 'name' => 'Turkish Airlines', 'color' => '#991b1b'],
        ];
    }

    /**
     * Get available sectors for filtering
     */
    private function getAvailableSectors()
    {
        return [
            ['code' => 'ISB-BAH', 'name' => 'Islamabad → Bahrain'],
            ['code' => 'ISB-SHJ', 'name' => 'Islamabad → Sharjah'],
            ['code' => 'LHE-DXB', 'name' => 'Lahore → Dubai'],
            ['code' => 'KHI-DOH', 'name' => 'Karachi → Doha'],
            ['code' => 'ISB-DXB', 'name' => 'Islamabad → Dubai'],
            ['code' => 'LHE-BAH', 'name' => 'Lahore → Bahrain'],
        ];
    }

    /**
     * Generate sample flights for demo purposes
     */
    private function generateSampleFlights($searchParams)
    {
        $airlines = $this->getAvailableAirlines();
        $flights = [];

        $basePrice = 84000;
        $departureDate = $searchParams['departure_date'];

        foreach ($airlines as $index => $airline) {
            $flightNumber = $airline['code'] . '-' . (764 + $index);
            $departureTime = sprintf('%02d:%02d', 12 + $index, 30 + ($index * 15));
            $arrivalTime = sprintf('%02d:%02d', 15 + $index, 0 + ($index * 15));
            $price = $basePrice + ($index * 1000);

            $flights[] = [
                'id' => 'flight-' . $index,
                'flight_number' => $flightNumber,
                'airline_code' => $airline['code'],
                'airline_name' => $airline['name'],
                'airline_color' => $airline['color'],
                'departure_airport' => $searchParams['departure_airport'],
                'arrival_airport' => $searchParams['arrival_airport'],
                'departure_date' => $departureDate,
                'departure_time' => $departureTime,
                'arrival_time' => $arrivalTime,
                'duration' => '2h 30m',
                'duration_minutes' => 150 + ($index * 10),
                'stops' => 0,
                'price' => $price,
                'currency' => 'PKR',
                'meal' => $index % 2 === 0 ? 'Yes' : 'No',
                'baggage' => '20+7 KG',
                'cabin_class' => $searchParams['cabin_class'],
                'available_seats' => 50 - ($index * 5),
            ];
        }

        return $flights;
    }

    /**
     * Get airport information
     */
    private function getAirportInfo($code)
    {
        $airports = [
            'ISB' => ['code' => 'ISB', 'name' => 'Islamabad International Airport', 'city' => 'Islamabad', 'country' => 'Pakistan'],
            'LHE' => ['code' => 'LHE', 'name' => 'Allama Iqbal International Airport', 'city' => 'Lahore', 'country' => 'Pakistan'],
            'KHI' => ['code' => 'KHI', 'name' => 'Jinnah International Airport', 'city' => 'Karachi', 'country' => 'Pakistan'],
            'BAH' => ['code' => 'BAH', 'name' => 'Bahrain International Airport', 'city' => 'Manama', 'country' => 'Bahrain'],
            'DXB' => ['code' => 'DXB', 'name' => 'Dubai International Airport', 'city' => 'Dubai', 'country' => 'UAE'],
            'SHJ' => ['code' => 'SHJ', 'name' => 'Sharjah International Airport', 'city' => 'Sharjah', 'country' => 'UAE'],
            'DOH' => ['code' => 'DOH', 'name' => 'Hamad International Airport', 'city' => 'Doha', 'country' => 'Qatar'],
        ];

        return $airports[$code] ?? ['code' => $code, 'name' => 'Unknown Airport', 'city' => $code, 'country' => 'Unknown'];
    }
}
