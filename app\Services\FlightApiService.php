<?php

namespace App\Services;

use App\Services\FlightProviders\AmadeusProvider;
use App\Services\FlightProviders\RapidApiSkyscannerProvider;
use App\Services\FlightProviders\TravelPayoutsProvider;
use App\Services\FlightProviders\MockProvider;
use App\Services\FlightProviders\AirLabsProvider;
use App\Services\FlightProviders\FlightProviderInterface;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Exception;

class FlightApiService
{
    protected array $providers = [];
    protected array $config;

    public function __construct()
    {
        $this->config = config('flight_apis');
        $this->initializeProviders();
    }

    /**
     * Initialize all enabled flight API providers
     */
    protected function initializeProviders(): void
    {
        foreach ($this->config['providers'] as $name => $config) {
            if ($config['enabled'] ?? false) {
                $this->providers[$name] = $this->createProvider($name, $config);
            }
        }
    }

    /**
     * Create a flight provider instance
     */
    protected function createProvider(string $name, array $config): FlightProviderInterface
    {
        return match ($name) {
            'amadeus' => new AmadeusProvider($config),
            'rapidapi_skyscanner' => new RapidApiSkyscannerProvider($config),
            'travelpayouts' => new TravelPayoutsProvider($config),
            'mock_provider' => new MockProvider($config),
            'airlabs' => new AirLabsProvider($config),
            default => throw new Exception("Unknown flight provider: {$name}")
        };
    }

    /**
     * Search for flights with fallback support
     */
    public function searchFlights(array $searchParams): array
    {
        $cacheKey = $this->generateCacheKey($searchParams);
        
        if ($this->config['cache']['enabled'] && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $results = $this->performSearch($searchParams);
        
        if ($this->config['cache']['enabled'] && !empty($results)) {
            Cache::put($cacheKey, $results, $this->config['cache']['ttl']);
        }

        return $results;
    }

    /**
     * Perform flight search with provider fallback
     */
    protected function performSearch(array $searchParams): array
    {
        $fallbackOrder = $this->config['fallback']['order'] ?? array_keys($this->providers);
        $maxRetries = $this->config['fallback']['max_retries'] ?? 3;
        
        foreach ($fallbackOrder as $providerName) {
            if (!isset($this->providers[$providerName])) {
                continue;
            }

            $provider = $this->providers[$providerName];
            
            for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
                try {
                    Log::info("Searching flights with {$providerName}, attempt {$attempt}");
                    
                    $results = $provider->searchFlights($searchParams);
                    
                    if (!empty($results)) {
                        Log::info("Flight search successful with {$providerName}");
                        return $this->normalizeResults($results, $providerName);
                    }
                } catch (Exception $e) {
                    Log::warning("Flight search failed with {$providerName}, attempt {$attempt}: " . $e->getMessage());
                    
                    if ($attempt < $maxRetries) {
                        sleep($this->config['fallback']['retry_delay'] ?? 2);
                    }
                }
            }
        }

        Log::error('All flight providers failed');
        return [];
    }

    /**
     * Get flight details by ID
     */
    public function getFlightDetails(string $flightId, string $provider = null): ?array
    {
        $provider = $provider ?? $this->config['default'];
        
        if (!isset($this->providers[$provider])) {
            throw new Exception("Provider {$provider} not available");
        }

        try {
            return $this->providers[$provider]->getFlightDetails($flightId);
        } catch (Exception $e) {
            Log::error("Failed to get flight details: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get real-time flight prices
     */
    public function getFlightPrices(array $flightIds): array
    {
        $prices = [];
        
        foreach ($this->providers as $providerName => $provider) {
            try {
                $providerPrices = $provider->getFlightPrices($flightIds);
                $prices = array_merge($prices, $providerPrices);
            } catch (Exception $e) {
                Log::warning("Failed to get prices from {$providerName}: " . $e->getMessage());
            }
        }

        return $prices;
    }

    /**
     * Book a flight
     */
    public function bookFlight(array $bookingData): array
    {
        $provider = $bookingData['provider'] ?? $this->config['default'];
        
        if (!isset($this->providers[$provider])) {
            throw new Exception("Provider {$provider} not available for booking");
        }

        try {
            return $this->providers[$provider]->bookFlight($bookingData);
        } catch (Exception $e) {
            Log::error("Flight booking failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get available airports
     */
    public function getAirports(string $query = ''): array
    {
        return $this->searchAirports($query);
    }

    /**
     * Search airports with fallback support
     */
    public function searchAirports(string $query = ''): array
    {
        $cacheKey = "airports_search_" . md5($query);

        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        foreach ($this->providers as $providerName => $provider) {
            try {
                $airports = $provider->searchAirports($query);
                if (!empty($airports)) {
                    Cache::put($cacheKey, $airports, 3600); // Cache for 1 hour
                    return $airports;
                }
            } catch (Exception $e) {
                Log::warning("Airport search failed with {$providerName}: " . $e->getMessage());
            }
        }

        return [];
    }

    /**
     * Normalize flight results from different providers
     */
    protected function normalizeResults(array $results, string $provider): array
    {
        return array_map(function ($flight) use ($provider) {
            return [
                'id' => $flight['id'] ?? uniqid(),
                'provider' => $provider,
                'airline' => $flight['airline'] ?? '',
                'flight_number' => $flight['flight_number'] ?? '',
                'departure_airport' => $flight['departure_airport'] ?? '',
                'arrival_airport' => $flight['arrival_airport'] ?? '',
                'departure_time' => $flight['departure_time'] ?? '',
                'arrival_time' => $flight['arrival_time'] ?? '',
                'duration' => $flight['duration'] ?? '',
                'stops' => $flight['stops'] ?? 0,
                'price' => $flight['price'] ?? 0,
                'currency' => $flight['currency'] ?? 'USD',
                'cabin_class' => $flight['cabin_class'] ?? 'economy',
                'available_seats' => $flight['available_seats'] ?? 0,
                'baggage_info' => $flight['baggage_info'] ?? [],
                'cancellation_policy' => $flight['cancellation_policy'] ?? '',
                'raw_data' => $flight,
            ];
        }, $results);
    }

    /**
     * Generate cache key for search parameters
     */
    protected function generateCacheKey(array $params): string
    {
        $key = $this->config['cache']['prefix'] . '_' . md5(serialize($params));
        return $key;
    }

    /**
     * Get available providers
     */
    public function getAvailableProviders(): array
    {
        return $this->providers;
    }

    /**
     * Get available provider names
     */
    public function getAvailableProviderNames(): array
    {
        return array_keys($this->providers);
    }

    /**
     * Check if a provider is available
     */
    public function isProviderAvailable(string $provider): bool
    {
        return isset($this->providers[$provider]);
    }
}
