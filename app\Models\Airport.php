<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Airport extends Model
{
    protected $fillable = [
        'code',
        'name',
        'city',
        'country',
        'latitude',
        'longitude',
        'timezone',
        'is_active',
    ];

    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'is_active' => 'boolean',
    ];

    /**
     * Get all departing flights from this airport
     */
    public function departingFlights(): HasMany
    {
        return $this->hasMany(Flight::class, 'departure_airport_id');
    }

    /**
     * Get all arriving flights to this airport
     */
    public function arrivingFlights(): HasMany
    {
        return $this->hasMany(Flight::class, 'arrival_airport_id');
    }

    /**
     * Scope to get only active airports
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get full airport display name
     */
    public function getFullNameAttribute(): string
    {
        return "{$this->name} ({$this->code})";
    }

    /**
     * Search airports by query
     */
    public static function search(string $query, int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return static::active()
            ->where(function ($q) use ($query) {
                $q->where('code', 'LIKE', "%{$query}%")
                  ->orWhere('name', 'LIKE', "%{$query}%")
                  ->orWhere('city', 'LIKE', "%{$query}%")
                  ->orWhere('country', 'LIKE', "%{$query}%");
            })
            ->orderByRaw("
                CASE
                    WHEN code LIKE '{$query}%' THEN 1
                    WHEN name LIKE '{$query}%' THEN 2
                    WHEN city LIKE '{$query}%' THEN 3
                    ELSE 4
                END
            ")
            ->limit($limit)
            ->get();
    }

    /**
     * Get airport for autocomplete display
     */
    public function toAutocompleteArray(): array
    {
        return [
            'code' => $this->code,
            'name' => $this->name,
            'city' => $this->city,
            'country' => $this->country,
            'display' => "{$this->code} - {$this->name}",
            'subtitle' => "{$this->city}, {$this->country}",
            'full_display' => "{$this->code} - {$this->name}\n{$this->city}, {$this->country}"
        ];
    }

    /**
     * Bulk import airports from array
     */
    public static function bulkImport(array $airports): int
    {
        $imported = 0;
        foreach ($airports as $airportData) {
            try {
                static::updateOrCreate(
                    ['code' => $airportData['code']],
                    $airportData
                );
                $imported++;
            } catch (\Exception $e) {
                \Log::error('Failed to import airport: ' . $e->getMessage(), $airportData);
            }
        }
        return $imported;
    }
}
