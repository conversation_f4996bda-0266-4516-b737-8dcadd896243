<?php

namespace Database\Seeders;

use App\Models\Flight;
use App\Models\Airline;
use App\Models\Airport;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class FlightSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $airlines = Airline::all();
        $airports = Airport::all();

        $aircraftTypes = [
            'Boeing 737', 'Boeing 747', 'Boeing 777', 'Boeing 787',
            'Airbus A320', 'Airbus A330', 'Airbus A350', 'Airbus A380'
        ];

        $classTypes = ['Economy', 'Business', 'First'];
        $statuses = ['Scheduled', 'Delayed', 'Cancelled', 'Completed'];

        // Create flights for the next 30 days
        for ($day = 0; $day < 30; $day++) {
            $date = Carbon::now()->addDays($day);

            // Create 10-15 flights per day
            $flightsPerDay = rand(10, 15);

            for ($i = 0; $i < $flightsPerDay; $i++) {
                $airline = $airlines->random();
                $departureAirport = $airports->random();
                $arrivalAirport = $airports->where('id', '!=', $departureAirport->id)->random();

                // Generate departure time
                $departureTime = $date->copy()->setHour(rand(6, 22))->setMinute(rand(0, 59));

                // Calculate flight duration based on distance (simplified)
                $duration = $this->calculateFlightDuration($departureAirport, $arrivalAirport);
                $arrivalTime = $departureTime->copy()->addMinutes($duration);

                // Determine flight type
                $flightType = ($departureAirport->country === $arrivalAirport->country) ? 'Domestic' : 'International';

                $aircraft = $aircraftTypes[array_rand($aircraftTypes)];
                $totalSeats = $this->getAircraftSeats($aircraft);
                $availableSeats = rand(0, $totalSeats);

                // Generate flight number
                $flightNumber = $airline->code . rand(100, 999);

                // Set status based on date
                $status = 'Scheduled';
                if ($date->isPast()) {
                    $status = $statuses[array_rand(['Completed', 'Completed', 'Completed', 'Delayed', 'Cancelled'])];
                } elseif ($date->isToday() && $departureTime->isPast()) {
                    $status = $statuses[array_rand(['Completed', 'Delayed'])];
                }

                Flight::create([
                    'flight_number' => $flightNumber,
                    'airline_id' => $airline->id,
                    'departure_airport_id' => $departureAirport->id,
                    'arrival_airport_id' => $arrivalAirport->id,
                    'departure_time' => $departureTime,
                    'arrival_time' => $arrivalTime,
                    'duration_minutes' => $duration,
                    'aircraft_type' => $aircraft,
                    'total_seats' => $totalSeats,
                    'available_seats' => $availableSeats,
                    'base_price' => $this->calculatePrice($flightType, $duration),
                    'flight_type' => $flightType,
                    'class_type' => $classTypes[array_rand($classTypes)],
                    'status' => $status,
                    'gate' => $this->generateGate(),
                ]);
            }
        }
    }

    private function calculateFlightDuration($departure, $arrival): int
    {
        // Simplified duration calculation based on distance
        $distance = $this->calculateDistance(
            $departure->latitude, $departure->longitude,
            $arrival->latitude, $arrival->longitude
        );

        // Rough estimate: 500 mph average speed + 30 minutes for takeoff/landing
        $hours = ($distance / 500) + 0.5;
        return (int) ($hours * 60);
    }

    private function calculateDistance($lat1, $lon1, $lat2, $lon2): float
    {
        $earthRadius = 3959; // miles

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon/2) * sin($dLon/2);
        $c = 2 * atan2(sqrt($a), sqrt(1-$a));

        return $earthRadius * $c;
    }

    private function getAircraftSeats($aircraft): int
    {
        return match($aircraft) {
            'Boeing 737' => rand(140, 180),
            'Boeing 747' => rand(400, 450),
            'Boeing 777' => rand(300, 350),
            'Boeing 787' => rand(250, 300),
            'Airbus A320' => rand(150, 180),
            'Airbus A330' => rand(250, 300),
            'Airbus A350' => rand(300, 350),
            'Airbus A380' => rand(500, 550),
            default => 180,
        };
    }

    private function calculatePrice($flightType, $duration): float
    {
        $basePrice = $flightType === 'International' ? 500 : 200;
        $durationMultiplier = $duration / 60; // hours

        return round($basePrice + ($durationMultiplier * 50), 2);
    }

    private function generateGate(): string
    {
        $letters = ['A', 'B', 'C', 'D', 'E'];
        return $letters[array_rand($letters)] . rand(1, 30);
    }
}
