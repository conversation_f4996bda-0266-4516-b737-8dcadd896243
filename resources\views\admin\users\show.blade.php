@extends('layouts.admin')

@section('title', 'User Details')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-user me-2"></i>User Details
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.users.index') }}">Users</a></li>
                    <li class="breadcrumb-item active">{{ $user->name }}</li>
                </ol>
            </nav>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-primary">
                <i class="fas fa-edit me-1"></i>Edit User
            </a>
            <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Users
            </a>
        </div>
    </div>

    <div class="row">
        <!-- User Information -->
        <div class="col-xl-8 col-lg-7">
            <!-- Basic Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>Basic Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Full Name</label>
                                <p class="form-control-plaintext">{{ $user->name }}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Email Address</label>
                                <p class="form-control-plaintext">
                                    {{ $user->email }}
                                    @if($user->email_verified_at)
                                        <span class="badge bg-success ms-2">
                                            <i class="fas fa-check me-1"></i>Verified
                                        </span>
                                    @else
                                        <span class="badge bg-warning ms-2">
                                            <i class="fas fa-exclamation-triangle me-1"></i>Unverified
                                        </span>
                                    @endif
                                </p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">User ID</label>
                                <p class="form-control-plaintext">
                                    <code>{{ $user->id }}</code>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Member Since</label>
                                <p class="form-control-plaintext">
                                    {{ $user->created_at->format('F j, Y') }}
                                    <small class="text-muted d-block">{{ $user->created_at->diffForHumans() }}</small>
                                </p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Last Updated</label>
                                <p class="form-control-plaintext">
                                    {{ $user->updated_at->format('F j, Y g:i A') }}
                                    <small class="text-muted d-block">{{ $user->updated_at->diffForHumans() }}</small>
                                </p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Account Status</label>
                                <p class="form-control-plaintext">
                                    @if($user->email_verified_at)
                                        <span class="badge bg-success">
                                            <i class="fas fa-check-circle me-1"></i>Active
                                        </span>
                                    @else
                                        <span class="badge bg-warning">
                                            <i class="fas fa-clock me-1"></i>Pending Verification
                                        </span>
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Roles and Permissions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-user-shield me-2"></i>Roles and Permissions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="fw-bold text-primary">Assigned Roles</h6>
                            @if($user->roles->count() > 0)
                                @foreach($user->roles as $role)
                                    @php
                                        $badgeClass = match($role->name) {
                                            'Admin' => 'bg-danger',
                                            'Manager' => 'bg-primary',
                                            'Staff' => 'bg-info',
                                            'Viewer' => 'bg-secondary',
                                            default => 'bg-secondary'
                                        };
                                    @endphp
                                    <span class="badge {{ $badgeClass }} me-2 mb-2">
                                        <i class="fas fa-user-tag me-1"></i>{{ $role->name }}
                                    </span>
                                @endforeach
                            @else
                                <p class="text-muted">No roles assigned</p>
                            @endif
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold text-success">Permissions</h6>
                            @if($user->getAllPermissions()->count() > 0)
                                <div class="permission-list" style="max-height: 200px; overflow-y: auto;">
                                    @foreach($user->getAllPermissions() as $permission)
                                        <span class="badge bg-light text-dark border me-1 mb-1">
                                            {{ ucwords(str_replace('-', ' ', $permission->name)) }}
                                        </span>
                                    @endforeach
                                </div>
                            @else
                                <p class="text-muted">No permissions assigned</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-history me-2"></i>Recent Activity
                    </h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <!-- Sample activity items -->
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">Account Created</h6>
                                <p class="timeline-text">User account was created and verified</p>
                                <small class="text-muted">{{ $user->created_at->format('M j, Y g:i A') }}</small>
                            </div>
                        </div>
                        @if($user->updated_at != $user->created_at)
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">Profile Updated</h6>
                                <p class="timeline-text">User profile information was updated</p>
                                <small class="text-muted">{{ $user->updated_at->format('M j, Y g:i A') }}</small>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- User Sidebar -->
        <div class="col-xl-4 col-lg-5">
            <!-- Profile Picture -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-camera me-2"></i>Profile Picture
                    </h6>
                </div>
                <div class="card-body text-center">
                    <img src="https://ui-avatars.com/api/?name={{ urlencode($user->name) }}&size=150&background=4e73df&color=ffffff" 
                         class="rounded-circle mb-3" width="150" height="150" alt="Profile Picture">
                    <h5 class="card-title">{{ $user->name }}</h5>
                    <p class="text-muted">{{ $user->email }}</p>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-chart-bar me-2"></i>Quick Stats
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 border-end">
                            <div class="h4 mb-0 text-primary">{{ $user->roles->count() }}</div>
                            <small class="text-muted">Roles</small>
                        </div>
                        <div class="col-6">
                            <div class="h4 mb-0 text-success">{{ $user->getAllPermissions()->count() }}</div>
                            <small class="text-muted">Permissions</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6 border-end">
                            <div class="h4 mb-0 text-info">0</div>
                            <small class="text-muted">Bookings</small>
                        </div>
                        <div class="col-6">
                            <div class="h4 mb-0 text-warning">{{ $user->created_at->diffInDays() }}</div>
                            <small class="text-muted">Days Active</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit me-1"></i>Edit User
                        </a>
                        @if(!$user->email_verified_at)
                        <button class="btn btn-success btn-sm" onclick="verifyUser({{ $user->id }})">
                            <i class="fas fa-check me-1"></i>Verify Email
                        </button>
                        @endif
                        <button class="btn btn-info btn-sm" onclick="resetPassword({{ $user->id }})">
                            <i class="fas fa-key me-1"></i>Reset Password
                        </button>
                        <button class="btn btn-warning btn-sm" onclick="sendNotification({{ $user->id }})">
                            <i class="fas fa-bell me-1"></i>Send Notification
                        </button>
                        @if($user->id !== auth()->id())
                        <button class="btn btn-danger btn-sm" onclick="deleteUser({{ $user->id }})">
                            <i class="fas fa-trash me-1"></i>Delete User
                        </button>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Security Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-danger">
                        <i class="fas fa-shield-alt me-2"></i>Security Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Two-Factor Auth:</strong><br>
                        <span class="badge bg-secondary">
                            <i class="fas fa-times me-1"></i>Disabled
                        </span>
                    </div>
                    <div class="mb-3">
                        <strong>Last Login:</strong><br>
                        <small class="text-muted">No login data available</small>
                    </div>
                    <div class="mb-3">
                        <strong>Login Attempts:</strong><br>
                        <span class="badge bg-success">0 Failed</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@push('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 17px;
    width: 2px;
    height: calc(100% + 5px);
    background-color: #dee2e6;
}

.timeline-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 5px;
}

.timeline-text {
    font-size: 13px;
    margin-bottom: 5px;
    color: #6c757d;
}
</style>
@endpush

@push('scripts')
<script>
function verifyUser(userId) {
    if (confirm('Are you sure you want to verify this user\'s email?')) {
        // Add AJAX call to verify user
        alert('Email verification functionality would be implemented here.');
    }
}

function resetPassword(userId) {
    if (confirm('Are you sure you want to reset this user\'s password?')) {
        // Add AJAX call to reset password
        alert('Password reset functionality would be implemented here.');
    }
}

function sendNotification(userId) {
    // Add notification functionality
    alert('Send notification functionality would be implemented here.');
}

function deleteUser(userId) {
    if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
        // Add AJAX call to delete user
        alert('User deletion functionality would be implemented here.');
    }
}
</script>
@endpush
