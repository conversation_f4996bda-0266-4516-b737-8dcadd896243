<?php

namespace App\Services;

use App\Models\Airport;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class AirportAutocompleteService
{
    protected FlightApiService $flightApiService;
    protected int $cacheTimeout = 3600; // 1 hour
    protected int $apiTimeout = 3; // 3 seconds
    protected int $maxResults = 10;

    public function __construct(FlightApiService $flightApiService)
    {
        $this->flightApiService = $flightApiService;
    }

    /**
     * Search airports with multiple data sources and fallback
     */
    public function searchAirports(string $query, int $limit = 10): array
    {
        $query = trim($query);
        
        if (strlen($query) < 1) {
            return [];
        }

        $cacheKey = "airport_search:" . md5(strtolower($query)) . ":{$limit}";
        
        return Cache::remember($cacheKey, $this->cacheTimeout, function () use ($query, $limit) {
            // Priority 1: Try external APIs
            $apiResults = $this->searchFromApis($query, $limit);
            
            if (!empty($apiResults)) {
                // Store API results in database for future use
                $this->storeApiResults($apiResults);
                return $this->formatResults($apiResults);
            }

            // Priority 2: Search local database
            $dbResults = $this->searchFromDatabase($query, $limit);
            
            if (!empty($dbResults)) {
                return $this->formatResults($dbResults);
            }

            // Priority 3: Emergency static list
            return $this->searchFromStaticList($query, $limit);
        });
    }

    /**
     * Search from external APIs
     */
    protected function searchFromApis(string $query, int $limit): array
    {
        try {
            $startTime = microtime(true);
            
            // Use FlightApiService to search airports
            $results = $this->flightApiService->searchAirports($query);
            
            $responseTime = microtime(true) - $startTime;
            
            // Log API performance
            Log::info('Airport API search', [
                'query' => $query,
                'results_count' => count($results),
                'response_time' => round($responseTime * 1000, 2) . 'ms'
            ]);

            return array_slice($results, 0, $limit);

        } catch (\Exception $e) {
            Log::warning('Airport API search failed', [
                'query' => $query,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Search from local database
     */
    protected function searchFromDatabase(string $query, int $limit): array
    {
        try {
            $airports = Airport::search($query, $limit);
            
            return $airports->map(function ($airport) {
                return [
                    'code' => $airport->code,
                    'name' => $airport->name,
                    'city' => $airport->city,
                    'country' => $airport->country,
                    'provider' => 'database'
                ];
            })->toArray();

        } catch (\Exception $e) {
            Log::error('Database airport search failed', [
                'query' => $query,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Emergency static airport list
     */
    protected function searchFromStaticList(string $query, int $limit): array
    {
        $staticAirports = [
            ['code' => 'JFK', 'name' => 'John F. Kennedy International Airport', 'city' => 'New York', 'country' => 'United States'],
            ['code' => 'LAX', 'name' => 'Los Angeles International Airport', 'city' => 'Los Angeles', 'country' => 'United States'],
            ['code' => 'LHR', 'name' => 'London Heathrow Airport', 'city' => 'London', 'country' => 'United Kingdom'],
            ['code' => 'CDG', 'name' => 'Charles de Gaulle Airport', 'city' => 'Paris', 'country' => 'France'],
            ['code' => 'DXB', 'name' => 'Dubai International Airport', 'city' => 'Dubai', 'country' => 'United Arab Emirates'],
            ['code' => 'NRT', 'name' => 'Narita International Airport', 'city' => 'Tokyo', 'country' => 'Japan'],
            ['code' => 'SYD', 'name' => 'Sydney Kingsford Smith Airport', 'city' => 'Sydney', 'country' => 'Australia'],
            ['code' => 'SIN', 'name' => 'Singapore Changi Airport', 'city' => 'Singapore', 'country' => 'Singapore'],
            ['code' => 'FRA', 'name' => 'Frankfurt Airport', 'city' => 'Frankfurt', 'country' => 'Germany'],
            ['code' => 'AMS', 'name' => 'Amsterdam Airport Schiphol', 'city' => 'Amsterdam', 'country' => 'Netherlands'],
            ['code' => 'ICN', 'name' => 'Incheon International Airport', 'city' => 'Seoul', 'country' => 'South Korea'],
            ['code' => 'HKG', 'name' => 'Hong Kong International Airport', 'city' => 'Hong Kong', 'country' => 'Hong Kong'],
            ['code' => 'BOM', 'name' => 'Chhatrapati Shivaji International Airport', 'city' => 'Mumbai', 'country' => 'India'],
            ['code' => 'DEL', 'name' => 'Indira Gandhi International Airport', 'city' => 'Delhi', 'country' => 'India'],
            ['code' => 'MEL', 'name' => 'Melbourne Airport', 'city' => 'Melbourne', 'country' => 'Australia'],
            ['code' => 'YYZ', 'name' => 'Toronto Pearson International Airport', 'city' => 'Toronto', 'country' => 'Canada'],
            ['code' => 'ORD', 'name' => 'O\'Hare International Airport', 'city' => 'Chicago', 'country' => 'United States'],
            ['code' => 'ATL', 'name' => 'Hartsfield-Jackson Atlanta International Airport', 'city' => 'Atlanta', 'country' => 'United States'],
            ['code' => 'DFW', 'name' => 'Dallas/Fort Worth International Airport', 'city' => 'Dallas', 'country' => 'United States'],
            ['code' => 'DEN', 'name' => 'Denver International Airport', 'city' => 'Denver', 'country' => 'United States'],
        ];

        $query = strtolower($query);
        $results = [];

        foreach ($staticAirports as $airport) {
            if (
                stripos($airport['code'], $query) !== false ||
                stripos($airport['name'], $query) !== false ||
                stripos($airport['city'], $query) !== false ||
                stripos($airport['country'], $query) !== false
            ) {
                $airport['provider'] = 'static';
                $results[] = $airport;
                
                if (count($results) >= $limit) {
                    break;
                }
            }
        }

        return $results;
    }

    /**
     * Format results for frontend consumption
     */
    protected function formatResults(array $results): array
    {
        return array_map(function ($airport) {
            return [
                'code' => $airport['code'] ?? '',
                'name' => $airport['name'] ?? '',
                'city' => $airport['city'] ?? '',
                'country' => $airport['country'] ?? '',
                'display' => ($airport['code'] ?? '') . ' - ' . ($airport['name'] ?? ''),
                'subtitle' => ($airport['city'] ?? '') . ', ' . ($airport['country'] ?? ''),
                'provider' => $airport['provider'] ?? 'unknown'
            ];
        }, $results);
    }

    /**
     * Store API results in database for future use
     */
    protected function storeApiResults(array $results): void
    {
        try {
            foreach ($results as $result) {
                if (isset($result['code']) && !empty($result['code'])) {
                    Airport::updateOrCreate(
                        ['code' => $result['code']],
                        [
                            'name' => $result['name'] ?? '',
                            'city' => $result['city'] ?? '',
                            'country' => $result['country'] ?? '',
                            'is_active' => true
                        ]
                    );
                }
            }
        } catch (\Exception $e) {
            Log::error('Failed to store API airport results', [
                'error' => $e->getMessage(),
                'results_count' => count($results)
            ]);
        }
    }

    /**
     * Clear airport search cache
     */
    public function clearCache(): void
    {
        $pattern = 'airport_search:*';
        $keys = Cache::getRedis()->keys($pattern);
        
        if (!empty($keys)) {
            Cache::getRedis()->del($keys);
        }
    }

    /**
     * Get search statistics
     */
    public function getSearchStats(): array
    {
        return [
            'total_airports' => Airport::count(),
            'active_airports' => Airport::active()->count(),
            'cache_keys' => $this->getCacheKeyCount(),
            'last_api_call' => Cache::get('last_airport_api_call', 'Never')
        ];
    }

    /**
     * Get cache key count
     */
    protected function getCacheKeyCount(): int
    {
        try {
            $keys = Cache::getRedis()->keys('airport_search:*');
            return count($keys);
        } catch (\Exception $e) {
            return 0;
        }
    }
}
